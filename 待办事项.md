# 第三章图表补充工作待办事项

本文档列出了为《第3章 基于环境约束的轨迹生成方法》补充图表和实验结果所需完成的工作事项。按照优先级和章节顺序排列。

## 1. 基础环境数据可视化（3.2节）

- [ ] **使用 `visualize_environment.py` 生成环境数据可视化图**
  - 确认脚本使用的是最终处理后的数据（UTM 30N坐标系，30米分辨率）
  - 调整 DPI 参数至少为 300，确保图像清晰度
  - 生成四幅子图：DEM（高程）、坡度、坡向、地表覆盖类型
  - 为每幅图添加颜色条、坐标轴标签和比例尺
  - 保存为高质量图片用于插入论文

- [ ] **（可选）使用 `visualize_dem.py` 生成高程数据的三维可视化**
  - 调整视角和渲染参数，确保地形特征清晰
  - 考虑在三维图上叠加部分规划路径，直观展示地形与路径的关系

## 2. 不同场景代价地图可视化（3.3.1 & 3.3.2节）

- [ ] **修改或创建新脚本，可视化四种场景的代价地图**
  - 基于 `simple_trajectories_generator/cost_configs.py` 中的配置
  - 使用相同区域和尺度生成四种场景（Standard、Stealth Priority、High Mobility、Mountain Special）的代价地图
  - 使用热力图形式（如 'viridis' 或 'plasma' 颜色映射）展示代价值
  - 添加颜色条和标题，确保四幅图使用统一的颜色范围便于对比
  - 可考虑在图上标记特定感兴趣区域（如极高代价区域）

## 3. 不同场景路径规划结果对比（3.3.3 & 3.3.4节）

- [ ] **运行 `simple_trajectories_generator/visualize_paths.py` 生成路径对比图**
  - 选取2-3组有代表性的起点-终点对
  - 在相同底图（如DEM或地表覆盖）上展示四种场景下规划的路径
  - 使用不同颜色或线型区分不同场景的路径
  - 添加清晰图例，标明每种颜色对应的场景
  - 可在图上标注关键点（如特殊地形区域、路径分歧点）
  - 确保背景图选择合适，能突出不同路径的差异原因

## 4. 时间窗口影响分析图表（3.5.1节，图3-8）

- [ ] **运行 `analyze_r2_distribution.py` 生成时间窗口影响分析图**
  - 生成不同时间窗口（1秒至60秒）下速度-坡度关系R²值的箱线图
  - 确保X轴表示时间窗口大小，Y轴表示R²值
  - 强调15-60秒窗口的优势
  - 添加统计显著性标记（如果有）
  - 考虑添加一个小表格，列出各窗口大小的具体R²数值
  - 检查图3-8的标题和注释是否准确反映内容

## 5. 不同地类速度-坡度关系图（3.5.1节，图3-9）

- [ ] **运行 `analyze_speed_environment.py` 生成地类速度关系图**
  - 处理OORD数据集，按主要地类（林地、灌木地、水体等）分组
  - 为每个主要地类生成速度-坡度散点图
  - 添加线性回归线，并在图上标注回归方程和R²值
  - 确保图例清晰，包含地类名称和样本量
  - 与表3-1的数据保持一致
  - 检查图3-9的标题和注释是否准确反映内容

## 6. 残差分布分析图表（3.5.1节，图3-10）

- [ ] **运行 `analyze_residuals.py` 生成残差分布图**
  - 计算并按地类分组展示速度模型残差
  - 绘制直方图展示残差分布
  - 添加拟合的概率分布曲线（如林地的拉普拉斯分布、灌木地的t分布）
  - 在图例中标注分布类型和关键参数
  - 确保视觉表现清晰，易于理解不同地类残差的分布差异
  - 检查图3-10的标题和注释是否准确反映内容

## 7. 生成轨迹可视化与分析（3.5.2节，图3-11,3-12,3-13）

- [ ] **速度时间序列图（图3-11）**
  - 运行 `visualize_trajectory_speeds.py` 处理生成的轨迹数据
  - 选择一条或几条典型轨迹，展示速度随时间变化
  - 在图上标注环境变化点（如地类转换、坡度变化点）
  - 添加平均速度线、最大最小速度范围等辅助信息
  - 确保时间轴单位和范围合适，速度轴单位明确

- [ ] **速度与环境特征关系图（图3-12）**
  - 使用或修改 `compare_simulated_trajectories.py`
  - 分析生成轨迹中速度与坡度、地类等环境因素的关系
  - 使用散点图展示，可用不同颜色表示不同地类
  - 添加趋势线以突出关系模式
  - 确保与图3-9的表达方式一致，便于对比真实与生成数据

- [ ] **曲率-速度关系图（图3-13）**
  - 使用 `analyze_trajectory_correlations.py` 处理生成轨迹数据
  - 计算并绘制轨迹点的局部曲率与速度关系
  - 添加拟合曲线，标注相关系数和拟合函数
  - 可考虑按地类或场景分组展示，以验证在不同条件下曲率对速度的影响

## 8. 真实与生成轨迹对比（3.5.3节，图3-14）

- [ ] **真实与生成轨迹速度响应模式对比**
  - 结合真实数据（OORD）和生成轨迹的分析结果
  - 创建对比图展示两者在速度-坡度关系上的相似性
  - 可以使用并排的散点图+回归线，或比较关键参数（如回归斜率）的条形图
  - 确保清晰标注哪些是真实数据，哪些是生成数据
  - 重点展示响应趋势的相似性，而非绝对值的匹配

## 9. 其他补充与优化

- [ ] **优化表格**
  - 检查表3-1和表3-2的数据是否完整、准确
  - 确保表格格式统一、美观
  - 考虑添加统计显著性标记

- [ ] **添加方法局限性讨论**
  - 在3.5后添加一个新小节"3.5.4 方法局限性与未来改进方向"
  - 基于实验结果讨论当前方法的局限性
  - 提出合理的改进方向

- [ ] **统一引用格式**
  - 检查全文参考文献引用格式，统一为一种风格
  - 确保每个引用都对应参考文献列表中的条目

## 实施建议

1. **从简单任务开始**：先完成已有脚本可直接生成的图表（如环境可视化）
2. **按章节顺序推进**：遵循从3.2到3.5的顺序，确保叙述连贯
3. **注意图表质量**：
   - 所有图表都应使用高分辨率（至少300 DPI）
   - 字体大小合适，图例清晰
   - 坐标轴标签和单位明确
   - 颜色选择合理，考虑色盲友好
4. **保存原始数据**：对于每个图表，保存生成它的原始数据和脚本参数，以便需要时调整

完成以上工作后，第三章的实验结果将更加充实、直观，大大增强论文的科学性和可读性。 