#!/bin/bash

# 后台持续下载脚本
# 这个脚本会无限循环，直到所有必需的DEM文件都下载完毕。

DEM_STORAGE_DIR="geospatial_trajectory_generator/data/raw/DEM"
BASE_URL="http://viewfinderpanoramas.org/dem3/"

# --- 根据我们LULC数据实际覆盖范围确定的DEM列表 ---
# 这是根据对LULC数据的精确分析得出的，与我们最初的研究区域无关
REQUIRED_ZIPS=(
    "N29.zip" "N30.zip" "N31.zip" "N32.zip" "N33.zip" "N34.zip" "N35.zip" 
    "N44.zip" "N45.zip" "N46.zip" "N47.zip" "N48.zip" "N49.zip" "N50.zip"
    "N54.zip" "N55.zip" "N56.zip" "N57.zip" "N58.zip" "N59.zip" "N60.zip"
)

echo "--- 启动后台DEM数据下载器 ---"
echo "目标目录: $DEM_STORAGE_DIR"
echo "将持续尝试下载，直到所有文件都存在且完整。"

while true; do
    
    all_completed=true
    
    for zip_name in "${REQUIRED_ZIPS[@]}"; do
        dest_path="$DEM_STORAGE_DIR/$zip_name"
        
        # 检查文件是否存在且大小 > 1KB (一个简单的完整性检查)
        if [ -f "$dest_path" ] && [ $(stat -c%s "$dest_path") -gt 1024 ]; then
            continue # 文件已存在且看起来完整，跳过
        fi
        
        # 如果文件不完整或不存在，则标记为未完成，并尝试下载
        all_completed=false
        echo "$(date): 正在尝试下载缺失或不完整的DEM文件: $zip_name"
        
        wget -c -T 60 -O "$dest_path" "$BASE_URL$zip_name"
        
        if [ $? -eq 0 ]; then
            echo "$(date): 成功下载/续传 $zip_name"
        else
            echo "$(date): 下载 $zip_name 失败，将在下一轮重试。"
        fi
        
        # 每次下载之间间隔一段时间，避免过于频繁
        sleep 10
    done
    
    if [ "$all_completed" = true ]; then
        echo "$(date): 所有必需的DEM文件已下载完成。退出后台下载任务。"
        break
    fi
    
    # 在完成一轮检查/下载后，等待较长时间再开始下一轮
    echo "$(date): 已完成一轮检查，将在5分钟后开始下一轮..."
    sleep 300
    
done 