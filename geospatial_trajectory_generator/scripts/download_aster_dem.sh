#!/bin/bash

# This script downloads ASTER GDEM v3 files based on a list.
# It uses a Bearer Token and session cookies for robust authentication.

# --- Configuration ---
# PASTE YOUR EARTHDATA LOGIN BEARER TOKEN HERE
AUTH_TOKEN="eyJ0eXAiOiJKV1QiLCJvcmlnaW4iOiJFYXJ0aGRhdGEgTG9naW4iLCJzaWciOiJlZGxqd3RwdWJrZXlfb3BzIiwiYWxnIjoiUlMyNTYifQ.***************************************************************************************************************************************************************************************************************************.RpaUDrmr8vehMPyZB5bPMudE9QPPYBCgGr_UZxLEdWra2NAzM_06u2Unbd4Z_UkAVWfF12vc8exhTiWfhMaxThTMzTlnWpFBvcww-uNf1ShzCClgHWITiROyDv9kr2oArVjzW2OSRWDb8KfISJeUhh4SltYsWe-34NJfv7WktXwKa-4D2L4HGNskQr9C9RuAGyCTZKlA5DRuuARvAiGDZvOU9Cfd-deBbzzoHZlV9cbbD8pJfze01SVuhX16dZpQZDAihzUn4STBy_xD7G5nSsXByiAmREtoJN0cVO9QkZ-mohvt-QNWM7moWU9fAj3jUkW8BhKlLqR3glJskpvJew"

# The base URL for ASTER GDEM v3 data downloads.
BASE_URL="https://e4ftl01.cr.usgs.gov/ASTT/ASTGTM.003/2019.08.05"

# The list of files to download.
FILE_LIST="geospatial_trajectory_generator/scripts/dem_aster_download_list.txt"

# The directory where files will be saved.
DOWNLOAD_DIR="geospatial_trajectory_generator/data/raw/dem_aster"

# Cookie file for maintaining session state.
COOKIE_FILE="cookies.txt"

# Log file for recording download progress.
LOG_DIR="geospatial_trajectory_generator/logs"
LOG_FILE="${LOG_DIR}/aster_dem_downloader.log"

# --- Script Execution ---

# Ensure the log directory exists
mkdir -p "$LOG_DIR"

# Check if the file list exists
if [ ! -f "$FILE_LIST" ]; then
    echo "ERROR: Download list not found at ${FILE_LIST}" | tee -a "$LOG_FILE"
    exit 1
fi

# Get the total number of files for the progress indicator
TOTAL_FILES=$(wc -l < "$FILE_LIST")
CURRENT_FILE=0

echo "Starting ASTER GDEM download with Bearer Token and Cookies..." | tee -a "$LOG_FILE"
echo "Found ${TOTAL_FILES} files to download." | tee -a "$LOG_FILE"
echo "--------------------------------------------------" | tee -a "$LOG_FILE"

# Read each line from the file list
while IFS= read -r FILENAME; do
    
    CURRENT_FILE=$((CURRENT_FILE + 1))
    
    # Construct the full URL for the file, including the date-based directory
    FULL_URL="${BASE_URL}/${FILENAME}"
    
    # Define the local path for the downloaded file
    LOCAL_PATH="${DOWNLOAD_DIR}/${FILENAME}"

    # Check if the file already exists to avoid re-downloading
    if [ -f "$LOCAL_PATH" ]; then
        echo "[$CURRENT_FILE/$TOTAL_FILES] SKIPPING: ${FILENAME} already exists." | tee -a "$LOG_FILE"
        continue
    fi

    echo "[$CURRENT_FILE/$TOTAL_FILES] Downloading: ${FILENAME}..." | tee -a "$LOG_FILE"
    
    # Use curl with the Bearer Token and Cookie Jar for authentication
    # -H, --header: Pass a custom header to the server.
    # -L, --location: Follows redirects.
    # -o, --output: Specifies the output file.
    # -f, --fail: Makes curl fail silently on server errors (HTTP 4xx/5xx).
    # -b, --cookie: Path to the cookie file to read from.
    # -c, --cookie-jar: Path to the cookie file to write to after completion.
    curl -H "Authorization: Bearer ${AUTH_TOKEN}" -L -f -o "$LOCAL_PATH" -b "${COOKIE_FILE}" -c "${COOKIE_FILE}" "$FULL_URL"
    
    # Check the exit code of curl to see if the download was successful
    if [ $? -eq 0 ]; then
        echo "[$CURRENT_FILE/$TOTAL_FILES] SUCCESS: Saved to ${LOCAL_PATH}" | tee -a "$LOG_FILE"
    else
        # Check the HTTP status code to distinguish between 401 and 404
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer ${AUTH_TOKEN}" -L -b "${COOKIE_FILE}" -c "${COOKIE_FILE}" "$FULL_URL")
        if [ "$HTTP_CODE" -eq 404 ]; then
            echo "[$CURRENT_FILE/$TOTAL_FILES] INFO: ${FILENAME} not found on server (404). This can be expected for water-only tiles. Skipping." | tee -a "$LOG_FILE"
        else
            echo "[$CURRENT_FILE/$TOTAL_FILES] FAILED: Could not download ${FULL_URL} (Error code: $HTTP_CODE)" | tee -a "$LOG_FILE"
        fi
        # Optional: remove partially downloaded file on failure
        rm -f "$LOCAL_PATH"
    fi

done < "$FILE_LIST"

echo "--------------------------------------------------" | tee -a "$LOG_FILE"
echo "Download process finished." | tee -a "$LOG_FILE"
echo "Please check the log file for details: ${LOG_FILE}" 