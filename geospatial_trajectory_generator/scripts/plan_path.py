
import numpy as np
import rasterio
import heapq
import os
import logging
from tqdm import tqdm

import config  # Assuming a config.py file with necessary paths

# --- 日志设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AStarPlanner:
    """
    使用A*算法在地理成本地图上规划路径。
    """

    def __init__(self, cost_map, resolution):
        """
        初始化A*规划器。
        :param cost_map: 一个2D numpy数组，其中每个像元的值代表通过该像元的成本。
        :param resolution: 地图的分辨率（每个像元的实际地理距离）。
        """
        self.cost_map = cost_map
        self.resolution = resolution
        self.height, self.width = cost_map.shape
        self.motions = self._get_motions()

    def _get_motions(self):
        """定义8个方向的移动及其成本（对角线移动成本更高）。"""
        return [
            (0, 1, 1), (1, 0, 1), (0, -1, 1), (-1, 0, 1),
            (1, 1, np.sqrt(2)), (1, -1, np.sqrt(2)),
            (-1, 1, np.sqrt(2)), (-1, -1, np.sqrt(2))
        ]

    def _is_valid(self, x, y):
        """检查坐标是否在地图范围内。"""
        return 0 <= x < self.width and 0 <= y < self.height

    def _heuristic(self, p1, p2):
        """计算两个点之间的曼哈顿距离作为启发式函数。"""
        return (abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])) * self.resolution

    def plan(self, start_px, end_px):
        """
        执行A*寻路。
        :param start_px: 起点的像素坐标 (x, y)。
        :param end_px: 终点的像素坐标 (x, y)。
        :return: 路径点（像素坐标列表）或None。
        """
        if not self._is_valid(*start_px) or not self._is_valid(*end_px):
            logging.error("起点或终点坐标无效。")
            return None

        open_set = []
        heapq.heappush(open_set, (0, start_px))  # (f_cost, (x, y))
        came_from = {}
        g_cost = { (x,y): float('inf') for x in range(self.width) for y in range(self.height) }
        g_cost[start_px] = 0
        f_cost = { (x,y): float('inf') for x in range(self.width) for y in range(self.height) }
        f_cost[start_px] = self._heuristic(start_px, end_px)

        pbar = tqdm(total=self.width * self.height, desc="A* Planning Progress")

        while open_set:
            pbar.update(1)
            current_f, current_px = heapq.heappop(open_set)

            if current_px == end_px:
                pbar.close()
                logging.info("已找到路径！正在回溯...")
                return self._reconstruct_path(came_from, current_px)

            for dx, dy, motion_cost in self.motions:
                neighbor_px = (current_px[0] + dx, current_px[1] + dy)

                if not self._is_valid(*neighbor_px):
                    continue
                
                # 结合地形移动成本和基础移动成本
                terrain_cost = self.cost_map[neighbor_px[1], neighbor_px[0]]
                tentative_g_cost = g_cost[current_px] + motion_cost * self.resolution * terrain_cost

                if tentative_g_cost < g_cost[neighbor_px]:
                    came_from[neighbor_px] = current_px
                    g_cost[neighbor_px] = tentative_g_cost
                    f_cost[neighbor_px] = tentative_g_cost + self._heuristic(neighbor_px, end_px)
                    heapq.heappush(open_set, (f_cost[neighbor_px], neighbor_px))
        
        pbar.close()
        logging.warning("未能找到从起点到终点的路径。")
        return None

    def _reconstruct_path(self, came_from, current):
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        return path[::-1] # 返回从起点到终点的路径

def create_cost_map(dem_path, lulc_path):
    """
    根据DEM和LULC数据创建成本地图。
    :return: 成本地图 (numpy array)。
    """
    # 这是一个简化的示例，可以根据需要扩展
    with rasterio.open(dem_path) as dem_src:
        elevation = dem_src.read(1)
        # 计算坡度作为成本的一部分
        slope = np.gradient(elevation)
        slope_cost = np.sqrt(slope[0]**2 + slope[1]**2)
    
    with rasterio.open(lulc_path) as lulc_src:
        lulc = lulc_src.read(1)
        # 定义不同地表类型的成本
        lulc_cost = np.ones_like(lulc, dtype=np.float32)
        lulc_cost[lulc == 10] = 100  # 水域成本极高
        lulc_cost[lulc == 50] = 5    # 建筑用地成本较高
        lulc_cost[lulc == 80] = 10   # 森林成本高
    
    # 标准化并组合成本
    normalized_slope_cost = (slope_cost - np.min(slope_cost)) / (np.max(slope_cost) - np.min(slope_cost))
    combined_cost = 1 + normalized_slope_cost * 5 + lulc_cost # 基础成本为1，加上坡度和地表类型成本
    
    return combined_cost

if __name__ == '__main__':
    # --- 这是一个如何使用该类的示例 ---
    # 1. 确保DEM和LULC数据已经准备好并且是合并后的单个文件
    #    (这需要额外的预处理步骤)
    MERGED_DEM_PATH = "data/DEM_processed/merged_dem.tif"
    MERGED_LULC_PATH = "data/LULC_processed/merged_lulc.tif"

    if not os.path.exists(MERGED_DEM_PATH) or not os.path.exists(MERGED_LULC_PATH):
        logging.error("未找到合并后的DEM或LULC文件。请先运行数据预处理脚本。")
        # exit() # 在实际使用中应该退出
    
    logging.info("正在创建成本地图...")
    # cost_map = create_cost_map(MERGED_DEM_PATH, MERGED_LULC_PATH)
    # print("成本地图创建完成。")
    
    # # 假设我们从某个栅格数据获取分辨率
    # with rasterio.open(MERGED_DEM_PATH) as src:
    #     resolution = src.res[0]
    #     transform = src.transform

    # planner = AStarPlanner(cost_map, resolution)

    # # 定义起点和终点（像素坐标）
    # start_xy_px = (50, 50)
    # end_xy_px = (800, 800)

    # logging.info(f"正在从 {start_xy_px} 规划至 {end_xy_px}...")
    # path_px = planner.plan(start_xy_px, end_xy_px)
    
    # if path:
    #     # 将像素坐标转换回地理坐标
    #     path_geo = [rasterio.transform.xy(transform, p[1], p[0]) for p in path_px]
        
    #     # 保存路径
    #     path_df = pd.DataFrame(path_geo, columns=['x', 'y'])
    #     path_df.to_csv("planned_path.csv", index=False)
    #     logging.info("路径已规划并保存至 planned_path.csv")

    print("\n路径规划脚本框架已创建。")
    print("下一步需要完成数据预处理，以生成合并的DEM和LULC文件。") 