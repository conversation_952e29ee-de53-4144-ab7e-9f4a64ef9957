
import ee
import os

# --- Earth Engine Authentication and Initialization ---
# We will try to initialize first. If it fails, we will trigger the authentication flow.
try:
    ee.Initialize()
except ee.EEException:
    print("Earth Engine not authenticated. Starting authentication flow...")
    # Force the notebook authentication flow, which does not depend on gcloud.
    ee.Authenticate(auth_mode='notebook')
    ee.Initialize()

# Define the regions of interest (bounding boxes).
# Using the same definitions as before.
REGIONS = {
    "Scottish_Highlands": {
        "lon_range": (-6.2, -3.5),
        "lat_range": (56.0, 58.6)
    },
    # Add other regions here if needed
    # "Israel_Palestine": { ... }
}

# Define the output directory for the satellite images
output_dir = "data/satellite_images"
os.makedirs(output_dir, exist_ok=True)

def download_region_imagery(region_name, lon_range, lat_range):
    """
    Downloads the best available Sentinel-2 satellite image for a given region.
    """
    print(f"Processing region: {region_name}")

    # Define the Area of Interest (AOI) as a ee.Geometry.Rectangle
    aoi = ee.Geometry.Rectangle(
        coords=[lon_range[0], lat_range[0], lon_range[1], lat_range[1]],
        proj='EPSG:4326',
        geodesic=False
    )

    # Search for Sentinel-2 imagery.
    # We will search for Level-2A (Surface Reflectance) data.
    # Filter by date to get recent imagery and filter by cloud cover.
    collection = (ee.ImageCollection('COPERNICUS/S2_SR')
                  .filterBounds(aoi)
                  .filterDate('2023-01-01', '2023-12-31')
                  .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 10))
                  .sort('CLOUDY_PIXEL_PERCENTAGE'))

    # Get the least cloudy image from the collection.
    image = collection.first()

    if image.getInfo() is None:
        print(f"No suitable image found for {region_name} with less than 10% cloud cover in 2023.")
        return

    print(f"Found image for {region_name}. Image ID: {image.id().getInfo()}")

    # Define visualization parameters for a true-color image.
    vis_params = {
        'bands': ['B4', 'B3', 'B2'],  # Red, Green, Blue
        'min': 0,
        'max': 3000,
        'gamma': 1.4,
    }

    # Create an RGB image from the selected bands.
    rgb_image = image.visualize(**vis_params)

    # Define the download URL.
    # We specify the scale (resolution in meters) and the region.
    url = rgb_image.getDownloadURL({
        'scale': 30,  # 30 meters per pixel
        'crs': 'EPSG:4326',
        'region': aoi.toGeoJSONString()
    })

    print(f"Generating download URL for {region_name}...")
    print("Please be patient, this can take a moment.")
    print(f"Download URL: {url}")
    
    # Note: This script will print the download URL.
    # You will need to copy this URL into your browser to download the file.
    # Automating the download of the ZIP from this URL can be complex due to authentication tokens.
    # For now, we will manually download.

if __name__ == "__main__":
    region_key = "Scottish_Highlands"
    region_data = REGIONS[region_key]
    download_region_imagery(
        region_name=region_key,
        lon_range=region_data["lon_range"],
        lat_range=region_data["lat_range"]
    ) 