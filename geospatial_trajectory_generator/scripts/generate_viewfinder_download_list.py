 
import os

def convert_to_viewfinder_format(lat, lon):
    """
    Converts latitude and longitude to the Viewfinder Panoramas file naming format.
    Example: lat=34, lon=112 -> N34E112
    """
    lat_str = f"N{abs(int(lat))}" if lat >= 0 else f"S{abs(int(lat))}"
    lon_str = f"E{abs(int(lon)):03d}" if lon >= 0 else f"W{abs(int(lon)):03d}"
    
    # The website uses lowercase for the hemisphere letters in the filename
    return f"{lat_str[0].lower()}{lat_str[1:]}{lon_str[0].lower()}{lon_str[1:]}.hgt"

def main():
    """
    Reads a list of ASTER DEM tiles, extracts the coordinates,
    and generates a download list for Viewfinder Panoramas.
    """
    input_file = 'dem_aster_download_list.txt'
    output_file = 'dem_viewfinder_download_list.txt'
    
    # The base URL for the downloads seems to be structured by continent/region.
    # From the coverage map, it looks like our area of interest is in Asia.
    # The direct download links seem to be in the format:
    # http://viewfinderpanoramas.org/dem3/N34.zip
    # Inside N34.zip would be files like n34e112.hgt, n34e113.hgt etc.
    # So we need to generate the .zip file names first.
    
    zip_files = set()

    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found in the current directory.")
        print("Please run this script from 'geospatial_trajectory_generator/scripts/'.")
        return

    with open(input_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            # Extract coordinates from the ASTER filename like ASTGTMV003_N34E112
            try:
                parts = line.split('_')
                coords_part = parts[1]
                lat_part = coords_part.split('E')[0] if 'E' in coords_part else coords_part.split('W')[0]
                
                lat_val = int(lat_part[1:])
                if lat_part[0] == 'S':
                    lat_val = -lat_val
                
                # We just need the latitude to determine the zip file name, e.g., N34.zip
                zip_name = f"{'N' if lat_val >= 0 else 'S'}{abs(lat_val)}.zip"
                zip_files.add(zip_name)

            except (IndexError, ValueError) as e:
                print(f"Skipping malformed line: {line} -> {e}")
                continue

    with open(output_file, 'w') as f_out:
        for zip_file in sorted(list(zip_files)):
            f_out.write(f"http://viewfinderpanoramas.org/dem3/{zip_file}\n")
            
    print(f"Successfully generated '{output_file}' with {len(zip_files)} unique zip file URLs.")
    print("You can now use a download manager or a script with this list.")

if __name__ == "__main__":
    main() 