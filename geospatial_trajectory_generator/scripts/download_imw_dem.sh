#!/bin/bash

# 下载列表文件路径
DOWNLOAD_LIST="geospatial_trajectory_generator/data/dem_imw_download_list.txt"

# 下载目标目录
DOWNLOAD_DIR="geospatial_trajectory_generator/data/raw/dem_imw"

# 日志文件路径
LOG_FILE="geospatial_trajectory_generator/logs/dem_imw_download.log"

# 创建目标目录和日志目录
mkdir -p "$DOWNLOAD_DIR"
mkdir -p "$(dirname "$LOG_FILE")"

# 使用wget进行后台下载
# -b: 后台执行
# -c: 断点续传
# -i: 从文件读取URL
# -P: 指定下载目录
# -a: 将日志附加到文件
wget -b -c -i "$DOWNLOAD_LIST" -P "$DOWNLOAD_DIR" -a "$LOG_FILE"

echo "DEM数据后台下载已启动。"
echo "您可以使用以下命令查看下载日志："
echo "tail -f $LOG_FILE"
echo "您可以使用以下命令查看已下载的文件："
echo "ls -lh $DOWNLOAD_DIR" 