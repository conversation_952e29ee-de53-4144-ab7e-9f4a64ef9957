import math
import os

def get_imw_tile(lat, lon):
    """
    Calculates the International Map of the World (IMW) tile reference for the northern hemisphere.
    """
    if not (0 <= lat < 84 and -180 <= lon < 180):
        return None
    
    # Latitude band (4 degrees per band, starting from 'A' at the equator)
    lat_band_index = math.floor(lat / 4)
    lat_char = chr(ord('A') + lat_band_index)
    
    # Longitude zone (6 degrees per zone, starting from 1 at 180W)
    lon_zone = math.floor((lon + 180) / 6) + 1
    
    return f"{lat_char}{lon_zone:02}"

def generate_tile_urls(name, min_lat, max_lat, min_lon, max_lon):
    """Generates download URLs for a given rectangular area."""
    urls = []
    print(f"--- Generating URLs for {name} ---")
    for lat in range(min_lat, max_lat):
        for lon in range(min_lon, max_lon):
            # Define the tile by its bottom-left corner
            hgt_ns = 'N'
            hgt_ew = 'E' if lon >= 0 else 'W'
            hgt_lat = f"{lat:02}"
            hgt_lon = f"{abs(lon):03}"
            
            hgt_filename = f"{hgt_ns}{hgt_lat}{hgt_ew}{hgt_lon}"
            
            imw_tile = get_imw_tile(lat, lon)
            
            if imw_tile:
                # Format: https://www.viewfinderpanoramas.org/dem3/{IMW_TILE}/{HGT_FILENAME}.zip
                url = f"https://www.viewfinderpanoramas.org/dem3/{imw_tile}/{hgt_filename}.zip"
                urls.append(url)
                print(f"  {hgt_filename} -> IMW Tile: {imw_tile} -> {url}")

    return urls

def main():
    """Main function to define target areas and generate download list."""
    # Define target areas [min_lat, max_lat, min_lon, max_lon]
    target_areas = {
        "israel_palestine": [29, 34, 34, 36],
        "kashmir": [32, 37, 72, 80],
        "scottish_highlands": [56, 59, -6, -2], # Longitude from W6 to W2
        "taiwan": [21, 26, 120, 122],
        "korean_peninsula": [33, 43, 124, 131],
    }

    all_urls = []
    for name, bounds in target_areas.items():
        urls = generate_tile_urls(name, bounds[0], bounds[1], bounds[2], bounds[3])
        all_urls.extend(urls)
        
    # Remove duplicates
    unique_urls = sorted(list(set(all_urls)))
    
    output_dir = "geospatial_trajectory_generator/data"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, "dem_imw_download_list.txt")

    with open(output_path, 'w') as f:
        for url in unique_urls:
            f.write(f"{url}\n")
            
    print(f"\nSuccessfully generated {len(unique_urls)} unique URLs.")
    print(f"Download list saved to: {output_path}")

if __name__ == "__main__":
    main() 