 
#!/bin/bash

# This script downloads DEM data from viewfinderpanoramas.org
# based on a list of URLs in a text file.

# The script should be run from the 'geospatial_trajectory_generator' directory.
LOGS_DIR="logs"
DATA_DIR="data/raw/dem_viewfinder"
DOWNLOAD_LIST_FILE="scripts/dem_viewfinder_download_list.txt"
LOG_FILE="${LOGS_DIR}/download_viewfinder_dem.log"

# Create necessary directories
mkdir -p "${LOGS_DIR}"
mkdir -p "${DATA_DIR}"

# Check if the download list file exists
if [ ! -f "$DOWNLOAD_LIST_FILE" ]; then
    echo "ERROR: Download list file not found at ${DOWNLOAD_LIST_FILE}" | tee -a "$LOG_FILE"
    echo "Please generate it first by running 'generate_viewfinder_download_list.py'" | tee -a "$LOG_FILE"
    exit 1
fi

# --- Download Files ---
echo "Starting DEM download from Viewfinder Panoramas..." | tee -a "$LOG_FILE"
echo "Data will be saved in: ${DATA_DIR}" | tee -a "$LOG_FILE"
echo "See log file for details: ${LOG_FILE}"

# Use wget to download the files.
# -i: Read URLs from the input file.
# -P: Save files to the specified directory.
# -nc: No-clobber, don't re-download files that already exist.
# -nv: No-verbose, be quieter.
# -a: Append output to log file.
wget -i "${DOWNLOAD_LIST_FILE}" -P "${DATA_DIR}" -nc -nv -a "${LOG_FILE}"

# --- Verification ---
# Count the number of URLs and the number of downloaded zip files
URL_COUNT=$(wc -l < "${DOWNLOAD_LIST_FILE}")
DOWNLOADED_COUNT=$(ls -1 "${DATA_DIR}"/*.zip 2>/dev/null | wc -l)

echo "----------------------------------------" | tee -a "$LOG_FILE"
echo "Download process finished." | tee -a "$LOG_FILE"
echo "Verification:" | tee -a "$LOG_FILE"
echo "  - URLs in list: ${URL_COUNT}" | tee -a "$LOG_FILE"
echo "  - Downloaded .zip files: ${DOWNLOADED_COUNT}" | tee -a "$LOG_FILE"

if [ "$URL_COUNT" -eq "$DOWNLOADED_COUNT" ]; then
    echo "SUCCESS: All files seem to be downloaded correctly." | tee -a "$LOG_FILE"
else
    echo "WARNING: The number of downloaded files does not match the number of URLs." | tee -a "$LOG_FILE"
    echo "Please check the log file '${LOG_FILE}' for any errors." | tee -a "$LOG_FILE"
fi

exit 0 