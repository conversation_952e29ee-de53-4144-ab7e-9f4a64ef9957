import math

def get_dem_tile_names(lon, lat):
    """
    Calculates the ASTER GDEM v3 tile name for a given longitude and latitude.
    The name corresponds to the southwest (bottom-left) corner of the 1x1 degree tile.
    """
    if lat >= 0:
        lat_str = f"N{math.floor(abs(lat)):02d}"
    else:
        lat_str = f"S{math.floor(abs(lat)):02d}"

    if lon >= 0:
        lon_str = f"E{math.floor(abs(lon)):03d}"
    else:
        lon_str = f"W{math.floor(abs(lon)):03d}"
    
    return f"ASTGTMV003_{lat_str}{lon_str}"

def get_required_tiles_for_bbox(bbox):
    """
    Generates a set of required tile names for a given bounding box.
    bbox = (min_lon, min_lat, max_lon, max_lat)
    """
    min_lon, min_lat, max_lon, max_lat = bbox
    
    # Floor the min and max lat/lon to get the integer grid boundaries
    start_lon = math.floor(min_lon)
    end_lon = math.floor(max_lon)
    start_lat = math.floor(min_lat)
    end_lat = math.floor(max_lat)
    
    tile_names = set()
    
    # Iterate through the grid and get tile names
    for lon in range(start_lon, end_lon + 1):
        for lat in range(start_lat, end_lat + 1):
            tile_names.add(get_dem_tile_names(lon, lat))
            
    return tile_names

def main():
    """
    Main function to define regions and generate the complete file list.
    """
    # Bounding boxes for the five regions of interest
    regions = {
        "scottish_highlands": (-6.2, 56.2, -3.7, 58.6), # min_lon, min_lat, max_lon, max_lat
        "israel_palestine": (34.2, 29.5, 35.6, 33.3),
        "kashmir": (72.6, 32.2, 77.8, 37.1),
        "taiwan": (119.8, 21.8, 122.1, 25.4),
        "korean_peninsula": (124.0, 33.0, 130.0, 43.1)
    }

    all_required_tiles = set()

    for region_name, bbox in regions.items():
        print(f"Calculating tiles for: {region_name}")
        tiles = get_required_tiles_for_bbox(bbox)
        all_required_tiles.update(tiles)
        print(f" -> Found {len(tiles)} tiles.")

    # Sort the list for consistency
    sorted_tiles = sorted(list(all_required_tiles))
    
    output_filename = 'geospatial_trajectory_generator/scripts/dem_aster_download_list.txt'
    
    with open(output_filename, 'w') as f:
        for tile_name in sorted_tiles:
            # The correct zip file name does not have the "_d" suffix.
            f.write(f"{tile_name}.zip\n")

    print(f"\nGenerated a list of {len(sorted_tiles)} unique DEM tiles.")
    print(f"File list saved to: {output_filename}")

if __name__ == "__main__":
    main() 