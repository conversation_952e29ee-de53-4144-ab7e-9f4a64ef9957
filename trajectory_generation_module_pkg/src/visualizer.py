"""
轨迹生成可视化模块

用于可视化展示轨迹生成过程、路径点、环境边界和警告信息
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Rectangle
import time
from typing import List, Tuple, Dict, Any

class TrajectoryVisualizer:
    """轨迹可视化器"""
    
    # 地类颜色映射
    LANDCOVER_COLORS = {
        10: '#0077BE',  # 水域: 蓝色
        20: '#80CCFF',  # 湿地: 浅蓝色
        30: '#90EE90',  # 草地: 浅绿色
        40: '#228B22',  # 灌木地: 深绿色
        50: '#CD5C5C',  # 建筑用地: 红褐色
        60: '#FFD700',  # 农田: 金黄色
        80: '#006400',  # 森林: 深绿色
        90: '#DEB887',  # 荒地: 棕色
        255: '#808080'  # 未分类: 灰色
    }
    
    def __init__(self):
        """初始化可视化器"""
        self.fig = None
        self.ax = None
        self.trajectory_line = None
        self.waypoints_scatter = None
        self.vehicle_marker = None
        self.warning_text = None
        self.boundary_rect = None
        self.landcover_patches = []
        
        # 存储历史轨迹数据
        self.trajectory_history = []
        self.warnings_history = []
        
    def setup_visualization(self, env_maps, waypoints, init_position):
        """设置可视化环境
        
        Args:
            env_maps: 环境地图对象
            waypoints: 路径点列表
            init_position: 初始位置
        """
        # 创建图形
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        
        # 提取航点坐标
        waypoints_array = np.array(waypoints)
        x_waypoints = waypoints_array[:, 0]
        y_waypoints = waypoints_array[:, 1]
        
        # 绘制初始路径航点
        self.waypoints_scatter = self.ax.scatter(
            x_waypoints, y_waypoints, c='blue', marker='o', 
            s=10, alpha=0.5, label='路径点'
        )
        
        # 绘制环境地图边界
        if env_maps:
            bounds = env_maps.get_utm_bounds()
            min_x, max_x = bounds['min_x'], bounds['max_x']
            min_y, max_y = bounds['min_y'], bounds['max_y']
            
            # 添加地图边界矩形
            self.boundary_rect = Rectangle(
                (min_x, min_y), 
                max_x - min_x, 
                max_y - min_y,
                fill=False, 
                edgecolor='red', 
                linestyle='--', 
                linewidth=2,
                label='地图边界'
            )
            self.ax.add_patch(self.boundary_rect)
            
            # 简化显示地类背景（可选，根据实际性能调整）
            # self._add_landcover_background(env_maps)
            
            # 设置坐标轴范围，稍微扩大以便更好地观察
            padding = (max_x - min_x) * 0.1
            self.ax.set_xlim(min_x - padding, max_x + padding)
            self.ax.set_ylim(min_y - padding, max_y + padding)
        else:
            # 如果没有环境地图，使用航点的范围
            padding = (np.max(x_waypoints) - np.min(x_waypoints)) * 0.1
            self.ax.set_xlim(np.min(x_waypoints) - padding, np.max(x_waypoints) + padding)
            self.ax.set_ylim(np.min(y_waypoints) - padding, np.max(y_waypoints) + padding)
        
        # 初始化轨迹线
        self.trajectory_line, = self.ax.plot([], [], 'g-', linewidth=1.5, label='生成轨迹')
        
        # 初始化车辆标记
        self.vehicle_marker = self.ax.scatter([], [], c='red', marker='^', s=100, label='车辆位置')
        
        # 添加警告文本区域
        self.warning_text = self.ax.text(
            0.02, 0.02, '', transform=self.ax.transAxes,
            color='red', fontsize=12, bbox=dict(facecolor='white', alpha=0.7)
        )
        
        # 添加图例和标题
        self.ax.legend(loc='upper right')
        self.ax.set_title('轨迹生成过程可视化', fontsize=22)
        self.ax.set_xlabel('UTM东向坐标 (m)', fontsize=20)
        self.ax.set_ylabel('UTM北向坐标 (m)', fontsize=20)
        self.ax.tick_params(labelsize=20)
        
        # 启用网格
        self.ax.grid(True)
        
        plt.tight_layout()
    
    def _add_landcover_background(self, env_maps):
        """添加地类背景（简化版本）
        注意：此功能可能降低性能，特别是对于大型地图
        """
        # 获取地图分辨率和尺寸
        resolution = env_maps.get_resolution()
        bounds = env_maps.get_utm_bounds()
        
        # 简化：每隔N个像素采样一次地类
        sample_rate = 10  # 每10个像素采样一次
        
        # 创建采样网格
        x_range = np.arange(bounds['min_x'], bounds['max_x'], resolution * sample_rate)
        y_range = np.arange(bounds['min_y'], bounds['max_y'], resolution * sample_rate)
        
        # 清除现有的地类背景
        for patch in self.landcover_patches:
            patch.remove()
        self.landcover_patches = []
        
        # 对每个采样点查询地类并绘制
        for x in x_range:
            for y in y_range:
                features = env_maps.query_by_xy(x, y)
                landcover = features['landcover']
                
                if landcover in self.LANDCOVER_COLORS:
                    # 创建一个小矩形表示该地类
                    rect_size = resolution * sample_rate
                    rect = Rectangle(
                        (x - rect_size/2, y - rect_size/2),
                        rect_size, rect_size,
                        color=self.LANDCOVER_COLORS[landcover],
                        alpha=0.3
                    )
                    self.ax.add_patch(rect)
                    self.landcover_patches.append(rect)
    
    def update_visualization(self, position, velocity, heading, warning_msg=None):
        """更新可视化
        
        Args:
            position: 当前位置 [x, y]
            velocity: 当前速度 [vx, vy]
            heading: 当前航向角(度)
            warning_msg: 警告信息（可选）
        """
        if self.fig is None or self.ax is None:
            print("可视化尚未初始化，请先调用setup_visualization")
            return
        
        # 记录轨迹历史
        self.trajectory_history.append((position[0], position[1]))
        if warning_msg:
            self.warnings_history.append((position[0], position[1], warning_msg))
        
        # 更新轨迹线
        traj_x, traj_y = zip(*self.trajectory_history)
        self.trajectory_line.set_data(traj_x, traj_y)
        
        # 更新车辆位置
        self.vehicle_marker.set_offsets([position[0], position[1]])
        
        # 更新警告文本
        if warning_msg:
            self.warning_text.set_text(warning_msg)
            self.warning_text.set_visible(True)
        else:
            self.warning_text.set_visible(False)
        
        # 刷新图形
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()
        
        # 暂停一小段时间，以便动画效果更明显
        plt.pause(0.01)
    
    def create_animation(self, trajectory_points, env_maps=None, waypoints=None):
        """创建轨迹生成过程的动画
        
        Args:
            trajectory_points: 轨迹点列表 [(timestamp, x, y, vx, vy, heading, ax, ay, goal_id), ...]
            env_maps: 环境地图对象（可选）
            waypoints: 路径点列表（可选）
            
        Returns:
            matplotlib.animation.Animation: 动画对象
        """
        # 确保有轨迹点
        if not trajectory_points:
            print("没有轨迹点，无法创建动画")
            return None
        
        # 提取初始位置
        init_x, init_y = trajectory_points[0][1], trajectory_points[0][2]
        
        # 设置可视化环境
        if waypoints is None:
            # 如果没有提供航点，使用轨迹点作为替代
            traj_points = np.array([(p[1], p[2]) for p in trajectory_points])
            self.setup_visualization(env_maps, traj_points, [init_x, init_y])
        else:
            self.setup_visualization(env_maps, waypoints, [init_x, init_y])
        
        # 定义动画更新函数
        def update(frame):
            if frame < len(trajectory_points):
                _, x, y, vx, vy, heading, _, _, _ = trajectory_points[frame]
                
                # 更新轨迹线
                if frame == 0:
                    self.trajectory_history = [(x, y)]
                else:
                    self.trajectory_history.append((x, y))
                
                traj_x, traj_y = zip(*self.trajectory_history)
                self.trajectory_line.set_data(traj_x, traj_y)
                
                # 更新车辆位置
                self.vehicle_marker.set_offsets([x, y])
                
                # 检查是否接近边界
                warning_msg = ""
                if env_maps:
                    bounds = env_maps.get_utm_bounds()
                    # 计算到边界的距离
                    dist_to_min_x = abs(x - bounds['min_x'])
                    dist_to_max_x = abs(x - bounds['max_x'])
                    dist_to_min_y = abs(y - bounds['min_y'])
                    dist_to_max_y = abs(y - bounds['max_y'])
                    
                    min_dist = min(dist_to_min_x, dist_to_max_x, dist_to_min_y, dist_to_max_y)
                    if min_dist < 50:  # 小于50米时显示警告
                        warning_msg = f"警告：接近地图边界！距离边界{min_dist:.2f}米"
                        
                    # 检查是否超出边界
                    if (x < bounds['min_x'] or x > bounds['max_x'] or 
                        y < bounds['min_y'] or y > bounds['max_y']):
                        warning_msg = "警告：坐标超出地图范围！"
                
                self.warning_text.set_text(warning_msg)
                self.warning_text.set_visible(bool(warning_msg))
            
            return self.trajectory_line, self.vehicle_marker, self.warning_text
        
        # 创建动画
        ani = animation.FuncAnimation(
            self.fig, update, frames=len(trajectory_points),
            interval=50, blit=True, repeat=False
        )
        
        return ani
    
    def save_animation(self, ani, filename="trajectory_animation.mp4"):
        """保存动画为视频文件
        
        Args:
            ani: 动画对象
            filename: 输出文件名
        """
        # 需要 ffmpeg 库支持
        try:
            ani.save(filename, writer='ffmpeg', fps=20, dpi=100)
            print(f"动画已保存到: {filename}")
        except Exception as e:
            print(f"保存动画失败: {e}")
            print("请确保已安装 ffmpeg 并添加到 PATH 环境变量")
    
    def show(self):
        """显示可视化结果"""
        plt.show() 