# Trajectory Generation Module

This module is responsible for converting path waypoints into full trajectories with kinematic information.

## Core Components

- `src/generator.py`: Contains the `TrajectoryGenerator` class for generating trajectories.
- `src/environment.py`: Contains the `EnvironmentMaps` class for loading and querying environmental data (DEM, slope, landcover, etc.).

## Usage

Refer to `examples/run_example.py` for a demonstration of how to use this module.

## Dependencies

See `requirements.txt`. Install using:
\`\`\`bash
pip install -r requirements.txt
\`\`\` 