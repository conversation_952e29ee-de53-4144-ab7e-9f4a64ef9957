#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import argparse
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import sys
import csv
import rasterio

# 添加上级目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from src.environment import EnvironmentMaps
from src.generator import TrajectoryGenerator

def run_single_trajectory_generation(input_path_file, env_data_dir, output_dir, goal_id=None):
    """
    生成单个轨迹
    
    参数:
        input_path_file: 输入路径文件路径（.npy格式）
        env_data_dir: 环境数据目录
        output_dir: 输出目录
        goal_id: 目标ID，用于输出文件名
    """
    print(f"处理路径: {input_path_file}")
    print(f"环境数据目录: {env_data_dir}")
    print(f"输出目录: {output_dir}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载环境数据
    env_maps = EnvironmentMaps(
        dem_file=os.path.join(env_data_dir, "dem_aligned.tif"),
        slope_file=os.path.join(env_data_dir, "slope_aligned.tif"),
        aspect_file=os.path.join(env_data_dir, "aspect_aligned.tif"),
        landcover_file=os.path.join(env_data_dir, "landcover_aligned.tif") if os.path.exists(os.path.join(env_data_dir, "landcover_aligned.tif")) else None
    )
    
    # 初始化轨迹生成器
    # 注意：如果要使用特定分析结果路径，可以在这里指定
    analysis_results_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "examples/data/analysis_results")
    trajectory_generator = TrajectoryGenerator(env_maps, analysis_results_path=analysis_results_path)
    
    # 加载路径点
    waypoints = np.load(input_path_file)
    
    # 验证路径点格式
    if waypoints.ndim != 2 or waypoints.shape[1] < 2:
        raise ValueError(f"路径点格式错误，期望是2D数组 [n, 2+]，实际为 {waypoints.shape}")
    
    # 检查路径点是否是UTM坐标
    # 根据原始代码，路径点是网格坐标，需要转换为UTM坐标
    
    # 获取DEM的变换矩阵，用于坐标转换
    transform = env_maps.dem_ds.transform

    # 将网格坐标转换为UTM坐标
    # 参考原始代码中的grid_to_utm方法
    utm_waypoints = np.zeros_like(waypoints, dtype=np.float64)
    for i, point in enumerate(waypoints):
        # 在原始代码中，grid_to_utm方法是这样的：
        # utm_x, utm_y = self.transform * (grid_x, grid_y)
        grid_x, grid_y = point
        utm_x, utm_y = transform * (grid_x, grid_y)
        utm_waypoints[i] = [utm_x, utm_y]
    
    print(f"网格坐标转换为UTM坐标示例:")
    print(f"  原始网格坐标: {waypoints[0]}")
    print(f"  转换UTM坐标: {utm_waypoints[0]}")
    print(f"  DEM边界: {env_maps.get_utm_bounds()}")
    
    # 使用转换后的UTM坐标
    waypoints = utm_waypoints
    
    # 设置初始状态
    initial_state = {
        'position': waypoints[0][:2],  # 起始位置
        'velocity': np.array([0.0, 0.0]),  # 初始速度为0
        'heading': 0.0,  # 初始朝向，将在生成过程中计算
        'time': 0.0  # 初始时间
    }
    
    # 设置生成参数
    generation_params = {
        'dt': 1.0,  # 时间步长，秒
        'max_speed': 10.0,  # 最大速度，米/秒
        'min_speed': 0.1,  # 最小速度，米/秒
        'max_accel': 0.5,  # 最大加速度，米/秒²
        'max_decel': 0.5,  # 最大减速度，米/秒²
    }
    
    # 设置仿真参数
    simulation_params = {
        'dt': 1.0,  # 仿真时间步长，秒
        'path_tracking_gain': 0.5,  # 路径跟踪增益
        'lookahead_distance': 5.0,  # 前视距离，米
        'target_speed': 5.0,  # 目标速度，米/秒
    }
    
    # 生成轨迹
    trajectory = trajectory_generator.generate_trajectory(
        waypoints, 
        initial_state, 
        generation_params, 
        simulation_params
    )
    
    # 确定输出文件名
    if goal_id is None:
        # 从输入文件名提取目标ID
        base_name = os.path.basename(input_path_file)
        goal_id = os.path.splitext(base_name)[0]
    
    output_file = os.path.join(output_dir, f"{goal_id}_trajectory.csv")
    
    # 保存轨迹到CSV文件
    with open(output_file, 'w', newline='') as csvfile:
        fieldnames = ['time', 'x', 'y', 'vx', 'vy', 'speed', 'heading', 'angular_velocity']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for point in trajectory:
            writer.writerow({
                'time': point['time'],
                'x': point['position'][0],
                'y': point['position'][1],
                'vx': point['velocity'][0],
                'vy': point['velocity'][1],
                'speed': point['speed'],
                'heading': point['heading'],
                'angular_velocity': point['angular_velocity']
            })
    
    print(f"轨迹已保存到 {output_file}")
    
    # 计算平均速度
    speeds = [point['speed'] for point in trajectory]
    avg_speed = sum(speeds) / len(speeds) if speeds else 0
    print(f"生成轨迹的平均速度: {avg_speed:.2f} m/s")
    
    return output_file, trajectory

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='轨迹生成示例')
    parser.add_argument('--input_path', type=str, required=True, 
                        help='输入路径文件路径（.npy格式）')
    parser.add_argument('--env_data_dir', type=str, required=True, 
                        help='环境数据目录')
    parser.add_argument('--output_dir', type=str, required=True, 
                        help='输出目录')
    parser.add_argument('--goal_id', type=str, default=None, 
                        help='目标ID，用于输出文件名')
    
    args = parser.parse_args()
    
    run_single_trajectory_generation(
        args.input_path, 
        args.env_data_dir, 
        args.output_dir, 
        args.goal_id
    ) 