# visualize_generated_trajectories.py
import pandas as pd
import matplotlib.pyplot as plt
import os
import argparse

def plot_trajectories(csv_files, output_dir):
    """
    读取并绘制多个轨迹文件的路径
    """
    plt.figure(figsize=(12, 10))
    
    for i, file_path in enumerate(csv_files):
        if not os.path.exists(file_path):
            print(f"警告: 文件不存在 {file_path}")
            continue
            
        try:
            df = pd.read_csv(file_path)
            label = os.path.basename(file_path).replace('_high_mobility.csv', '')
            plt.plot(df['x'], df['y'], label=label, linewidth=1.5, alpha=0.8)
            # 标记起点和终点
            plt.scatter(df['x'].iloc[0], df['y'].iloc[0], marker='o', s=50, label=f'{label} Start', alpha=0.8)
            plt.scatter(df['x'].iloc[-1], df['y'].iloc[-1], marker='x', s=50, label=f'{label} End', alpha=0.8)
        except Exception as e:
            print(f"绘制文件 {file_path} 出错: {e}")

    plt.title('Generated Trajectories Visualization')
    plt.xlabel('UTM Easting (x)')
    plt.ylabel('UTM Northing (y)')
    plt.legend(fontsize='small', loc='center left', bbox_to_anchor=(1, 0.5))
    plt.grid(True)
    plt.axis('equal') # 保持X/Y轴比例一致
    plt.tight_layout(rect=[0, 0, 0.85, 1]) # 调整布局给图例留空间

    # 保存图像
    output_image_path = os.path.join(output_dir, 'generated_trajectory_paths.png')
    plt.savefig(output_image_path)
    print(f"轨迹路径可视化图像已保存到: {output_image_path}")
    plt.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Visualize generated trajectories.')
    parser.add_argument('--trajectory_dir', type=str, 
                        default='/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories', 
                        help='Directory containing trajectory CSV files.')
    parser.add_argument('--num_to_plot', type=int, default=5, 
                        help='Number of trajectories to plot.')
    args = parser.parse_args()

    # 查找CSV文件
    all_csv_files = [os.path.join(args.trajectory_dir, f) for f in os.listdir(args.trajectory_dir) if f.endswith('_high_mobility.csv')]
    
    if not all_csv_files:
        print(f"在目录 {args.trajectory_dir} 中未找到轨迹CSV文件。")
    else:
        # 选择要绘制的文件
        files_to_plot = all_csv_files[:args.num_to_plot]
        print(f"将绘制以下轨迹: {files_to_plot}")
        plot_trajectories(files_to_plot, args.trajectory_dir) 