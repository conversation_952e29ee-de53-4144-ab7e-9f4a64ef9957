"""
简化轨迹可视化脚本

用于生成简化版的轨迹可视化图像
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse

def visualize_simplified(trajectory_file, path_file=None, output_file=None):
    """生成简化版轨迹可视化
    
    Args:
        trajectory_file: 轨迹文件路径
        path_file: 路径文件路径（可选）
        output_file: 输出文件路径（可选）
    """
    # 加载轨迹数据
    df = pd.read_csv(trajectory_file)
    
    # 创建图形（2x1布局）
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(8, 10))
    
    # 1. 轨迹形状图
    ax1.plot(df['x'], df['y'], 'g-', linewidth=1, alpha=0.8, label='生成轨迹')
    
    # 绘制起点和终点
    ax1.scatter(df['x'].iloc[0], df['y'].iloc[0], color='blue', s=60, marker='^', label='起点')
    ax1.scatter(df['x'].iloc[-1], df['y'].iloc[-1], color='red', s=60, marker='s', label='终点')
    
    # 如果提供了路径文件，绘制原始路径
    if path_file and os.path.exists(path_file):
        try:
            path_points = np.load(path_file)
            # 采样以减少点数
            sample_rate = max(1, len(path_points) // 100)
            path_points_sampled = path_points[::sample_rate]
            ax1.scatter(path_points_sampled[:, 0], path_points_sampled[:, 1], 
                       color='black', s=2, alpha=0.5, label='原始路径')
        except Exception as e:
            print(f"无法加载路径文件: {e}")
    
    # 添加图例和标题
    ax1.legend(loc='best')
    ax1.set_title('轨迹形状')
    ax1.set_xlabel('X坐标')
    ax1.set_ylabel('Y坐标')
    ax1.grid(True, alpha=0.3)
    
    # 2. 速度-时间图
    # 计算速度和时间
    speeds = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
    times = df['timestamp_ms'] / 1000  # 转换为秒
    
    # 采样以减少点数
    sample_rate = max(1, len(speeds) // 100)
    speeds_sampled = speeds[::sample_rate]
    times_sampled = times[::sample_rate]
    
    # 绘制速度曲线
    ax2.plot(times_sampled, speeds_sampled, 'b-', linewidth=1.5)
    
    # 添加平均速度线
    avg_speed = speeds.mean()
    ax2.axhline(y=avg_speed, color='r', linestyle='--', linewidth=1.5, 
               label=f'平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)')
    
    # 添加图例和标题
    ax2.legend(loc='best')
    ax2.set_title('速度随时间变化')
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('速度 (m/s)')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, max(speeds) * 1.1)
    
    # 保存图形或显示
    plt.tight_layout()
    if output_file:
        plt.savefig(output_file, dpi=100)
        print(f"简化可视化图已保存到: {output_file}")
    else:
        plt.show()
    
    plt.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成简化版轨迹可视化')
    parser.add_argument('--trajectory', type=str, required=True,
                        help='轨迹文件路径')
    parser.add_argument('--path', type=str, default=None,
                        help='路径文件路径（可选）')
    parser.add_argument('--output', type=str, default=None,
                        help='输出文件路径')
    args = parser.parse_args()
    
    # 如果未指定输出文件，创建默认名称
    if not args.output:
        # 创建输出目录
        os.makedirs('./simple_vis', exist_ok=True)
        # 获取文件名（不含扩展名）
        base_name = os.path.splitext(os.path.basename(args.trajectory))[0]
        args.output = f'./simple_vis/{base_name}_simple.png'
    
    # 生成简化可视化
    visualize_simplified(args.trajectory, args.path, args.output)

if __name__ == "__main__":
    main() 