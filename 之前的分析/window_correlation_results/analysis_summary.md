# 分窗口相关性分析报告

## 1. 综合表现对比

| 数据文件 | 时间窗口 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|----------|----------|------------|----------------|-------------|
| converted_sequence_1_core_1秒_速度比较 | 30秒 | 0.3219 | 1.73 | 1.32 | 109.96 |
| converted_sequence_1_core_5秒_速度比较 | 30秒 | 0.3207 | 1.71 | 1.32 | 93.80 |
| converted_sequence_1_core_10秒_速度比较 | 17秒 | 0.3508 | 1.65 | 1.28 | 89.83 |
| converted_sequence_1_core_15秒_速度比较 | 11秒 | 0.3897 | 1.57 | 1.25 | 85.52 |
| converted_sequence_2_core_1秒_速度比较 | 30秒 | 0.2347 | 2.43 | 1.81 | 1122.30 |
| converted_sequence_2_core_5秒_速度比较 | 30秒 | 0.2425 | 2.40 | 1.79 | 980.72 |
| converted_sequence_2_core_10秒_速度比较 | 21秒 | 0.2570 | 2.35 | 1.76 | 913.96 |
| converted_sequence_2_core_15秒_速度比较 | 14秒 | 0.2516 | 2.35 | 1.76 | 916.42 |
| converted_sequence_3_core_1秒_速度比较 | 30秒 | 0.3309 | 1.94 | 1.48 | 169.77 |
| converted_sequence_3_core_5秒_速度比较 | 30秒 | 0.3438 | 1.90 | 1.46 | 144.21 |
| converted_sequence_3_core_10秒_速度比较 | 17秒 | 0.3590 | 1.85 | 1.44 | 136.17 |
| converted_sequence_3_core_15秒_速度比较 | 11秒 | 0.3903 | 1.79 | 1.41 | 125.03 |
| converted_sequence_4_core_1秒_速度比较 | 30秒 | 0.3809 | 2.17 | 1.67 | 378.09 |
| converted_sequence_4_core_5秒_速度比较 | 30秒 | 0.4046 | 2.11 | 1.65 | 302.41 |
| converted_sequence_4_core_10秒_速度比较 | 20秒 | 0.4265 | 2.07 | 1.62 | 284.60 |
| converted_sequence_4_core_15秒_速度比较 | 13秒 | 0.4440 | 2.01 | 1.59 | 277.86 |


## 2. 高相关性窗口详情

### converted_sequence_1_core_1秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 75 | 104 | 0.8857 | 1.62 | 1.58 | 38.00 |
| 210 | 239 | 0.8617 | 1.13 | 1.02 | 21.19 |
| 735 | 764 | 0.8433 | 0.86 | 0.84 | 14.24 |


### converted_sequence_1_core_5秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 135 | 164 | 0.6582 | 1.54 | 1.10 | 90.27 |
| 300 | 329 | 0.6229 | 2.23 | 1.99 | 35.56 |


### converted_sequence_1_core_10秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 96 | 112 | 0.6665 | 1.33 | 0.98 | 33.78 |
| 64 | 80 | 0.6568 | 1.45 | 1.12 | 69.53 |
| 152 | 168 | 0.6515 | 2.06 | 1.80 | 32.27 |


### converted_sequence_1_core_15秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 45 | 55 | 0.7277 | 1.33 | 1.04 | 76.51 |
| 65 | 75 | 0.7181 | 1.28 | 0.98 | 38.51 |
| 100 | 110 | 0.6502 | 2.05 | 1.84 | 32.78 |


### converted_sequence_2_core_1秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 1095 | 1124 | 0.8296 | 0.66 | 0.61 | 12.57 |
| 1545 | 1574 | 0.8260 | 1.09 | 1.04 | 22.94 |
| 1365 | 1394 | 0.8115 | 1.95 | 1.93 | 144.22 |


### converted_sequence_2_core_5秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 240 | 269 | 0.6491 | 3.09 | 2.68 | 1563.84 |
| 45 | 74 | 0.6277 | 1.97 | 1.53 | 57.28 |


### converted_sequence_2_core_10秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 120 | 140 | 0.6691 | 2.76 | 2.45 | 1076.90 |


### converted_sequence_2_core_15秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 77 | 90 | 0.7393 | 2.60 | 2.14 | 1038.96 |
| 14 | 27 | 0.6738 | 1.90 | 1.60 | 170.18 |


### converted_sequence_3_core_1秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 105 | 134 | 0.9385 | 1.28 | 1.24 | 32.28 |
| 975 | 1004 | 0.9015 | 0.72 | 0.61 | 10.48 |
| 1125 | 1154 | 0.8727 | 1.85 | 1.71 | 34.47 |


### converted_sequence_3_core_5秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 195 | 224 | 0.7301 | 1.73 | 1.42 | 168.32 |
| 285 | 314 | 0.6263 | 2.28 | 1.95 | 32.71 |


### converted_sequence_3_core_10秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 96 | 112 | 0.6313 | 1.70 | 1.47 | 93.64 |


### converted_sequence_3_core_15秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 95 | 105 | 0.7275 | 2.15 | 1.83 | 31.33 |
| 35 | 45 | 0.7142 | 1.34 | 0.90 | 55.92 |
| 60 | 70 | 0.7050 | 1.35 | 1.06 | 43.76 |


### converted_sequence_4_core_1秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 1860 | 1889 | 0.9295 | 1.15 | 1.00 | 27.90 |
| 1365 | 1394 | 0.8972 | 1.10 | 0.94 | 18.13 |
| 1905 | 1934 | 0.8272 | 1.45 | 1.41 | 21.87 |


### converted_sequence_4_core_5秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 240 | 269 | 0.7650 | 1.96 | 1.73 | 136.84 |


### converted_sequence_4_core_10秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 110 | 129 | 0.7019 | 1.65 | 1.29 | 57.03 |


### converted_sequence_4_core_15秒_速度比较

| 窗口起点 | 窗口终点 | 相关系数 | RMSE (m/s) | 平均误差 (m/s) | 相对误差 (%) |
|---------|---------|---------|-----------|---------------|-------------|
| 96 | 108 | 0.7300 | 0.75 | 0.56 | 12.21 |
| 48 | 60 | 0.7006 | 1.56 | 1.09 | 93.52 |
| 72 | 84 | 0.6454 | 1.50 | 1.21 | 36.64 |


## 3. 建议与分析

1. 整体表现最好的数据文件是 **converted_sequence_4_core_15秒_速度比较**，相关系数为 **0.4440**

2. 所有文件中表现最好的数据窗口在 **converted_sequence_3_core_1秒_速度比较** 中，位置为 **(105, 134)**，相关系数为 **0.9385**

3. 所有文件的平均相关系数为 **0.3405**

4. 以下文件表现较差，建议重点关注：
   - converted_sequence_1_core_1秒_速度比较
   - converted_sequence_1_core_5秒_速度比较
   - converted_sequence_2_core_1秒_速度比较
   - converted_sequence_2_core_5秒_速度比较
   - converted_sequence_2_core_10秒_速度比较
   - converted_sequence_2_core_15秒_速度比较
   - converted_sequence_3_core_1秒_速度比较

5. 建议模型优化的策略：
   - 针对高相关性窗口，提取环境特征，分析模型表现良好的原因
   - 针对低相关性窗口，分析环境特征与速度变化的关系，优化模型参数
   - 考虑使用分段模型策略，针对不同速度区间或环境条件使用不同的预测策略
   - 对残差进行深入分析，寻找残差与环境特征的关系，进行针对性优化
