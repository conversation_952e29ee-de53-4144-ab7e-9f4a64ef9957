"""
轨迹可视化脚本

用于可视化生成的轨迹形状、速度和加速度随时间变化
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import argparse

def visualize_trajectory_shape(trajectory_file, path_file=None, output_file=None):
    """可视化轨迹形状
    
    Args:
        trajectory_file: 轨迹文件路径
        path_file: 路径文件路径（可选）
        output_file: 输出文件路径（可选）
    """
    # 加载轨迹数据
    df = pd.read_csv(trajectory_file)
    
    # 创建图形
    plt.figure(figsize=(10, 10))
    
    # 计算速度
    speeds = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
    
    # 创建颜色映射
    colormap = LinearSegmentedColormap.from_list('speed_cmap', ['blue', 'green', 'yellow', 'red'])
    
    # 绘制轨迹，使用速度着色
    points = plt.scatter(df['x'], df['y'], c=speeds, cmap=colormap, s=5, alpha=0.8)
    plt.colorbar(points, label='速度 (m/s)')
    
    # 绘制起点和终点
    plt.scatter(df['x'].iloc[0], df['y'].iloc[0], color='green', s=100, marker='^', label='起点')
    plt.scatter(df['x'].iloc[-1], df['y'].iloc[-1], color='red', s=100, marker='s', label='终点')
    
    # 如果提供了路径文件，绘制原始路径
    if path_file and os.path.exists(path_file):
        try:
            path_points = np.load(path_file)
            plt.scatter(path_points[:, 0], path_points[:, 1], color='black', s=2, alpha=0.3, label='原始路径')
        except Exception as e:
            print(f"无法加载路径文件: {e}")
    
    # 添加图例和标题
    plt.legend(loc='upper right')
    plt.title('轨迹形状可视化')
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.grid(True, alpha=0.3)
    
    # 调整轴范围以更好地显示轨迹
    x_range = df['x'].max() - df['x'].min()
    y_range = df['y'].max() - df['y'].min()
    plt.xlim(df['x'].min() - x_range * 0.05, df['x'].max() + x_range * 0.05)
    plt.ylim(df['y'].min() - y_range * 0.05, df['y'].max() + y_range * 0.05)
    
    # 保存图形或显示
    if output_file:
        plt.savefig(output_file, dpi=300)
        print(f"轨迹形状图已保存到: {output_file}")
    else:
        plt.tight_layout()
        plt.show()
    
    plt.close()

def visualize_trajectory_speed(trajectory_file, output_file=None):
    """可视化轨迹速度随时间变化
    
    Args:
        trajectory_file: 轨迹文件路径
        output_file: 输出文件路径（可选）
    """
    # 加载轨迹数据
    df = pd.read_csv(trajectory_file)
    
    # 创建图形
    plt.figure(figsize=(12, 6))
    
    # 计算速度
    speeds = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
    
    # 计算时间（秒）
    times = df['timestamp_ms'] / 1000
    
    # 绘制速度曲线
    plt.plot(times, speeds, 'b-', linewidth=2)
    
    # 添加平均速度线
    avg_speed = speeds.mean()
    plt.axhline(y=avg_speed, color='r', linestyle='--', linewidth=1.5, 
               label=f'平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)')
    
    # 添加图例和标题
    plt.legend(loc='upper right')
    plt.title('轨迹速度随时间变化')
    plt.xlabel('时间 (秒)')
    plt.ylabel('速度 (m/s)')
    plt.grid(True, alpha=0.3)
    
    # 调整Y轴范围
    plt.ylim(0, max(speeds) * 1.1)
    
    # 保存图形或显示
    if output_file:
        plt.savefig(output_file, dpi=300)
        print(f"速度-时间图已保存到: {output_file}")
    else:
        plt.tight_layout()
        plt.show()
    
    plt.close()

def visualize_trajectory_acceleration(trajectory_file, output_file=None):
    """可视化轨迹加速度随时间变化
    
    Args:
        trajectory_file: 轨迹文件路径
        output_file: 输出文件路径（可选）
    """
    # 加载轨迹数据
    df = pd.read_csv(trajectory_file)
    
    # 创建图形
    plt.figure(figsize=(12, 6))
    
    # 计算速度
    speeds = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
    
    # 计算时间（秒）
    times = df['timestamp_ms'] / 1000
    
    # 计算时间差
    time_diffs = np.diff(times)
    
    # 计算速度差
    speed_diffs = np.diff(speeds)
    
    # 计算加速度 (m/s²)
    accelerations = np.zeros_like(speeds)
    accelerations[1:] = speed_diffs / time_diffs
    
    # 绘制加速度曲线
    plt.plot(times, accelerations, 'g-', linewidth=2)
    
    # 添加平均加速度线
    avg_acceleration = np.mean(accelerations[1:])  # 跳过第一个点，因为它的加速度计算可能不准确
    plt.axhline(y=avg_acceleration, color='r', linestyle='--', linewidth=1.5, 
               label=f'平均加速度: {avg_acceleration:.2f} m/s²')
    
    # 添加零加速度线
    plt.axhline(y=0, color='k', linestyle='-', linewidth=0.5)
    
    # 添加图例和标题
    plt.legend(loc='upper right')
    plt.title('轨迹加速度随时间变化')
    plt.xlabel('时间 (秒)')
    plt.ylabel('加速度 (m/s²)')
    plt.grid(True, alpha=0.3)
    
    # 调整Y轴范围，使正负加速度能够清晰显示
    max_abs_acc = max(abs(np.max(accelerations)), abs(np.min(accelerations)))
    plt.ylim(-max_abs_acc * 1.1, max_abs_acc * 1.1)
    
    # 保存图形或显示
    if output_file:
        plt.savefig(output_file, dpi=300)
        print(f"加速度-时间图已保存到: {output_file}")
    else:
        plt.tight_layout()
        plt.show()
    
    plt.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='可视化轨迹数据')
    parser.add_argument('--trajectory', type=str, required=True,
                        help='轨迹文件路径')
    parser.add_argument('--path', type=str, default=None,
                        help='路径文件路径（可选）')
    parser.add_argument('--output_dir', type=str, default='./visualization_output',
                        help='输出目录')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取文件名（不含扩展名）
    base_name = os.path.splitext(os.path.basename(args.trajectory))[0]
    
    # 轨迹形状可视化
    shape_output = os.path.join(args.output_dir, f"{base_name}_shape.png")
    visualize_trajectory_shape(args.trajectory, args.path, shape_output)
    
    # 速度-时间可视化
    speed_output = os.path.join(args.output_dir, f"{base_name}_speed_time.png")
    visualize_trajectory_speed(args.trajectory, speed_output)
    
    # 加速度-时间可视化
    acceleration_output = os.path.join(args.output_dir, f"{base_name}_acceleration_time.png")
    visualize_trajectory_acceleration(args.trajectory, acceleration_output)
    
    print(f"可视化完成，输出目录: {args.output_dir}")

if __name__ == "__main__":
    main() 