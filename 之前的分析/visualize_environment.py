# visualize_environment.py
import rasterio
import matplotlib.pyplot as plt
import numpy as np
import os

# 定义文件路径
data_dir = "trajectory_generator/data/environment"
dem_path = os.path.join(data_dir, "dem_aligned.tif")
slope_path = os.path.join(data_dir, "slope_aligned.tif")
landcover_path = os.path.join(data_dir, "landcover_aligned.tif")
aspect_path = os.path.join(data_dir, "aspect_aligned.tif")
# 保存为不压缩的文件，可以考虑用新名字区分，或者直接覆盖
dem_processed_path = os.path.join(data_dir, "dem_aligned_processed_nocompress.tif")
landcover_processed_path = os.path.join(data_dir, "landcover_aligned_processed_nocompress.tif")

print(f"Checking DEM file: {dem_path}")

# --- DEM 处理 ---
try:
    with rasterio.open(dem_path) as src:
        dem_data = src.read(1)
        dem_profile = src.profile # 获取原始 profile
        crs = src.crs
        transform = src.transform

        print(f"DEM CRS: {crs}")
        print(f"DEM Transform: {transform}")
        print(f"DEM Shape: {dem_data.shape}")
        print(f"DEM Min value: {np.min(dem_data)}")
        print(f"DEM Max value: {np.max(dem_data)}")
        print(f"DEM NoData value: {src.nodata}")
        print(f"Original DEM Compression: {dem_profile.get('compress')}")

        # 检查是否存在 -9999
        dem_needs_processing = -9999 in dem_data
        if dem_needs_processing:
            print("Found -9999 in DEM data. Replacing with 0.")
            original_nodata = src.nodata
            if original_nodata == -9999:
                 print(f"Original DEM NoData value is -9999. Updating profile's NoData to 0.")
                 dem_profile.update(nodata=0)

            # 修改 profile 以禁用压缩
            print("Disabling compression for processed DEM.")
            dem_profile['compress'] = None # 或者 del dem_profile['compress'] 如果确定存在

            dem_data[dem_data == -9999] = 0
            print(f"Saving processed DEM (no compression) to: {dem_processed_path}")
            with rasterio.open(dem_processed_path, 'w', **dem_profile) as dst:
                dst.write(dem_data, 1)
            dem_to_visualize_path = dem_processed_path
            dem_title = "Processed DEM (NoComp, -9999->0)"
        else:
            print("No -9999 values found in DEM data. Using original.")
            dem_to_visualize_path = dem_path
            dem_title = "Original DEM"

    # --- Landcover 处理 ---
    print(f"\nChecking Landcover file: {landcover_path}")
    with rasterio.open(landcover_path) as src:
        landcover_data = src.read(1)
        landcover_profile = src.profile # 获取原始 profile

        print(f"Landcover Shape: {landcover_data.shape}")
        unique_values = np.unique(landcover_data)
        print(f"Original Landcover unique values: {unique_values}")
        print(f"Does original Landcover contain 0? {'Yes' if 0 in unique_values else 'No'}")
        print(f"Landcover NoData value: {src.nodata}")
        print(f"Original Landcover Compression: {landcover_profile.get('compress')}")

        # 检查是否存在 255
        landcover_needs_processing = 255 in unique_values
        if landcover_needs_processing:
            print("Found 255 in Landcover data. Replacing with 0.")
            original_nodata = src.nodata
            if original_nodata == 255:
                print(f"Original Landcover NoData value is 255. Updating profile's NoData to 0.")
                landcover_profile.update(nodata=0)

            # 修改 profile 以禁用压缩
            print("Disabling compression for processed Landcover.")
            landcover_profile['compress'] = None

            landcover_data_processed = landcover_data.copy()
            landcover_data_processed[landcover_data == 255] = 0
            print(f"Saving processed Landcover (no compression) to: {landcover_processed_path}")
            with rasterio.open(landcover_processed_path, 'w', **landcover_profile) as dst:
                dst.write(landcover_data_processed, 1)
            landcover_to_visualize_path = landcover_processed_path
            landcover_title = "Processed Landcover (NoComp, 255->0)"
        else:
            print("No 255 values found in Landcover data. Using original.")
            landcover_to_visualize_path = landcover_path
            landcover_title = "Original Landcover"


    # --- 可视化所有环境文件（包括处理后、无压缩的） ---
    files_to_visualize = {
        dem_title: dem_to_visualize_path,
        "Slope": slope_path,
        landcover_title: landcover_to_visualize_path,
        "Aspect": aspect_path
    }

    num_files = len(files_to_visualize)
    fig, axes = plt.subplots(1, num_files, figsize=(5 * num_files, 5))
    if num_files == 1: # Handle case with only one plot
        axes = [axes]

    print("\nVisualizing files...")
    i = 0
    for title, path in files_to_visualize.items():
        try:
            with rasterio.open(path) as src:
                cmap = 'tab20' if 'Landcover' in title else 'viridis'
                nodata = src.nodata
                data = src.read(1, masked=True)

                ax = axes[i]
                current_cmap = plt.get_cmap(cmap).copy()
                if nodata is not None:
                     current_cmap.set_bad(color='white', alpha=0)

                im = ax.imshow(data, cmap=current_cmap)
                ax.set_title(title)
                ax.set_xlabel("Longitude")
                ax.set_ylabel("Latitude")
                fig.colorbar(im, ax=ax, orientation='vertical', fraction=0.046, pad=0.04)
                print(f"  - Visualized: {os.path.basename(path)}")
            i += 1
        except Exception as e:
            print(f"Error visualizing {path}: {e}")

    plt.tight_layout()
    output_visualization_path = "environment_visualization_nocompress.png"
    plt.savefig(output_visualization_path)
    print(f"\nVisualization with uncompressed processed files saved to: {output_visualization_path}")


except ImportError:
    print("Error: rasterio or matplotlib is not installed.")
    print("Please install them in the 'wargame' environment using:")
    print("conda install -c conda-forge rasterio matplotlib")
except FileNotFoundError as e:
    print(f"Error: File not found - {e}")
except Exception as e:
    print(f"An error occurred: {e}") 