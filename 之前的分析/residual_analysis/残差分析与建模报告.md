# 残差分析与建模报告

## 总体残差分析

### 林地残差分析

- 样本数量: 32
- 均值: 0.0000
- 标准差: 0.5489
- 偏度: -0.0125
- 峰度: 0.3566
- Shapiro-W<PERSON>k正态性检验: 统计量=0.9867, p值=0.9535
- 最佳拟合分布: laplace
- 分布参数: (-0.0029519813519804927, 0.4091219259906759)

### 灌木地残差分析

- 样本数量: 246
- 均值: -0.0000
- 标准差: 0.6638
- 偏度: -1.0387
- 峰度: 4.2079
- Shapiro-Wilk正态性检验: 统计量=0.9267, p值=0.0000
- 最佳拟合分布: t
- 分布参数: (3.622015347631904, 0.036293773688441444, 0.4567715822734458)

### 水体残差分析

- 样本数量: 81
- 均值: 0.0000
- 标准差: 0.8705
- 偏度: -0.3412
- 峰度: -0.8890
- Shapiro-Wilk正态性检验: 统计量=0.9503, p值=0.0033
- 最佳拟合分布: gmm
- 高斯混合模型参数:
  - 成分1: 均值=-0.8065, 权重=0.4072, 方差=0.3710
  - 成分2: 均值=0.5541, 权重=0.5928, 方差=0.2694

## 残差建模建议

基于以上分析，我们提出以下残差建模方法：

### 1. 正态分布模型

对于近似正态分布的残差，可以使用正态分布模型：

```python
def generate_residual(land_type):
    mean = residual_params[land_type]['mean']
    std = residual_params[land_type]['std']
    return np.random.normal(mean, std)
```

### 2. 高斯混合模型

对于分布更复杂的残差，可以使用高斯混合模型：

```python
def generate_residual_gmm(land_type):
    # 选择一个高斯成分
    component = np.random.choice(len(gmm_params[land_type]['weights']), p=gmm_params[land_type]['weights'])
    # 从选定的成分生成
    mean = gmm_params[land_type]['means'][component]
    std = np.sqrt(gmm_params[land_type]['covariances'][component])
    return np.random.normal(mean, std)
```

### 3. 基于历史数据的残差抽样法

对于难以用参数分布拟合的残差，可以直接从历史残差中随机抽样：

```python
def generate_residual_sampling(land_type):
    # 从历史残差中随机抽样
    historical_residuals = all_residuals[land_type]
    return np.random.choice(historical_residuals)
```

### 4. 非参数核密度估计

使用核密度估计来模拟复杂的残差分布：

```python
def generate_residual_kde(land_type):
    # 使用KDE模型生成随机样本
    kde = kde_models[land_type]
    sample = kde.sample(1)[0][0]
    return sample
```

### 5. 整合到速度生成模型

将残差模型整合到速度生成中：

```python
def calculate_speed(slope, land_type):
    # 计算基础速度（线性模型）
    base_speed = models[land_type]['slope'] * slope + models[land_type]['intercept']
    
    # 生成随机残差
    residual = generate_residual(land_type)
    
    # 添加残差
    speed = base_speed + residual
    
    # 确保速度在合理范围内
    speed = max(min_speed, min(speed, max_speed))
    
    return speed
```