#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脚本名称: visualize_trajectory_speeds.py
===========================================

功能描述:
  绘制参考轨迹与其他轨迹的速度曲线对比图。

主要功能:
  1. 加载参考轨迹和其他轨迹数据
  2. 处理轨迹速度数据并对齐时间
  3. 为每个时间窗口创建包含四个子图的figure：
     - 子图1：参考轨迹vs生成轨迹
     - 子图2：参考轨迹vs核心轨迹2
     - 子图3：参考轨迹vs核心轨迹3
     - 子图4：参考轨迹vs核心轨迹4
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import matplotlib as mpl
from scipy.ndimage import gaussian_filter1d

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
plt.rcParams['font.size'] = 20  # 设置全局字体大小为20

# 定义输出目录
OUTPUT_DIR = 'trajectory_speed_comparison'
os.makedirs(OUTPUT_DIR, exist_ok=True)

def load_and_process_trajectory(file_path, is_core=True, window_size=1):
    """加载并处理轨迹数据
    
    Args:
        file_path: 轨迹文件路径
        is_core: 是否为core轨迹
        window_size: 时间窗口大小（秒）
        
    Returns:
        times: 时间数组
        speeds: 速度数组
    """
    print(f"加载轨迹: {file_path}")
    try:
        df = pd.read_csv(file_path)
        
        if is_core:
            # core轨迹时间处理
            df['time_s'] = (df['timestamp_ms'] - df['timestamp_ms'].iloc[0]) / 1000.0
            
            # 使用已有速度列或计算速度
            if 'velocity_2d_ms' in df.columns:
                speeds = df['velocity_2d_ms'].values
            else:
                speeds = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2).values
        else:
            # 生成轨迹时间处理
            df['time_s'] = df['timestamp_ms'] / 1000.0
            
            # 计算2D速度
            speeds = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2).values
        
        # 如果窗口大小大于1，则进行窗口聚合
        if window_size > 1:
            print(f"  应用{window_size}秒窗口聚合...")
            # 创建时间窗口
            df['window'] = (df['time_s'] / window_size).astype(int)
            
            # 按窗口聚合
            window_df = df.groupby('window').agg({
                'time_s': 'mean'
            }).reset_index()
            
            # 为每个窗口计算平均速度
            speeds_list = []
            for window_id in window_df['window']:
                window_mask = df['window'] == window_id
                window_speeds = speeds[window_mask]
                if len(window_speeds) > 0:
                    speeds_list.append(np.mean(window_speeds))
                else:
                    speeds_list.append(np.nan)
            
            times = window_df['time_s'].values
            speeds = np.array(speeds_list)
        else:
            times = df['time_s'].values
        
        print(f"  成功加载数据，长度: {len(times)}")
        return times, speeds
    except Exception as e:
        print(f"加载轨迹文件出错: {e}")
        import traceback
        traceback.print_exc()
        return np.array([0]), np.array([0])

def create_speed_comparison_figure(ref_traj_path, gen_trajs, core_trajs, window_size=1):
    """创建速度对比图，包含四个子图
    
    Args:
        ref_traj_path: 参考轨迹路径
        gen_trajs: 生成轨迹字典列表
        core_trajs: 核心轨迹字典列表
        window_size: 时间窗口大小（秒）
    """
    print(f"\n创建{window_size}秒窗口的对比图...")
    
    # 加载参考轨迹
    ref_times, ref_speeds = load_and_process_trajectory(ref_traj_path, True, window_size)
    
    # 创建2x2的子图布局
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    axes = axes.flatten()
    
    # 为每个子图设置标题和标签
    for i, ax in enumerate(axes):
        ax.set_xlabel('时间 (秒)', fontsize=20)
        ax.set_ylabel('速度 (m/s)', fontsize=20)
        ax.grid(True)
        ax.tick_params(axis='both', which='major', labelsize=18)
    
    # 子图1：参考轨迹vs生成轨迹
    axes[0].set_title(f'参考轨迹 vs 生成轨迹 ({window_size}秒窗口)', fontsize=20)
    axes[0].plot(ref_times, ref_speeds, 'b-', linewidth=3, label='参考轨迹 (Core 1)')
    
    # 颜色列表，用于区分不同生成轨迹
    colors = ['r', 'g', 'c', 'm', 'y', 'orange', 'purple', 'brown', 'pink']
    
    # 绘制生成轨迹（最多显示5条）
    for i, traj in enumerate(gen_trajs[:5]):
        color = colors[i % len(colors)]
        times, speeds = load_and_process_trajectory(traj['path'], False, window_size)
        
        if len(times) <= 1 or len(ref_times) <= 1:
            print(f"  跳过轨迹 {traj['name']}，数据点不足")
            continue
        
        # 如果时间长度不匹配，进行插值对齐
        try:
            min_time = max(times.min(), ref_times.min())
            max_time = min(times.max(), ref_times.max())
            
            # 创建新的时间点
            aligned_times = np.linspace(min_time, max_time, 1000)
            
            # 为参考轨迹和当前轨迹创建插值函数
            ref_interp = interp1d(ref_times, ref_speeds, bounds_error=False, fill_value=np.nan)
            traj_interp = interp1d(times, speeds, bounds_error=False, fill_value=np.nan)
            
            # 插值得到对应的速度值
            aligned_ref_speeds = ref_interp(aligned_times)
            aligned_speeds = traj_interp(aligned_times)
            
            # 绘制
            if i == 0:  # 只为第一条生成轨迹重新绘制参考轨迹
                axes[0].plot(aligned_times, aligned_ref_speeds, 'b-', linewidth=3, label='参考轨迹 (Core 1)')
            axes[0].plot(aligned_times, aligned_speeds, color=color, linestyle='-', linewidth=2,
                    alpha=0.8, label=traj['name'])
        except Exception as e:
            print(f"  绘制生成轨迹 {traj['name']} 时出错: {e}")
            import traceback
            traceback.print_exc()
    
    axes[0].legend(loc='upper right', fontsize=16)
    
    # 子图2-4：参考轨迹vs每条核心轨迹
    for i, traj in enumerate(core_trajs):
        if i >= 3:  # 最多处理3条核心轨迹
            break
            
        ax_idx = i + 1
        core_id = traj['name'].split()[-1]
        axes[ax_idx].set_title(f'参考轨迹 vs 核心轨迹 {core_id} ({window_size}秒窗口)', fontsize=20)
        
        times, speeds = load_and_process_trajectory(traj['path'], True, window_size)
        
        if len(times) <= 1 or len(ref_times) <= 1:
            print(f"  跳过轨迹 {traj['name']}，数据点不足")
            continue
        
        try:
            # 时间对齐与插值
            min_time = max(times.min(), ref_times.min())
            max_time = min(times.max(), ref_times.max())
            
            # 创建新的时间点
            aligned_times = np.linspace(min_time, max_time, 1000)
            
            # 为参考轨迹和当前轨迹创建插值函数
            ref_interp = interp1d(ref_times, ref_speeds, bounds_error=False, fill_value=np.nan)
            traj_interp = interp1d(times, speeds, bounds_error=False, fill_value=np.nan)
            
            # 插值得到对应的速度值
            aligned_ref_speeds = ref_interp(aligned_times)
            aligned_speeds = traj_interp(aligned_times)
            
            # 绘制
            axes[ax_idx].plot(aligned_times, aligned_ref_speeds, 'b-', linewidth=3, label='参考轨迹 (Core 1)')
            axes[ax_idx].plot(aligned_times, aligned_speeds, 'r-', linewidth=2, alpha=0.8, label=traj['name'])
        except Exception as e:
            print(f"  绘制核心轨迹 {traj['name']} 时出错: {e}")
            import traceback
            traceback.print_exc()
        
        axes[ax_idx].legend(loc='upper right', fontsize=16)
    
    # 调整子图之间的间距
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(OUTPUT_DIR, f'speed_comparison_{window_size}s_window.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"速度对比图({window_size}秒窗口)已保存到: {output_file}")
    
    plt.close(fig)

def main():
    # 参考轨迹
    ref_traj = '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/core_trajectories/converted_sequence_1_core.csv'
    
    # 其他核心轨迹
    core_trajs = []
    for i in [2, 3, 4]:
        path = f'/home/<USER>/data/Sucess_or_Die/ai_agent_generation/core_trajectories/converted_sequence_{i}_core.csv'
        if os.path.exists(path):
            core_trajs.append({
                'name': f'核心轨迹 {i}',
                'path': path,
                'is_core': True
            })
    
    # 生成的轨迹（选取几条）
    gen_dir = '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories'
    gen_files = [f for f in os.listdir(gen_dir) if f.endswith('_high_mobility.csv')][:5]
    
    gen_trajs = []
    for file in gen_files:
        path = os.path.join(gen_dir, file)
        if os.path.exists(path):
            traj_id = file.replace('trajectory_', '').replace('_high_mobility.csv', '')
            gen_trajs.append({
                'name': f'生成轨迹 {traj_id}',
                'path': path,
                'is_core': False
            })
    
    print(f"加载了{len(core_trajs)}条核心轨迹和{len(gen_trajs)}条生成轨迹")
    
    # 为每个时间窗口创建对比图
    for window_size in [1, 5, 10, 30, 60]:
        create_speed_comparison_figure(ref_traj, gen_trajs, core_trajs, window_size)

if __name__ == "__main__":
    main() 