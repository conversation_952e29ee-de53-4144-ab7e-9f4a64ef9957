# 真实环境数据与轨迹速度关系分析报告

## 模型拟合结果总结

### 不同聚合窗口的R²对比

| 聚合窗口 | 林地R² | 灌木地R² | 水体R² | 平均R² |
|---------|-------|---------|-------|-------|
| 1秒 | 0.0015 | 0.2637 | 0.0011 | 0.0888 |
| 5秒 | 0.0016 | 0.2749 | 0.0048 | 0.0937 |
| 10秒 | 0.0138 | 0.2965 | 0.0001 | 0.1035 |
| 20秒 | 0.0027 | 0.3281 | nan | 0.1654 |
| 30秒 | 0.0054 | 0.3618 | nan | 0.1836 |
| 60秒 | nan | 0.4546 | nan | 0.4546 |

## 最佳模型参数

### 林地 (最佳窗口: 10秒)

**线性模型:** y = 0.0141x + 5.6992 (R² = 0.0138)

**残差分布:** t分布 (df=2.9673, μ=0.0977, σ=0.4773)

### 灌木地 (最佳窗口: 60秒)

**线性模型:** y = -0.1536x + 6.4415 (R² = 0.4546)

**残差分布:** t分布 (df=6391482.1610, μ=-0.0001, σ=1.5470)

### 水体 (最佳窗口: 5秒)

**线性模型:** y = 0.0099x + 6.1615 (R² = 0.0048)

**残差分布:** 拉普拉斯分布 (μ=0.0540, b=0.3590)

## 总结与建议

1. 基于R²评估，最佳聚合窗口为 **60秒**
2. 不同土地类型对速度-坡度关系有显著影响，应分别建模
3. 残差分布因土地类型而异，应采用不同的随机模型模拟误差

## 实现建议

```python
def generate_speed(slope, land_type):
    """根据坡度和土地类型生成速度

    参数:
    - slope: 坡度
    - land_type: 土地类型（"林地", "灌木地", "水体"）

    返回:
    - speed: 生成的速度（米/秒）
    """
    import numpy as np
    from scipy import stats

    # 线性模型参数
    LINEAR_MODELS = {
        "林地": (0.014053, 5.699195),  # 斜率, 截距
        "灌木地": (-0.153633, 6.441540),  # 斜率, 截距
        "水体": (0.009883, 6.161541),  # 斜率, 截距
    }

    # 残差模型参数
    RESIDUAL_MODELS = {
        "林地": {"dist": "t", "params": (2.967345, 0.097660, 0.477329)},  # df, μ, σ
        "灌木地": {"dist": "t", "params": (6391482.161046, -0.000092, 1.546952)},  # df, μ, σ
        "水体": {"dist": "laplace", "params": (0.053969, 0.359034)},  # μ, b
    }

    # 获取线性模型参数
    if land_type not in LINEAR_MODELS:
        land_type = "林地"  # 默认使用林地模型
    slope_coef, intercept = LINEAR_MODELS[land_type]

    # 计算基础速度
    base_speed = slope_coef * slope + intercept

    # 生成残差
    if land_type in RESIDUAL_MODELS:
        residual_model = RESIDUAL_MODELS[land_type]
        dist_type = residual_model["dist"]
        params = residual_model["params"]

        if dist_type == "normal":
            loc, scale = params
            residual = np.random.normal(loc, scale)
        elif dist_type == "laplace":
            loc, scale = params
            residual = np.random.laplace(loc, scale)
        elif dist_type == "t":
            df, loc, scale = params
            residual = loc + scale * np.random.standard_t(df)
        elif dist_type == "gmm":
            # 从GMM中采样
            weights = params["weights"]
            means = params["means"]
            covars = params["covars"]
            
            # 选择一个组件
            component = np.random.choice(len(weights), p=weights)
            # 从选定的组件生成样本
            residual = np.random.normal(means[component], np.sqrt(covars[component]))
        else:
            residual = 0.0
    else:
        residual = 0.0

    # 添加残差到基础速度
    speed = base_speed + residual

    # 确保速度在合理范围内
    speed = max(0.5, min(10.0, speed))

    return speed
```
