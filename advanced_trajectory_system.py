#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级轨迹生成系统 - 主程序
整合四个阶段：数据预处理、路径规划、控制预测、轨迹仿真

使用方法：
1. 训练模式：训练深度学习控制预测器
2. 生成模式：生成新的轨迹
3. 评估模式：评估生成质量
"""

import os
import argparse
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader, random_split
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from pathlib import Path
import json
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_preprocessing import (
    HighResolutionEnvironmentMaps, 
    TrajectoryFeatureExtractor, 
    batch_process_trajectories
)
from path_planning import (
    HierarchicalAStar, 
    ScenarioType, 
    plan_multiple_paths
)
from control_predictor import (
    TransformerControlPredictor,
    ModelConfig,
    TrajectoryDataset,
    ControlPredictorTrainer,
    sample_from_mdn
)
from trajectory_simulator import (
    TrajectorySimulator,
    VehicleConfig,
    SimulationConfig,
    visualize_trajectory
)

class AdvancedTrajectorySystem:
    """高级轨迹生成系统"""
    
    def __init__(self, config_file: str = "system_config.json"):
        """
        初始化系统
        
        Args:
            config_file: 系统配置文件路径
        """
        # 加载配置
        self.config = self._load_config(config_file)
        
        # 系统组件
        self.env_maps = None
        self.control_predictor = None
        self.path_planner = None
        self.trajectory_simulator = None
        
        # 数据路径
        self.env_data_dir = self.config['data']['environment_dir']
        self.trajectory_data_dir = self.config['data']['trajectory_dir']
        self.output_dir = self.config['data']['output_dir']
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🚀 高级轨迹生成系统初始化完成")
        
    def _load_config(self, config_file: str) -> Dict:
        """加载系统配置"""
        default_config = {
            "data": {
                "environment_dir": "trajectory_generation_module_pkg/examples/data/environment",
                "trajectory_dir": "core_trajectories",
                "output_dir": "advanced_trajectory_output",
                "processed_trajectory_dir": "processed_trajectories"
            },
            "preprocessing": {
                "target_resolution": 5.0,
                "time_step": 0.25
            },
            "model": {
                "sequence_length": 20,
                "hidden_dim": 256,
                "num_layers": 4,
                "num_heads": 8,
                "dropout": 0.1,
                "learning_rate": 1e-4,
                "batch_size": 32,
                "max_epochs": 100
            },
            "simulation": {
                "time_step": 0.1,
                "max_simulation_time": 3600.0
            },
            "vehicle_configs": {
                "standard": {
                    "max_speed": 25.0,
                    "max_acceleration": 3.0,
                    "vehicle_type": "standard"
                },
                "high_mobility": {
                    "max_speed": 35.0,
                    "max_acceleration": 5.0,
                    "vehicle_type": "high_mobility"
                },
                "stealth": {
                    "max_speed": 15.0,
                    "max_acceleration": 2.0,
                    "vehicle_type": "stealth"
                },
                "mountain": {
                    "max_speed": 20.0,
                    "max_acceleration": 2.5,
                    "vehicle_type": "mountain"
                }
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        else:
            # 保存默认配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            print(f"创建默认配置文件: {config_file}")
            
        return default_config
        
    def initialize_components(self):
        """初始化系统组件"""
        print("\n📍 阶段1：初始化环境地图...")
        
        # 初始化环境地图
        self.env_maps = HighResolutionEnvironmentMaps(
            self.env_data_dir,
            use_original_resolution=True
        )
        
        # 初始化路径规划器
        print("\n🗺️  阶段2：初始化路径规划器...")
        self.path_planner = HierarchicalAStar(self.env_maps, ScenarioType.STANDARD)
        
        print("✅ 系统组件初始化完成")
        
    def preprocess_trajectories(self, force_reprocess: bool = False):
        """预处理轨迹数据"""
        processed_dir = self.config['data']['processed_trajectory_dir']
        
        if os.path.exists(processed_dir) and not force_reprocess:
            print(f"发现已处理的轨迹数据: {processed_dir}")
            return
            
        print("\n🔄 阶段1：轨迹数据预处理...")
        
        # 查找轨迹文件
        trajectory_files = []
        traj_dir = Path(self.trajectory_data_dir)
        
        for pattern in ['*.csv']:
            trajectory_files.extend(list(traj_dir.glob(pattern)))
            
        if not trajectory_files:
            raise FileNotFoundError(f"在 {self.trajectory_data_dir} 中未找到轨迹文件")
            
        trajectory_files = [str(f) for f in trajectory_files]
        print(f"找到 {len(trajectory_files)} 个轨迹文件")
        
        # 批量处理
        batch_process_trajectories(
            trajectory_files,
            self.env_data_dir,
            processed_dir,
            use_original_resolution=True,
            time_step=self.config['preprocessing']['time_step']
        )
        
    def train_control_predictor(self):
        """训练控制预测器"""
        print("\n🧠 阶段3：训练深度学习控制预测器...")
        
        # 加载预处理数据
        processed_dir = self.config['data']['processed_trajectory_dir']
        if not os.path.exists(processed_dir):
            raise FileNotFoundError(f"未找到预处理数据: {processed_dir}")
            
        # 读取所有预处理轨迹
        processed_files = list(Path(processed_dir).glob('*.csv'))
        trajectories = []
        
        for file_path in processed_files:
            try:
                df = pd.read_csv(file_path)
                if len(df) > self.config['model']['sequence_length']:
                    trajectories.append(df)
                    print(f"加载轨迹: {file_path.name}, 点数: {len(df)}")
            except Exception as e:
                print(f"加载 {file_path} 失败: {e}")
                
        if not trajectories:
            raise ValueError("没有找到有效的预处理轨迹数据")
            
        print(f"成功加载 {len(trajectories)} 条轨迹")
        
        # 创建模型配置
        model_config = ModelConfig(
            sequence_length=self.config['model']['sequence_length'],
            hidden_dim=self.config['model']['hidden_dim'],
            num_layers=self.config['model']['num_layers'],
            num_heads=self.config['model']['num_heads'],
            dropout=self.config['model']['dropout'],
            learning_rate=self.config['model']['learning_rate'],
            batch_size=self.config['model']['batch_size'],
            max_epochs=self.config['model']['max_epochs']
        )
        
        # 创建数据集
        print("创建训练数据集...")
        dataset = TrajectoryDataset(trajectories, model_config)
        
        # 划分训练和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset, 
            batch_size=model_config.batch_size, 
            shuffle=True
        )
        val_loader = DataLoader(
            val_dataset, 
            batch_size=model_config.batch_size, 
            shuffle=False
        )
        
        print(f"训练集样本数: {len(train_dataset)}")
        print(f"验证集样本数: {len(val_dataset)}")
        
        # 初始化训练器
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {device}")
        
        trainer = ControlPredictorTrainer(model_config, device)
        
        # 开始训练
        print("开始训练...")
        history = trainer.train(train_loader, val_loader)
        
        # 保存模型
        model_path = os.path.join(self.output_dir, 'control_predictor.pth')
        torch.save(trainer.model.state_dict(), model_path)
        
        # 保存配置
        config_path = os.path.join(self.output_dir, 'model_config.json')
        with open(config_path, 'w') as f:
            json.dump(model_config.__dict__, f, indent=2)
            
        # 保存训练历史
        history_path = os.path.join(self.output_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(history, f, indent=2)
            
        print(f"✅ 模型训练完成，保存到: {model_path}")
        
        # 保存模型引用
        self.control_predictor = trainer.model
        self.model_config = model_config
        
    def load_trained_model(self, model_path: Optional[str] = None):
        """加载训练好的模型"""
        if model_path is None:
            model_path = os.path.join(self.output_dir, 'control_predictor.pth')
            
        config_path = os.path.join(self.output_dir, 'model_config.json')
        
        if not os.path.exists(model_path) or not os.path.exists(config_path):
            raise FileNotFoundError("未找到训练好的模型，请先运行训练")
            
        # 加载配置
        with open(config_path, 'r') as f:
            config_dict = json.load(f)
            
        self.model_config = ModelConfig(**config_dict)
        
        # 加载模型
        self.control_predictor = TransformerControlPredictor(self.model_config)
        self.control_predictor.load_state_dict(torch.load(model_path, map_location='cpu'))
        self.control_predictor.eval()
        
        print(f"✅ 模型加载完成: {model_path}")
        
    def generate_trajectories(self, 
                            start_point: Tuple[float, float],
                            end_point: Tuple[float, float],
                            vehicle_type: str = "standard",
                            scenario: str = "standard",
                            num_samples: int = 3,
                            visualize: bool = True):
        """生成轨迹"""
        print(f"\n🎯 阶段4：生成轨迹 ({vehicle_type}, {scenario})")
        print(f"起点: {start_point}")
        print(f"终点: {end_point}")
        
        # 确保模型已加载
        if self.control_predictor is None:
            self.load_trained_model()
            
        # 阶段2：路径规划
        print("进行路径规划...")
        scenario_type = ScenarioType(scenario)
        planner = HierarchicalAStar(self.env_maps, scenario_type)
        reference_path = planner.plan_path(start_point, end_point)
        
        if not reference_path:
            raise RuntimeError("路径规划失败")
            
        print(f"路径规划完成，路径点数: {len(reference_path)}")
        
        # 配置车辆
        vehicle_config_dict = self.config['vehicle_configs'][vehicle_type]
        vehicle_config = VehicleConfig(
            vehicle_type=vehicle_type,
            initial_position=start_point,
            initial_velocity=(5.0, 0.0),  # 初始速度
            initial_heading=0.0,
            max_speed=vehicle_config_dict['max_speed'],
            max_acceleration=vehicle_config_dict['max_acceleration']
        )
        
        # 配置仿真
        sim_config = SimulationConfig(
            time_step=self.config['simulation']['time_step'],
            max_simulation_time=self.config['simulation']['max_simulation_time']
        )
        
        # 阶段4：轨迹仿真
        print("初始化轨迹仿真器...")
        simulator = TrajectorySimulator(
            self.control_predictor,
            self.env_maps,
            vehicle_config,
            sim_config,
            self.model_config
        )
        
        print(f"生成 {num_samples} 条轨迹样本...")
        trajectories = simulator.generate_trajectory(
            reference_path,
            num_samples=num_samples,
            random_seed=42
        )
        
        # 保存结果
        results = {
            'reference_path': reference_path,
            'trajectories': trajectories,
            'config': {
                'vehicle_type': vehicle_type,
                'scenario': scenario,
                'start_point': start_point,
                'end_point': end_point
            }
        }
        
        # 保存轨迹数据
        for i, traj in enumerate(trajectories):
            output_file = os.path.join(
                self.output_dir, 
                f'generated_trajectory_{vehicle_type}_{scenario}_{i:02d}.csv'
            )
            traj.to_csv(output_file, index=False)
            print(f"保存轨迹 {i+1}: {output_file}")
            
        # 可视化
        if visualize and trajectories:
            for i, traj in enumerate(trajectories):
                viz_file = os.path.join(
                    self.output_dir,
                    f'trajectory_visualization_{vehicle_type}_{scenario}_{i:02d}.png'
                )
                
                plt.style.use('default')
                visualize_trajectory(traj, reference_path, self.env_maps, viz_file)
                
        print(f"✅ 轨迹生成完成，共 {len(trajectories)} 条轨迹")
        return results
        
    def evaluate_system(self):
        """评估系统性能"""
        print("\n📊 系统评估...")
        
        # 这里可以添加评估逻辑
        # 例如：与真实轨迹对比、多样性分析等
        
        print("✅ 系统评估完成")
        
    def run_full_pipeline(self, 
                         start_point: Tuple[float, float],
                         end_point: Tuple[float, float],
                         vehicle_type: str = "standard",
                         scenario: str = "standard",
                         force_retrain: bool = False):
        """运行完整流水线"""
        print("🌟 启动高级轨迹生成系统完整流水线")
        
        # 初始化组件
        self.initialize_components()
        
        # 预处理轨迹
        self.preprocess_trajectories()
        
        # 训练或加载模型
        model_path = os.path.join(self.output_dir, 'control_predictor.pth')
        if force_retrain or not os.path.exists(model_path):
            self.train_control_predictor()
        else:
            self.load_trained_model()
            
        # 生成轨迹
        results = self.generate_trajectories(
            start_point, end_point, vehicle_type, scenario, num_samples=3
        )
        
        # 评估
        self.evaluate_system()
        
        print("🎉 完整流水线运行完成！")
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高级轨迹生成系统')
    
    parser.add_argument('--mode', choices=['train', 'generate', 'full'], 
                       default='full', help='运行模式')
    parser.add_argument('--start', nargs=2, type=float, 
                       default=[500000, 4000000], help='起点坐标 (UTM)')
    parser.add_argument('--end', nargs=2, type=float,
                       default=[510000, 4010000], help='终点坐标 (UTM)')
    parser.add_argument('--vehicle-type', choices=['standard', 'high_mobility', 'stealth', 'mountain'],
                       default='standard', help='车辆类型')
    parser.add_argument('--scenario', choices=['standard', 'high_mobility', 'stealth_priority', 'mountain_special'],
                       default='standard', help='场景类型')
    parser.add_argument('--num-samples', type=int, default=3, help='生成轨迹数量')
    parser.add_argument('--force-retrain', action='store_true', help='强制重新训练模型')
    parser.add_argument('--config', default='system_config.json', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 创建系统实例
    system = AdvancedTrajectorySystem(args.config)
    
    try:
        if args.mode == 'train':
            system.initialize_components()
            system.preprocess_trajectories()
            system.train_control_predictor()
            
        elif args.mode == 'generate':
            system.initialize_components()
            system.generate_trajectories(
                tuple(args.start), tuple(args.end),
                args.vehicle_type, args.scenario, args.num_samples
            )
            
        elif args.mode == 'full':
            system.run_full_pipeline(
                tuple(args.start), tuple(args.end),
                args.vehicle_type, args.scenario, args.force_retrain
            )
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main()) 