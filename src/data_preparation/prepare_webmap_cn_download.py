 
import math

# 1. Define the regions of interest with their bounding boxes
# BBoxes are from our previous work, format: [lon_min, lat_min, lon_max, lat_max]
REGIONS = {
    "scottish_highlands": {"bbox": [-6, 56.6, -4, 58.4]},
    "israel_palestine": {"bbox": [33.75, 30.6, 35.85, 32.4]},
    "kashmir": {"bbox": [74.95, 33.1, 77.05, 34.9]},
    "gulf_war_kuwait": {"bbox": [46.9, 28.4, 48.9, 30.2]},
    "donbas_ukraine_russia": {"bbox": [36.65, 47.4, 39.35, 49.2]},
}

def get_tile_name(lon_index, lat_index):
    """
    Generates the tile name based on the custom grid system.
    Example format: N43_30
    """
    # According to the screenshot, latitude can go up to 35, which is 2 digits.
    # The user example is N43_30.
    return f"N{lon_index}_{lat_index}"

def calculate_tiles_for_bbox(bbox):
    """
    Calculates the list of required tile names for a given bounding box.
    """
    lon_min, lat_min, lon_max, lat_max = bbox
    tiles = set()

    # --- Calculate Latitude Indices ---
    # The latitude index is the northern edge of the 5-degree tile.
    # A tile with lat_index Y covers the latitude range [Y-5, Y].
    # We need to find all tiles that intersect with [lat_min, lat_max].
    start_lat_index = math.ceil(lat_min / 5.0) * 5
    end_lat_index = math.ceil(lat_max / 5.0) * 5
    
    # --- Calculate Longitude Indices ---
    # The longitude index 'XX' is calculated by: XX = floor(lon / 5) + 29
    # A tile with lon_index X covers [5*X - 145, 5*X - 140].
    # We need to find all tiles that intersect with [lon_min, lon_max].
    start_lon_index = math.floor(lon_min / 5.0) + 29
    end_lon_index = math.floor(lon_max / 5.0) + 29

    # Generate all tile names within the calculated index ranges
    # The loops need to include the end_..._index
    for lat_idx in range(start_lat_index, end_lat_index + 5, 5):
        for lon_idx in range(start_lon_index, end_lon_index + 1, 1):
            tiles.add(get_tile_name(lon_idx, lat_idx))
            
    return sorted(list(tiles))

def main():
    """
    Main function to generate and print the tile list for all regions.
    """
    print("--- 瓦片下载清单 (Tile Download List) ---\n")
    all_tiles = set()

    for region_name, data in REGIONS.items():
        print(f"区域 (Region): {region_name}")
        region_tiles = calculate_tiles_for_bbox(data["bbox"])
        print(f"  瓦片列表 (Tiles): {', '.join(region_tiles)}")
        print("-" * 20)
        all_tiles.update(region_tiles)
    
    print("\n--- 合并后的总清单 (Consolidated List) ---")
    print(f"总计需要下载 {len(all_tiles)} 个独立瓦片。")
    print("All unique tiles required:")
    # Print in a more readable format, e.g., 5 tiles per line
    sorted_total_list = sorted(list(all_tiles))
    for i in range(0, len(sorted_total_list), 5):
         print("  " + "  ".join(sorted_total_list[i:i+5]))
         
    print("\n请根据此清单在 https://www.webmap.cn/ 上进行查询和下载。")


if __name__ == "__main__":
    main() 