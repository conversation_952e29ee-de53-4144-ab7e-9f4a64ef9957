 
import os
import math

# 1. 定义我们感兴趣的区域及其边界框
REGIONS = {
    "scottish_highlands": {"bbox": [-6, 56.6, -4, 58.4]},
    "israel_palestine": {"bbox": [33.75, 30.6, 35.85, 32.4]},
    "kashmir": {"bbox": [74.95, 33.1, 77.05, 34.9]},
    "gulf_war_kuwait": {"bbox": [46.9, 28.4, 48.9, 30.2]},
    "donbas_ukraine_russia": {"bbox": [36.65, 47.4, 39.35, 49.2]},
}

# 2. 用户提供的LULC文件列表
# 我们从文件名中提取瓦片ID，例如从 'N30_55_2010LC030' 提取 'N30_55'
PROVIDED_FILES_FULL = [
    "N30_55_2010LC030",
    "N36_30_2010LC030",
    "N36_45_2010LC030",
    "N38_30_2010LC030",
    "N43_30_2010LC030",
]
# 提取瓦片ID用于匹配
PROVIDED_TILES = {f.split('_')[0] + '_' + f.split('_')[1] for f in PROVIDED_FILES_FULL}


def get_required_tile_name(lon_index, lat_index):
    """根据索引生成瓦片名称，例如 'N43_30'"""
    return f"N{lon_index}_{lat_index}"

def calculate_required_tiles_for_bbox(bbox):
    """
    根据经纬度边界框计算所有需要的瓦片名称 (基于webmap.cn的格网系统)
    """
    lon_min, lat_min, lon_max, lat_max = bbox
    tiles = set()

    # 纬度索引是瓦片上边缘，是5的倍数
    start_lat_index = math.ceil(lat_min / 5.0) * 5
    end_lat_index = math.ceil(lat_max / 5.0) * 5
    
    # 经度索引是 floor(lon / 5) + 29
    start_lon_index = math.floor(lon_min / 5.0) + 29
    end_lon_index = math.floor(lon_max / 5.0) + 29

    for lat_idx in range(start_lat_index, end_lat_index + 5, 5):
        for lon_idx in range(start_lon_index, end_lon_index + 1, 1):
            tiles.add(get_required_tile_name(lon_idx, lat_idx))
            
    return tiles

# --- 主程序 ---

def verify_lulc_coverage():
    """主函数，检查用户提供的LULC文件是否满足所有区域的需求"""
    print("--- LULC 地表覆盖数据验证报告 ---\n")
    print(f"已收到 {len(PROVIDED_TILES)} 个LULC文件:")
    print(f"  {', '.join(sorted(list(PROVIDED_TILES)))}\n")
    print("-" * 35)

    all_regions_covered = True
    total_missing_tiles = set()

    for region_name, data in REGIONS.items():
        print(f"正在分析区域: {region_name}")
        required_tiles = calculate_required_tiles_for_bbox(data["bbox"])
        print(f"  - 需要的瓦片: {', '.join(sorted(list(required_tiles)))}")

        missing_files = required_tiles - PROVIDED_TILES
        
        if not missing_files:
            print("  - 状态: \033[92m数据完整\033[0m\n")
        else:
            print(f"  - 状态: \033[91m数据缺失\033[0m")
            print(f"  - 缺失的瓦片: {', '.join(sorted(list(missing_files)))}\n")
            all_regions_covered = False
            total_missing_tiles.update(missing_files)
    
    print("-" * 35)
    # 总结
    if all_regions_covered:
        print("\033[1m\033[92m结论: 您提供的LULC文件完整覆盖了所有研究区域的需求！\033[0m")
    else:
        print("\033[1m\033[91m结论: LULC数据不完整。\033[0m")
        print("总共缺失以下瓦片:")
        sorted_missing = sorted(list(total_missing_tiles))
        for i in range(0, len(sorted_missing), 5):
             print("  " + "  ".join(sorted_missing[i:i+5]))

if __name__ == "__main__":
    verify_lulc_coverage() 