import ee
import os

# 定义我们感兴趣的五个区域
# 每个区域都是一个200km x 200km的矩形框
REGIONS = [
    {
        "name": "Scottish_Highlands",
        "bbox": [-6, 56.6, -4, 58.4]  # [lon_min, lat_min, lon_max, lat_max]
    },
    {
        "name": "Israel_Palestine",
        "bbox": [33.75, 30.6, 35.85, 32.4]
    },
    {
        "name": "Kashmir",
        "bbox": [74.95, 33.1, 77.05, 34.9]
    },
    {
        "name": "Kuwait_Gulf_War",
        "bbox": [46.9, 28.4, 48.9, 30.2]
    },
    {
        "name": "Donbas_Ukraine",
        "bbox": [36.65, 47.4, 39.35, 49.2]
    }
]

# ESA WorldCover 2020 v100 的地表覆盖类型原始值
# https://developers.google.com/earth-engine/datasets/catalog/ESA_WorldCover_v100
# 我们需要将它们重新映射到项目要求的分类值
# from: [10, 20, 30, 40, 50, 60, 80, 90]
# to:   [80, 40, 30, 60, 50, 90, 10, 20]
# GEE原始值 -> 项目要求值
# 10: Tree cover (森林) -> 80
# 20: Shrubland (灌木地) -> 40
# 30: Grassland (草地) -> 30
# 40: Cropland (农田) -> 60
# 50: Built-up (建筑用地) -> 50
# 60: Barren / sparse vegetation (荒地) -> 90
# 80: Open water (水域) -> 10
# 90: Herbaceous wetland (湿地) -> 20
# 其他所有类型 (如冰雪、苔藓等) 将被映射为 "未分类" (255)
REMAP_FROM = [10, 20, 30, 40, 50, 60, 80, 90]
REMAP_TO =   [80, 40, 30, 60, 50, 90, 10, 20]


def main():
    """
    主函数，用于初始化GEE、处理并导出数据。
    """
    try:
        # 首先，尝试进行认证。
        # 我们强制使用 'notebook' 模式，以确保它生成一个URL，而不是去查找gcloud。
        ee.Authenticate(auth_mode='notebook')

        # 初始化 Google Earth Engine
        # 这会使用您刚刚通过浏览器授权创建的认证信息
        ee.Initialize(auth_mode='notebook')
        print("Google Earth Engine 初始化成功！")
    except Exception as e:
        print("Google Earth Engine 初始化或认证失败。")
        print("请仔细检查您是否正确地复制了授权链接并在浏览器中授权，然后将授权码粘贴回终端。")
        print(f"错误详情: {e}")
        return

    # 加载全球DEM数据集 (SRTM 30米分辨率)
    dem_image = ee.Image("USGS/SRTMGL1_003")

    # 加载全球地表覆盖数据集 (ESA WorldCover 10米分辨率)
    lulc_image = ee.Image("ESA/WorldCover/v100").select('Map')

    # 对地表覆盖数据进行重分类
    lulc_remapped = lulc_image.remap(REMAP_FROM, REMAP_TO, defaultValue=255).rename('lulc')

    # 循环处理并导出每个区域的数据
    for region in REGIONS:
        region_name = region["name"]
        geometry = ee.Geometry.Rectangle(region["bbox"])
        print(f"开始处理区域: {region_name}...")

        # --- 导出DEM数据 ---
        export_params_dem = {
            'image': dem_image.clip(geometry),
            'description': f'{region_name}_DEM',
            'folder': 'GEE_Exports',  # Google Drive 中的文件夹名称
            'fileNamePrefix': f'{region_name}_DEM',
            'scale': 30,  # 分辨率 (米)
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_dem = ee.batch.Export.image.toDrive(**export_params_dem)
        task_dem.start()
        print(f"  -> 已启动DEM数据导出任务: {task_dem.id}")

        # --- 导出地表覆盖(LULC)数据 ---
        export_params_lulc = {
            'image': lulc_remapped.clip(geometry),
            'description': f'{region_name}_LULC',
            'folder': 'GEE_Exports',
            'fileNamePrefix': f'{region_name}_LULC',
            'scale': 30, # 同样使用30米以匹配DEM
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_lulc = ee.batch.Export.image.toDrive(**export_params_lulc)
        task_lulc.start()
        print(f"  -> 已启动地表覆盖数据导出任务: {task_lulc.id}")

    print("\n所有区域的处理任务均已启动！")
    print("请注意：实际的数据处理和导出在Google的云端进行，这可能需要几分钟到几十分钟不等。")
    print("您可以前往以下网址监控任务状态：")
    print("https://code.earthengine.google.com/tasks")
    print("任务完成后，文件将出现在您Google Drive的 'GEE_Exports' 文件夹中。")


if __name__ == '__main__':
    main() 
import os

# 定义我们感兴趣的五个区域
# 每个区域都是一个200km x 200km的矩形框
REGIONS = [
    {
        "name": "Scottish_Highlands",
        "bbox": [-6, 56.6, -4, 58.4]  # [lon_min, lat_min, lon_max, lat_max]
    },
    {
        "name": "Israel_Palestine",
        "bbox": [33.75, 30.6, 35.85, 32.4]
    },
    {
        "name": "Kashmir",
        "bbox": [74.95, 33.1, 77.05, 34.9]
    },
    {
        "name": "Kuwait_Gulf_War",
        "bbox": [46.9, 28.4, 48.9, 30.2]
    },
    {
        "name": "Donbas_Ukraine",
        "bbox": [36.65, 47.4, 39.35, 49.2]
    }
]

# ESA WorldCover 2020 v100 的地表覆盖类型原始值
# https://developers.google.com/earth-engine/datasets/catalog/ESA_WorldCover_v100
# 我们需要将它们重新映射到项目要求的分类值
# from: [10, 20, 30, 40, 50, 60, 80, 90]
# to:   [80, 40, 30, 60, 50, 90, 10, 20]
# GEE原始值 -> 项目要求值
# 10: Tree cover (森林) -> 80
# 20: Shrubland (灌木地) -> 40
# 30: Grassland (草地) -> 30
# 40: Cropland (农田) -> 60
# 50: Built-up (建筑用地) -> 50
# 60: Barren / sparse vegetation (荒地) -> 90
# 80: Open water (水域) -> 10
# 90: Herbaceous wetland (湿地) -> 20
# 其他所有类型 (如冰雪、苔藓等) 将被映射为 "未分类" (255)
REMAP_FROM = [10, 20, 30, 40, 50, 60, 80, 90]
REMAP_TO =   [80, 40, 30, 60, 50, 90, 10, 20]


def main():
    """
    主函数，用于初始化GEE、处理并导出数据。
    """
    try:
        # 首先，尝试进行认证。
        # 我们强制使用 'notebook' 模式，以确保它生成一个URL，而不是去查找gcloud。
        ee.Authenticate(auth_mode='notebook')

        # 初始化 Google Earth Engine
        # 这会使用您刚刚通过浏览器授权创建的认证信息
        ee.Initialize(auth_mode='notebook')
        print("Google Earth Engine 初始化成功！")
    except Exception as e:
        print("Google Earth Engine 初始化或认证失败。")
        print("请仔细检查您是否正确地复制了授权链接并在浏览器中授权，然后将授权码粘贴回终端。")
        print(f"错误详情: {e}")
        return

    # 加载全球DEM数据集 (SRTM 30米分辨率)
    dem_image = ee.Image("USGS/SRTMGL1_003")

    # 加载全球地表覆盖数据集 (ESA WorldCover 10米分辨率)
    lulc_image = ee.Image("ESA/WorldCover/v100").select('Map')

    # 对地表覆盖数据进行重分类
    lulc_remapped = lulc_image.remap(REMAP_FROM, REMAP_TO, defaultValue=255).rename('lulc')

    # 循环处理并导出每个区域的数据
    for region in REGIONS:
        region_name = region["name"]
        geometry = ee.Geometry.Rectangle(region["bbox"])
        print(f"开始处理区域: {region_name}...")

        # --- 导出DEM数据 ---
        export_params_dem = {
            'image': dem_image.clip(geometry),
            'description': f'{region_name}_DEM',
            'folder': 'GEE_Exports',  # Google Drive 中的文件夹名称
            'fileNamePrefix': f'{region_name}_DEM',
            'scale': 30,  # 分辨率 (米)
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_dem = ee.batch.Export.image.toDrive(**export_params_dem)
        task_dem.start()
        print(f"  -> 已启动DEM数据导出任务: {task_dem.id}")

        # --- 导出地表覆盖(LULC)数据 ---
        export_params_lulc = {
            'image': lulc_remapped.clip(geometry),
            'description': f'{region_name}_LULC',
            'folder': 'GEE_Exports',
            'fileNamePrefix': f'{region_name}_LULC',
            'scale': 30, # 同样使用30米以匹配DEM
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_lulc = ee.batch.Export.image.toDrive(**export_params_lulc)
        task_lulc.start()
        print(f"  -> 已启动地表覆盖数据导出任务: {task_lulc.id}")

    print("\n所有区域的处理任务均已启动！")
    print("请注意：实际的数据处理和导出在Google的云端进行，这可能需要几分钟到几十分钟不等。")
    print("您可以前往以下网址监控任务状态：")
    print("https://code.earthengine.google.com/tasks")
    print("任务完成后，文件将出现在您Google Drive的 'GEE_Exports' 文件夹中。")


if __name__ == '__main__':
    main() 
import os

# 定义我们感兴趣的五个区域
# 每个区域都是一个200km x 200km的矩形框
REGIONS = [
    {
        "name": "Scottish_Highlands",
        "bbox": [-6, 56.6, -4, 58.4]  # [lon_min, lat_min, lon_max, lat_max]
    },
    {
        "name": "Israel_Palestine",
        "bbox": [33.75, 30.6, 35.85, 32.4]
    },
    {
        "name": "Kashmir",
        "bbox": [74.95, 33.1, 77.05, 34.9]
    },
    {
        "name": "Kuwait_Gulf_War",
        "bbox": [46.9, 28.4, 48.9, 30.2]
    },
    {
        "name": "Donbas_Ukraine",
        "bbox": [36.65, 47.4, 39.35, 49.2]
    }
]

# ESA WorldCover 2020 v100 的地表覆盖类型原始值
# https://developers.google.com/earth-engine/datasets/catalog/ESA_WorldCover_v100
# 我们需要将它们重新映射到项目要求的分类值
# from: [10, 20, 30, 40, 50, 60, 80, 90]
# to:   [80, 40, 30, 60, 50, 90, 10, 20]
# GEE原始值 -> 项目要求值
# 10: Tree cover (森林) -> 80
# 20: Shrubland (灌木地) -> 40
# 30: Grassland (草地) -> 30
# 40: Cropland (农田) -> 60
# 50: Built-up (建筑用地) -> 50
# 60: Barren / sparse vegetation (荒地) -> 90
# 80: Open water (水域) -> 10
# 90: Herbaceous wetland (湿地) -> 20
# 其他所有类型 (如冰雪、苔藓等) 将被映射为 "未分类" (255)
REMAP_FROM = [10, 20, 30, 40, 50, 60, 80, 90]
REMAP_TO =   [80, 40, 30, 60, 50, 90, 10, 20]


def main():
    """
    主函数，用于初始化GEE、处理并导出数据。
    """
    try:
        # 首先，尝试进行认证。
        # 我们强制使用 'notebook' 模式，以确保它生成一个URL，而不是去查找gcloud。
        ee.Authenticate(auth_mode='notebook')

        # 初始化 Google Earth Engine
        # 这会使用您刚刚通过浏览器授权创建的认证信息
        ee.Initialize(auth_mode='notebook')
        print("Google Earth Engine 初始化成功！")
    except Exception as e:
        print("Google Earth Engine 初始化或认证失败。")
        print("请仔细检查您是否正确地复制了授权链接并在浏览器中授权，然后将授权码粘贴回终端。")
        print(f"错误详情: {e}")
        return

    # 加载全球DEM数据集 (SRTM 30米分辨率)
    dem_image = ee.Image("USGS/SRTMGL1_003")

    # 加载全球地表覆盖数据集 (ESA WorldCover 10米分辨率)
    lulc_image = ee.Image("ESA/WorldCover/v100").select('Map')

    # 对地表覆盖数据进行重分类
    lulc_remapped = lulc_image.remap(REMAP_FROM, REMAP_TO, defaultValue=255).rename('lulc')

    # 循环处理并导出每个区域的数据
    for region in REGIONS:
        region_name = region["name"]
        geometry = ee.Geometry.Rectangle(region["bbox"])
        print(f"开始处理区域: {region_name}...")

        # --- 导出DEM数据 ---
        export_params_dem = {
            'image': dem_image.clip(geometry),
            'description': f'{region_name}_DEM',
            'folder': 'GEE_Exports',  # Google Drive 中的文件夹名称
            'fileNamePrefix': f'{region_name}_DEM',
            'scale': 30,  # 分辨率 (米)
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_dem = ee.batch.Export.image.toDrive(**export_params_dem)
        task_dem.start()
        print(f"  -> 已启动DEM数据导出任务: {task_dem.id}")

        # --- 导出地表覆盖(LULC)数据 ---
        export_params_lulc = {
            'image': lulc_remapped.clip(geometry),
            'description': f'{region_name}_LULC',
            'folder': 'GEE_Exports',
            'fileNamePrefix': f'{region_name}_LULC',
            'scale': 30, # 同样使用30米以匹配DEM
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_lulc = ee.batch.Export.image.toDrive(**export_params_lulc)
        task_lulc.start()
        print(f"  -> 已启动地表覆盖数据导出任务: {task_lulc.id}")

    print("\n所有区域的处理任务均已启动！")
    print("请注意：实际的数据处理和导出在Google的云端进行，这可能需要几分钟到几十分钟不等。")
    print("您可以前往以下网址监控任务状态：")
    print("https://code.earthengine.google.com/tasks")
    print("任务完成后，文件将出现在您Google Drive的 'GEE_Exports' 文件夹中。")


if __name__ == '__main__':
    main() 
import os

# 定义我们感兴趣的五个区域
# 每个区域都是一个200km x 200km的矩形框
REGIONS = [
    {
        "name": "Scottish_Highlands",
        "bbox": [-6, 56.6, -4, 58.4]  # [lon_min, lat_min, lon_max, lat_max]
    },
    {
        "name": "Israel_Palestine",
        "bbox": [33.75, 30.6, 35.85, 32.4]
    },
    {
        "name": "Kashmir",
        "bbox": [74.95, 33.1, 77.05, 34.9]
    },
    {
        "name": "Kuwait_Gulf_War",
        "bbox": [46.9, 28.4, 48.9, 30.2]
    },
    {
        "name": "Donbas_Ukraine",
        "bbox": [36.65, 47.4, 39.35, 49.2]
    }
]

# ESA WorldCover 2020 v100 的地表覆盖类型原始值
# https://developers.google.com/earth-engine/datasets/catalog/ESA_WorldCover_v100
# 我们需要将它们重新映射到项目要求的分类值
# from: [10, 20, 30, 40, 50, 60, 80, 90]
# to:   [80, 40, 30, 60, 50, 90, 10, 20]
# GEE原始值 -> 项目要求值
# 10: Tree cover (森林) -> 80
# 20: Shrubland (灌木地) -> 40
# 30: Grassland (草地) -> 30
# 40: Cropland (农田) -> 60
# 50: Built-up (建筑用地) -> 50
# 60: Barren / sparse vegetation (荒地) -> 90
# 80: Open water (水域) -> 10
# 90: Herbaceous wetland (湿地) -> 20
# 其他所有类型 (如冰雪、苔藓等) 将被映射为 "未分类" (255)
REMAP_FROM = [10, 20, 30, 40, 50, 60, 80, 90]
REMAP_TO =   [80, 40, 30, 60, 50, 90, 10, 20]


def main():
    """
    主函数，用于初始化GEE、处理并导出数据。
    """
    try:
        # 首先，尝试进行认证。
        # 我们强制使用 'notebook' 模式，以确保它生成一个URL，而不是去查找gcloud。
        ee.Authenticate(auth_mode='notebook')

        # 初始化 Google Earth Engine
        # 这会使用您刚刚通过浏览器授权创建的认证信息
        ee.Initialize(auth_mode='notebook')
        print("Google Earth Engine 初始化成功！")
    except Exception as e:
        print("Google Earth Engine 初始化或认证失败。")
        print("请仔细检查您是否正确地复制了授权链接并在浏览器中授权，然后将授权码粘贴回终端。")
        print(f"错误详情: {e}")
        return

    # 加载全球DEM数据集 (SRTM 30米分辨率)
    dem_image = ee.Image("USGS/SRTMGL1_003")

    # 加载全球地表覆盖数据集 (ESA WorldCover 10米分辨率)
    lulc_image = ee.Image("ESA/WorldCover/v100").select('Map')

    # 对地表覆盖数据进行重分类
    lulc_remapped = lulc_image.remap(REMAP_FROM, REMAP_TO, defaultValue=255).rename('lulc')

    # 循环处理并导出每个区域的数据
    for region in REGIONS:
        region_name = region["name"]
        geometry = ee.Geometry.Rectangle(region["bbox"])
        print(f"开始处理区域: {region_name}...")

        # --- 导出DEM数据 ---
        export_params_dem = {
            'image': dem_image.clip(geometry),
            'description': f'{region_name}_DEM',
            'folder': 'GEE_Exports',  # Google Drive 中的文件夹名称
            'fileNamePrefix': f'{region_name}_DEM',
            'scale': 30,  # 分辨率 (米)
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_dem = ee.batch.Export.image.toDrive(**export_params_dem)
        task_dem.start()
        print(f"  -> 已启动DEM数据导出任务: {task_dem.id}")

        # --- 导出地表覆盖(LULC)数据 ---
        export_params_lulc = {
            'image': lulc_remapped.clip(geometry),
            'description': f'{region_name}_LULC',
            'folder': 'GEE_Exports',
            'fileNamePrefix': f'{region_name}_LULC',
            'scale': 30, # 同样使用30米以匹配DEM
            'region': geometry,
            'fileFormat': 'GeoTIFF',
            'maxPixels': 1e9
        }
        task_lulc = ee.batch.Export.image.toDrive(**export_params_lulc)
        task_lulc.start()
        print(f"  -> 已启动地表覆盖数据导出任务: {task_lulc.id}")

    print("\n所有区域的处理任务均已启动！")
    print("请注意：实际的数据处理和导出在Google的云端进行，这可能需要几分钟到几十分钟不等。")
    print("您可以前往以下网址监控任务状态：")
    print("https://code.earthengine.google.com/tasks")
    print("任务完成后，文件将出现在您Google Drive的 'GEE_Exports' 文件夹中。")


if __name__ == '__main__':
    main() 
 
 
 