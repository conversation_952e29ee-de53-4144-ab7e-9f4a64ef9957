 
import requests
import os
from tqdm import tqdm

# 1. 根据之前的验证脚本，我们缺失这两个文件
MISSING_FILES = ["N28.zip", "N29.zip"]

# 2. 定义下载的基础URL和输出目录
BASE_URL = "https://www.viewfinderpanoramas.org/dem3"
OUTPUT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'DEM'))

# --- 主程序 ---

def download_missing_files():
    """下载指定的缺失DEM文件"""
    print(f"将开始下载缺失的DEM文件到: {OUTPUT_DIR}")
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    success_count = 0
    error_count = 0

    with tqdm(total=len(MISSING_FILES), desc="下载进度") as pbar:
        for filename in MISSING_FILES:
            pbar.set_postfix_str(filename)
            file_url = f"{BASE_URL}/{filename}"
            output_path = os.path.join(OUTPUT_DIR, filename)

            if os.path.exists(output_path):
                print(f"\n文件 {filename} 已存在，跳过。")
                pbar.update(1)
                success_count += 1
                continue

            try:
                print(f"\n正在下载: {file_url}")
                response = requests.get(file_url, stream=True, timeout=300) # 5分钟超时
                response.raise_for_status()  # 如果发生HTTP错误(如404)，则会引发异常

                total_size = int(response.headers.get('content-length', 0))
                
                with open(output_path, 'wb') as f, tqdm(
                    total=total_size, unit='iB', unit_scale=True,
                    desc=f"  -> {filename}", leave=False
                ) as file_pbar:
                    for chunk in response.iter_content(chunk_size=1024):
                        f.write(chunk)
                        file_pbar.update(len(chunk))

                print(f"\n成功下载: {filename}")
                success_count += 1

            except requests.exceptions.HTTPError as e:
                # 特别处理404 Not Found错误
                if e.response.status_code == 404:
                    print(f"\n错误: 文件在服务器上不存在 (404 Not Found): {file_url}")
                else:
                    print(f"\n下载 {filename} 时发生HTTP错误: {e}")
                error_count += 1
            except requests.exceptions.RequestException as e:
                print(f"\n下载 {filename} 时发生网络错误: {e}")
                error_count += 1
            except Exception as e:
                print(f"\n处理 {filename} 时发生未知错误: {e}")
                error_count += 1

            pbar.update(1)

    print("\n--- 下载总结 ---")
    if success_count == len(MISSING_FILES):
        print("\033[92m所有缺失文件已成功下载！\033[0m")
    elif success_count > 0:
        print(f"\033[93m部分文件下载完成 ({success_count}/{len(MISSING_FILES)})。\033[0m")
    else:
        print("\033[91m所有文件下载失败。\033[0m")
    
    if error_count > 0:
        print("请检查上面列出的错误信息。")


if __name__ == "__main__":
    download_missing_files() 