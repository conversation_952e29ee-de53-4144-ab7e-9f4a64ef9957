import os
import math
import rioxarray
from tqdm import tqdm
import sys

# --- 配置 ---

# 1. 定义我们感兴趣的区域及其中心点
REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}

# 2. 定义每个区域边界框的大小（公里）
BOX_SIZE_KM = 200

# 3. 定义所有数据的总输出目录
# 假设此脚本位于 'src/data_preparation'
OUTPUT_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data'))

# 4. 定义DEM数据源 (AWS S3上的公共数据)
DEM_SOURCE = {
    "url": "https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt",
    "output_filename": "dem.tif"
}

# --- 辅助函数 ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """根据中心点和大小计算正方形边界框"""
    lat_rad = math.radians(lat_deg)
    # 这是一个近似计算，但对于我们的目的来说足够了
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    
    half_size_lat_deg = (size_km / 2.0) * lat_deg_per_km
    half_size_lon_deg = (size_km / 2.0) * lon_deg_per_km
    
    return (
        lon_deg - half_size_lon_deg,
        lat_deg - half_size_lat_deg,
        lon_deg + half_size_lon_deg,
        lat_deg + half_size_lat_deg,
    )

# --- 主程序 ---

def download_dem_data():
    """使用rioxarray下载所有指定区域的DEM数据"""
    print(f"数据将保存到基础目录: {OUTPUT_BASE_DIR}")
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # 配置Rasterio使其不使用AWS签名，以便访问公共数据
    os.environ['AWS_NO_SIGN_REQUEST'] = 'YES'

    total_downloads = len(REGIONS)
    errors_occurred = False
    
    with tqdm(total=total_downloads, desc="DEM下载总进度") as pbar:
        for region_name, coords in REGIONS.items():
            pbar.set_postfix_str(f"区域: {region_name}")
            region_dir = os.path.join(OUTPUT_BASE_DIR, region_name)
            os.makedirs(region_dir, exist_ok=True)

            bbox = calculate_bounding_box(coords["lat"], coords["lon"], BOX_SIZE_KM)
            
            output_path = os.path.join(region_dir, DEM_SOURCE["output_filename"])

            if os.path.exists(output_path):
                print(f"\n  - {region_name} 的DEM数据已存在，跳过下载。")
                pbar.update(1)
                continue

            try:
                # 打开远程数据集，进行裁剪并保存
                print(f"\n  - 开始下载 {region_name} 的DEM数据...")
                rds = rioxarray.open_rasterio(DEM_SOURCE["url"])
                clipped = rds.rio.clip_box(
                    minx=bbox[0],
                    miny=bbox[1],
                    maxx=bbox[2],
                    maxy=bbox[3],
                    crs="EPSG:4326"
                )
                clipped.rio.to_raster(output_path)
                print(f"\n  - 成功下载到: {output_path}")

            except Exception as e:
                print(f"\n  - 下载 {region_name} 的DEM数据时出错。")
                print(f"  - URL: {DEM_SOURCE['url']}")
                print(f"  - 错误详情: {e}")
                errors_occurred = True
            
            finally:
                pbar.update(1)

    if errors_occurred:
        print("\n有一个或多个DEM文件下载失败。请检查网络连接或错误信息。")
        sys.exit(1)
    else:
        print("\n\n所有DEM文件均已成功准备就绪！")

if __name__ == "__main__":
    download_dem_data() 