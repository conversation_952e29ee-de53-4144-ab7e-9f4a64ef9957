 
import os
import math

# 1. 定义所有初始区域及其中心点
INITIAL_REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}
BOX_SIZE_KM = 200

# 2. 定义可用的数据文件
# 用户提供的LULC瓦片ID
PROVIDED_LULC_TILES = {
    "N30_55", "N36_30", "N36_45", "N38_30", "N43_30"
}
# 'data/DEM' 目录下实际存在的DEM压缩包
# (基于我们之前的 'ls' 和 'verify' 脚本结果)
AVAILABLE_DEM_ZIPS = {
    "N30.zip", "N31.zip", "N32.zip", "N33.zip", "N34.zip",
    "N47.zip", "N48.zip", "N49.zip", "N56.zip", "N57.zip", "N58.zip"
}

# --- 辅助函数 ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """计算边界框，用于确定所需的DEM瓦片"""
    lat_rad = math.radians(lat_deg)
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    half_size_lat = (size_km / 2.0) * lat_deg_per_km
    half_size_lon = (size_km / 2.0) * lon_deg_per_km
    return {
        "lat_min": lat_deg - half_size_lat, "lat_max": lat_deg + half_size_lat,
        "lon_min": lon_deg - half_size_lon, "lon_max": lon_deg + half_size_lon,
    }

def check_lulc_availability(lat, lon):
    """检查区域中心点是否落在已提供的LULC瓦片内"""
    lon_index = math.floor(lon / 5.0) + 29
    lat_index = math.ceil(lat / 5.0) * 5
    required_tile = f"N{lon_index}_{lat_index}"
    
    is_available = required_tile in PROVIDED_LULC_TILES
    return is_available, required_tile

def check_dem_availability(bbox):
    """检查覆盖区域所需的所有DEM瓦片是否都已存在"""
    required_zips = set()
    lat_start = math.floor(bbox["lat_min"])
    lat_end = math.floor(bbox["lat_max"])
    
    for lat in range(lat_start, lat_end + 1):
        required_zips.add(f"N{lat:02d}.zip")
        
    missing_zips = required_zips - AVAILABLE_DEM_ZIPS
    is_available = not missing_zips
    return is_available, required_zips

# --- 主程序 ---

def finalize_regions():
    """根据数据可用性最终确定研究区域"""
    print("--- 最终研究区域验证报告 ---\n")
    
    valid_regions = {}
    
    for name, coords in INITIAL_REGIONS.items():
        print(f"正在分析区域: {name}")
        
        # 检查LULC数据
        lulc_ok, lulc_tile = check_lulc_availability(coords['lat'], coords['lon'])
        if lulc_ok:
            print(f"  - LULC ✅: 中心点落在提供的瓦片 '{lulc_tile}' 中。")
        else:
            print(f"  - LULC ❌: 中心点所需的瓦片 '{lulc_tile}' 未提供。")
            
        # 检查DEM数据
        bbox = calculate_bounding_box(coords['lat'], coords['lon'], BOX_SIZE_KM)
        dem_ok, dem_zips = check_dem_availability(bbox)
        if dem_ok:
            print(f"  - DEM  ✅: 所需的DEM压缩包 ({', '.join(sorted(list(dem_zips)))}) 均已存在。")
        else:
            missing_zips = dem_zips - AVAILABLE_DEM_ZIPS
            print(f"  - DEM  ❌: 缺失DEM压缩包: {', '.join(sorted(list(missing_zips)))}。")
            
        # 最终决定
        if lulc_ok and dem_ok:
            print("  - 结果: \033[92m区域有效\033[0m，将用于后续分析。\n")
            valid_regions[name] = coords
        else:
            print("  - 结果: \033[91m放弃该区域\033[0m。\n")
            
    print("-" * 35)
    print("\n--- 最终结论 ---")
    if not valid_regions:
        print("\033[1m\033[91m没有找到任何同时拥有两种数据的有效区域！\033[0m")
    else:
        print(f"\033[1m\033[92m项目将基于以下 {len(valid_regions)} 个有效区域进行：\033[0m")
        for region_name in valid_regions:
            print(f"  - {region_name}")
            
    return valid_regions

if __name__ == "__main__":
    finalize_regions() 