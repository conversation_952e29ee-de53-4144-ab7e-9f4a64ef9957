 
import os
import math

# 1. 定义我们感兴趣的区域及其中心点和大小
REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}
BOX_SIZE_KM = 200

# 2. 定义DEM数据所在的目录
DEM_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'DEM'))

# --- 辅助函数 ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """根据中心点和大小计算正方形边界框"""
    lat_rad = math.radians(lat_deg)
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    
    half_size_lat_deg = (size_km / 2.0) * lat_deg_per_km
    half_size_lon_deg = (size_km / 2.0) * lon_deg_per_km
    
    return {
        "lat_min": lat_deg - half_size_lat_deg,
        "lat_max": lat_deg + half_size_lat_deg,
        "lon_min": lon_deg - half_size_lon_deg,
        "lon_max": lon_deg + half_size_lon_deg,
    }

def get_required_dem_tiles(bbox):
    """
    根据边界框计算所需的所有 viewfinderpanoramas.org DEM瓦片.
    这些瓦片是1x1度的. 文件名基于瓦片的左下角坐标.
    例如 N57W005.hgt 覆盖 57-58N, 4-5W.
    网站将同一纬度的所有文件打包成一个zip, e.g., N57.zip
    """
    required_zips = set()
    
    # 向下取整得到左下角坐标
    lat_start = math.floor(bbox["lat_min"])
    lat_end = math.floor(bbox["lat_max"])
    
    for lat in range(lat_start, lat_end + 1):
        if lat >= 0:
            lat_str = f"N{lat:02d}"
        else:
            lat_str = f"S{-lat:02d}"
        
        zip_filename = f"{lat_str}.zip"
        required_zips.add(zip_filename)
        
    return required_zips

# --- 主程序 ---

def verify_dem_coverage():
    """主函数，检查DEM文件覆盖率"""
    print(f"正在检查目录: {DEM_DIR}\n")

    if not os.path.isdir(DEM_DIR):
        print(f"错误: 目录不存在 {DEM_DIR}")
        return

    # 1. 获取目录下实际存在的zip文件
    try:
        existing_files = {f for f in os.listdir(DEM_DIR) if f.endswith('.zip')}
        print(f"目录下找到 {len(existing_files)} 个zip压缩包:")
        # 每行打印5个，方便阅读
        sorted_existing = sorted(list(existing_files))
        for i in range(0, len(sorted_existing), 5):
             print("  " + "  ".join(sorted_existing[i:i+5]))
        print("-" * 30)
    except Exception as e:
        print(f"无法读取目录 {DEM_DIR}: {e}")
        return

    # 2. 检查每个区域的需求
    all_regions_covered = True
    total_missing_files = set()

    for region_name, coords in REGIONS.items():
        print(f"正在分析区域: {region_name}")
        bbox = calculate_bounding_box(coords["lat"], coords["lon"], BOX_SIZE_KM)
        required_tiles = get_required_dem_tiles(bbox)
        
        print(f"  - 经纬度范围: "
              f'Lat({bbox["lat_min"]:.2f} to {bbox["lat_max"]:.2f}), '
              f'Lon({bbox["lon_min"]:.2f} to {bbox["lon_max"]:.2f})')
        print(f"  - 需要的压缩包: {', '.join(sorted(list(required_tiles)))}")

        missing_files = required_tiles - existing_files
        
        if not missing_files:
            print("  - 状态: \033[92m数据完整\033[0m\n")
        else:
            print(f"  - 状态: \033[91m数据缺失\033[0m")
            print(f"  - 缺失的文件: {', '.join(sorted(list(missing_files)))}\n")
            all_regions_covered = False
            total_missing_files.update(missing_files)
    
    print("-" * 30)
    # 3. 总结
    if all_regions_covered:
        print("\033[1m\033[92m结论: 所有区域的DEM数据均已满足要求。\033[0m")
    else:
        print("\033[1m\033[91m结论: 数据不完整。\033[0m")
        print("总共缺失以下文件:")
        sorted_missing = sorted(list(total_missing_files))
        for i in range(0, len(sorted_missing), 5):
             print("  " + "  ".join(sorted_missing[i:i+5]))

if __name__ == "__main__":
    verify_dem_coverage() 