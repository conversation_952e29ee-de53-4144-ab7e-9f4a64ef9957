{"data": {"environment_dir": "trajectory_generation_module_pkg/examples/data/environment", "trajectory_dir": "core_trajectories", "output_dir": "advanced_trajectory_output", "processed_trajectory_dir": "processed_trajectories"}, "preprocessing": {"target_resolution": 5.0, "time_step": 0.5}, "model": {"sequence_length": 20, "hidden_dim": 256, "num_layers": 4, "num_heads": 8, "dropout": 0.1, "learning_rate": 0.0001, "batch_size": 32, "max_epochs": 100}, "simulation": {"time_step": 0.1, "max_simulation_time": 3600.0}, "vehicle_configs": {"standard": {"max_speed": 25.0, "max_acceleration": 3.0, "vehicle_type": "standard"}, "high_mobility": {"max_speed": 35.0, "max_acceleration": 5.0, "vehicle_type": "high_mobility"}, "stealth": {"max_speed": 15.0, "max_acceleration": 2.0, "vehicle_type": "stealth"}, "mountain": {"max_speed": 20.0, "max_acceleration": 2.5, "vehicle_type": "mountain"}}}