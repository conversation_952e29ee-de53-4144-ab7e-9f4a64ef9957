#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轨迹迭代生成器 (The Simulator)
将深度学习预测器的控制指令转化为实际轨迹

核心功能：
1. 基于控制指令的运动学仿真
2. 物理约束强制执行
3. 多样性轨迹生成
4. 实时状态更新
"""

import numpy as np
import torch
import pandas as pd
from typing import Dict, List, Tuple, Optional, Deque
from collections import deque
from dataclasses import dataclass
import math
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

from control_predictor import TransformerControlPredictor, ModelConfig, sample_from_mdn
from data_preprocessing import HighResolutionEnvironmentMaps
from path_planning import HierarchicalAStar, ScenarioType

@dataclass
class VehicleConfig:
    """车辆配置"""
    # 物理参数
    mass: float = 1800.0                    # 质量 (kg)
    wheelbase: float = 2.7                  # 轴距 (m)
    max_speed: float = 30.0                 # 最大速度 (m/s)
    max_acceleration: float = 4.0           # 最大加速度 (m/s²)
    max_deceleration: float = 8.0           # 最大减速度 (m/s²)
    max_steering_angle: float = 30.0        # 最大转向角 (度)
    
    # 类型标识
    vehicle_type: str = "standard"          # 车辆类型
    
    # 初始状态
    initial_position: Tuple[float, float] = (0.0, 0.0)
    initial_velocity: Tuple[float, float] = (0.0, 0.0)
    initial_heading: float = 0.0            # 度

@dataclass
class SimulationConfig:
    """仿真配置"""
    time_step: float = 0.1                  # 时间步长 (s)
    max_simulation_time: float = 3600.0     # 最大仿真时间 (s)
    history_buffer_size: int = 20           # 历史状态缓冲区大小
    goal_tolerance: float = 50.0            # 目标到达容差 (m)
    path_following_tolerance: float = 100.0  # 路径跟踪容差 (m)

class VehicleKinematicsModel:
    """车辆运动学模型"""
    
    def __init__(self, vehicle_config: VehicleConfig):
        self.config = vehicle_config
        
        # 当前状态
        self.position = np.array(vehicle_config.initial_position, dtype=float)
        self.velocity = np.array(vehicle_config.initial_velocity, dtype=float)
        self.heading = vehicle_config.initial_heading  # 度
        self.speed = np.linalg.norm(self.velocity)
        
    def update(self, control_input: np.ndarray, dt: float) -> Dict[str, float]:
        """
        根据控制输入更新车辆状态
        
        Args:
            control_input: [throttle_brake, steering_angle] 控制输入
            dt: 时间步长
            
        Returns:
            更新后的状态信息
        """
        throttle_brake, steering_angle = control_input
        
        # 物理约束限制
        throttle_brake = np.clip(throttle_brake, -self.config.max_deceleration, self.config.max_acceleration)
        steering_angle = np.clip(steering_angle, -self.config.max_steering_angle, self.config.max_steering_angle)
        
        # 纵向运动更新
        acceleration = throttle_brake
        
        # 更新速度
        new_speed = self.speed + acceleration * dt
        new_speed = np.clip(new_speed, 0.0, self.config.max_speed)
        
        # 转向运动更新（自行车模型）
        if new_speed > 0.1:  # 避免除零
            # 转向角弧度
            steering_rad = np.radians(steering_angle)
            
            # 角速度 (rad/s)
            angular_velocity = (new_speed / self.config.wheelbase) * np.tan(steering_rad)
            
            # 更新航向角
            new_heading = self.heading + np.degrees(angular_velocity * dt)
            new_heading = (new_heading + 360) % 360  # 归一化到 [0, 360)
        else:
            new_heading = self.heading
            angular_velocity = 0.0
            
        # 更新位置
        heading_rad = np.radians(new_heading)
        new_velocity = new_speed * np.array([np.cos(heading_rad), np.sin(heading_rad)])
        new_position = self.position + self.velocity * dt + 0.5 * acceleration * np.array([np.cos(heading_rad), np.sin(heading_rad)]) * dt**2
        
        # 更新状态
        self.position = new_position
        self.velocity = new_velocity
        self.speed = new_speed
        self.heading = new_heading
        
        # 计算实际加速度
        velocity_change = new_velocity - self.velocity
        actual_acceleration = velocity_change / dt if dt > 0 else np.zeros(2)
        
        return {
            'position': self.position.copy(),
            'velocity': self.velocity.copy(),
            'speed': self.speed,
            'heading': self.heading,
            'acceleration': actual_acceleration,
            'angular_velocity': angular_velocity,
            'control_throttle_brake': throttle_brake,
            'control_steering_angle': steering_angle
        }

class TrajectorySimulator:
    """轨迹仿真器"""
    
    def __init__(self, 
                 control_predictor: TransformerControlPredictor,
                 env_maps: HighResolutionEnvironmentMaps,
                 vehicle_config: VehicleConfig,
                 sim_config: SimulationConfig,
                 model_config: ModelConfig):
        """
        初始化轨迹仿真器
        
        Args:
            control_predictor: 训练好的控制预测器
            env_maps: 环境地图
            vehicle_config: 车辆配置
            sim_config: 仿真配置
            model_config: 模型配置
        """
        self.control_predictor = control_predictor
        self.env_maps = env_maps
        self.vehicle_config = vehicle_config
        self.sim_config = sim_config
        self.model_config = model_config
        
        # 车辆类型映射
        self.vehicle_type_map = {
            'standard': 0,
            'high_mobility': 1,
            'stealth': 2,
            'mountain': 3
        }
        
        print(f"轨迹仿真器初始化完成")
        print(f"车辆类型: {vehicle_config.vehicle_type}")
        print(f"时间步长: {sim_config.time_step}s")
        
    def generate_trajectory(self, 
                          reference_path: List[Tuple[float, float]],
                          num_samples: int = 1,
                          random_seed: Optional[int] = None) -> List[pd.DataFrame]:
        """
        生成轨迹
        
        Args:
            reference_path: 参考路径点列表
            num_samples: 生成样本数
            random_seed: 随机种子
            
        Returns:
            生成的轨迹列表
        """
        if random_seed is not None:
            np.random.seed(random_seed)
            torch.manual_seed(random_seed)
            
        trajectories = []
        
        for sample_idx in range(num_samples):
            print(f"生成轨迹样本 {sample_idx + 1}/{num_samples}")
            
            trajectory = self._simulate_single_trajectory(reference_path, sample_idx)
            trajectories.append(trajectory)
            
        return trajectories
        
    def _simulate_single_trajectory(self, 
                                  reference_path: List[Tuple[float, float]],
                                  sample_idx: int = 0) -> pd.DataFrame:
        """仿真单条轨迹"""
        
        # 初始化车辆模型
        vehicle = VehicleKinematicsModel(self.vehicle_config)
        
        # 初始化历史状态缓冲区
        history_buffer = deque(maxlen=self.sim_config.history_buffer_size)
        
        # 轨迹记录
        trajectory_data = []
        
        # 仿真参数
        dt = self.sim_config.time_step
        current_time = 0.0
        path_index = 0
        
        # 初始化历史缓冲区（用零状态填充）
        initial_state = self._get_current_state_vector(vehicle)
        for _ in range(self.model_config.sequence_length):
            history_buffer.append(initial_state.copy())
            
        # 仿真循环
        while (current_time < self.sim_config.max_simulation_time and
               path_index < len(reference_path) - 1):
            
            # 1. 准备深度学习模型输入
            model_input = self._prepare_model_input(vehicle, history_buffer, reference_path, path_index)
            
            # 2. 预测控制指令
            with torch.no_grad():
                self.control_predictor.eval()
                mdn_params = self.control_predictor(model_input)
                
                # 从MDN采样控制指令
                control_samples = sample_from_mdn(mdn_params, num_samples=1)
                control_input = control_samples[0, 0].cpu().numpy()  # [throttle_brake, steering_angle]
                
            # 3. 运动学更新
            state_info = vehicle.update(control_input, dt)
            
            # 4. 记录状态
            trajectory_record = {
                'timestamp': current_time,
                'x': state_info['position'][0],
                'y': state_info['position'][1],
                'velocity_x': state_info['velocity'][0],
                'velocity_y': state_info['velocity'][1],
                'speed': state_info['speed'],
                'heading': state_info['heading'],
                'acceleration_x': state_info['acceleration'][0],
                'acceleration_y': state_info['acceleration'][1],
                'control_throttle_brake': state_info['control_throttle_brake'],
                'control_steering_angle': state_info['control_steering_angle'],
                'angular_velocity': state_info['angular_velocity'],
                'path_index': path_index
            }
            
            # 添加环境特征
            env_features = self.env_maps.query_features(state_info['position'][0], state_info['position'][1])
            trajectory_record.update(env_features)
            
            trajectory_data.append(trajectory_record)
            
            # 5. 更新历史缓冲区
            current_state_vector = self._get_current_state_vector(vehicle)
            history_buffer.append(current_state_vector)
            
            # 6. 检查路径跟踪进度
            path_index = self._update_path_index(vehicle.position, reference_path, path_index)
            
            # 7. 检查是否到达目标
            if self._is_goal_reached(vehicle.position, reference_path[-1]):
                print(f"到达目标，仿真时间: {current_time:.2f}s")
                break
                
            current_time += dt
            
        # 转换为DataFrame
        trajectory_df = pd.DataFrame(trajectory_data)
        
        print(f"轨迹生成完成，总点数: {len(trajectory_df)}, 持续时间: {current_time:.2f}s")
        return trajectory_df
        
    def _get_current_state_vector(self, vehicle: VehicleKinematicsModel) -> np.ndarray:
        """获取当前状态向量"""
        return np.array([
            vehicle.position[0],    # x
            vehicle.position[1],    # y
            vehicle.velocity[0],    # vx
            vehicle.velocity[1],    # vy
            0.0,                   # ax (将由历史计算)
            0.0,                   # ay (将由历史计算)
            vehicle.heading        # heading
        ], dtype=np.float32)
        
    def _prepare_model_input(self, 
                           vehicle: VehicleKinematicsModel,
                           history_buffer: Deque,
                           reference_path: List[Tuple[float, float]],
                           path_index: int) -> Dict[str, torch.Tensor]:
        """准备深度学习模型输入"""
        
        # 1. 运动学历史序列
        kinematic_sequence = np.array(list(history_buffer))  # [seq_len, 7]
        
        # 2. 当前环境特征
        env_features = self.env_maps.query_features(vehicle.position[0], vehicle.position[1])
        
        # 提取数值特征（排除字符串类型）
        env_values = []
        for key, value in env_features.items():
            if isinstance(value, (int, float)):
                env_values.append(float(value))
                
        env_array = np.array(env_values, dtype=np.float32)
        
        # 确保环境特征维度匹配
        expected_env_dim = self.model_config.environment_dim
        if len(env_array) < expected_env_dim:
            # 零填充
            padded_env = np.zeros(expected_env_dim, dtype=np.float32)
            padded_env[:len(env_array)] = env_array
            env_array = padded_env
        elif len(env_array) > expected_env_dim:
            # 截断
            env_array = env_array[:expected_env_dim]
            
        # 3. 路径引导特征
        path_guidance = self._calculate_path_guidance_features(vehicle, reference_path, path_index)
        
        # 4. 车辆类型
        vehicle_type_id = self.vehicle_type_map.get(self.vehicle_config.vehicle_type, 0)
        
        # 转换为张量并添加batch维度
        model_input = {
            'kinematic_sequence': torch.from_numpy(kinematic_sequence).unsqueeze(0),  # [1, seq_len, 7]
            'environment_features': torch.from_numpy(env_array).unsqueeze(0),        # [1, env_dim]
            'path_guidance': torch.from_numpy(path_guidance).unsqueeze(0),           # [1, 4]
            'vehicle_type': torch.tensor([vehicle_type_id], dtype=torch.long)        # [1]
        }
        
        return model_input
        
    def _calculate_path_guidance_features(self, 
                                        vehicle: VehicleKinematicsModel,
                                        reference_path: List[Tuple[float, float]],
                                        path_index: int) -> np.ndarray:
        """计算路径引导特征"""
        
        if path_index >= len(reference_path) - 1:
            return np.array([0.0, 0.0, 0.0, 0.0], dtype=np.float32)
            
        # 当前位置到下一个路径点的向量
        current_pos = vehicle.position
        next_waypoint = np.array(reference_path[min(path_index + 1, len(reference_path) - 1)])
        
        # 相对位置
        rel_pos = next_waypoint - current_pos
        distance = np.linalg.norm(rel_pos)
        
        if distance > 0:
            direction = rel_pos / distance
            
            # 计算路径曲率（简化版）
            if path_index + 2 < len(reference_path):
                p1 = np.array(reference_path[path_index])
                p2 = np.array(reference_path[path_index + 1])
                p3 = np.array(reference_path[path_index + 2])
                
                # 简单曲率估计
                v1 = p2 - p1
                v2 = p3 - p2
                
                if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)
                    curvature = 1.0 - cos_angle  # 简化的曲率度量
                else:
                    curvature = 0.0
            else:
                curvature = 0.0
                
            return np.array([
                direction[0],  # 方向x分量
                direction[1],  # 方向y分量
                min(distance, 1000.0) / 1000.0,  # 归一化距离
                curvature      # 路径曲率
            ], dtype=np.float32)
        else:
            return np.array([0.0, 0.0, 0.0, 0.0], dtype=np.float32)
            
    def _update_path_index(self, 
                          current_position: np.ndarray,
                          reference_path: List[Tuple[float, float]],
                          current_index: int) -> int:
        """更新路径索引"""
        
        if current_index >= len(reference_path) - 1:
            return current_index
            
        # 检查是否接近下一个路径点
        next_waypoint = np.array(reference_path[current_index + 1])
        distance = np.linalg.norm(current_position - next_waypoint)
        
        if distance < self.sim_config.path_following_tolerance:
            return min(current_index + 1, len(reference_path) - 1)
        else:
            return current_index
            
    def _is_goal_reached(self, 
                        current_position: np.ndarray,
                        goal_position: Tuple[float, float]) -> bool:
        """检查是否到达目标"""
        goal_array = np.array(goal_position)
        distance = np.linalg.norm(current_position - goal_array)
        return distance < self.sim_config.goal_tolerance

def visualize_trajectory(trajectory_df: pd.DataFrame, 
                        reference_path: List[Tuple[float, float]],
                        env_maps: HighResolutionEnvironmentMaps,
                        save_path: Optional[str] = None):
    """可视化生成的轨迹"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 轨迹路径图
    ax1 = axes[0, 0]
    
    # 绘制参考路径
    ref_x, ref_y = zip(*reference_path)
    ax1.plot(ref_x, ref_y, 'k--', linewidth=2, label='参考路径', alpha=0.7)
    ax1.scatter(ref_x[0], ref_y[0], c='green', s=100, marker='o', label='起点', zorder=5)
    ax1.scatter(ref_x[-1], ref_y[-1], c='red', s=100, marker='s', label='终点', zorder=5)
    
    # 绘制生成轨迹
    ax1.plot(trajectory_df['x'], trajectory_df['y'], 'b-', linewidth=1.5, label='生成轨迹')
    
    ax1.set_xlabel('UTM East (m)')
    ax1.set_ylabel('UTM North (m)')
    ax1.set_title('轨迹路径对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 2. 速度时间图
    ax2 = axes[0, 1]
    ax2.plot(trajectory_df['timestamp'], trajectory_df['speed'], 'b-', linewidth=1.5)
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('速度 (m/s)')
    ax2.set_title('速度变化')
    ax2.grid(True, alpha=0.3)
    
    # 3. 控制输入图
    ax3 = axes[1, 0]
    ax3_twin = ax3.twinx()
    
    line1 = ax3.plot(trajectory_df['timestamp'], trajectory_df['control_throttle_brake'], 
                     'g-', linewidth=1.5, label='油门/制动')
    line2 = ax3_twin.plot(trajectory_df['timestamp'], trajectory_df['control_steering_angle'], 
                          'r-', linewidth=1.5, label='转向角')
    
    ax3.set_xlabel('时间 (s)')
    ax3.set_ylabel('油门/制动 (m/s²)', color='g')
    ax3_twin.set_ylabel('转向角 (度)', color='r')
    ax3.set_title('控制输入')
    
    # 合并图例
    lines1, labels1 = ax3.get_legend_handles_labels()
    lines2, labels2 = ax3_twin.get_legend_handles_labels()
    ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    
    ax3.grid(True, alpha=0.3)
    
    # 4. 地形高程图
    ax4 = axes[1, 1]
    if 'dem' in trajectory_df.columns:
        ax4.plot(trajectory_df['timestamp'], trajectory_df['dem'], 'brown', linewidth=1.5)
        ax4.set_xlabel('时间 (s)')
        ax4.set_ylabel('海拔 (m)')
        ax4.set_title('地形高程变化')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"轨迹可视化保存到: {save_path}")
    
    plt.show()

if __name__ == "__main__":
    # 示例使用
    print("轨迹仿真器模块创建完成")
    
    # 配置示例
    vehicle_config = VehicleConfig(
        vehicle_type="standard",
        initial_position=(0.0, 0.0),
        initial_velocity=(5.0, 0.0),
        initial_heading=0.0
    )
    
    sim_config = SimulationConfig(
        time_step=0.1,
        max_simulation_time=600.0
    )
    
    print(f"车辆配置: {vehicle_config}")
    print(f"仿真配置: {sim_config}")