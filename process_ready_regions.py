import os
import glob
import math
import rasterio
from rasterio.merge import merge
from rasterio.mask import mask
from rasterio.warp import calculate_default_transform, reproject, Resampling, transform_bounds
import geopandas as gpd
from shapely.geometry import box

TARGET_CRS = "EPSG:4326"

def get_files_in_bbox(file_paths, bbox_poly):
    """从文件列表中筛选出与目标Bbox有交集的那些文件。"""
    relevant_files = []
    for fp in file_paths:
        try:
            with rasterio.open(fp) as src:
                # 对.hgt文件强制假定其CRS为WGS84
                src_crs = src.crs if src.crs else "EPSG:4326"
                # 将文件边界转换为目标CRS以进行比较
                file_bounds_poly = box(*transform_bounds(src_crs, TARGET_CRS, *src.bounds))
                if bbox_poly.intersects(file_bounds_poly):
                    relevant_files.append(fp)
        except Exception as e:
            print(f"警告: 无法读取文件 {fp} 的边界: {e}")
    return relevant_files

def reproject_to_target_crs(src_path, dest_path, target_crs=TARGET_CRS):
    """将单个栅格文件重新投影到目标CRS。"""
    with rasterio.open(src_path) as src:
        transform, width, height = calculate_default_transform(
            src.crs, target_crs, src.width, src.height, *src.bounds)
        kwargs = src.meta.copy()
        kwargs.update({
            'crs': target_crs,
            'transform': transform,
            'width': width,
            'height': height
        })

        with rasterio.open(dest_path, 'w', **kwargs) as dst:
            for i in range(1, src.count + 1):
                reproject(
                    source=rasterio.band(src, i),
                    destination=rasterio.band(dst, i),
                    src_transform=src.transform,
                    src_crs=src.crs,
                    dst_transform=transform,
                    dst_crs=target_crs,
                    resampling=Resampling.nearest)
    return dest_path

def process_region(region_name, bbox, all_dem_hgt_files, all_lulc_tif_files, output_base_dir):
    """
    (重构版) 处理单个区域：筛选、重投影、拼接、裁剪。
    """
    print(f"\n--- 开始处理区域: {region_name} ---")
    
    region_output_dir = os.path.join(output_base_dir, region_name.replace(" ", "_"))
    temp_dir = os.path.join(region_output_dir, "temp")
    os.makedirs(temp_dir, exist_ok=True)
    
    region_bbox_poly = box(*bbox)
    crop_geometry = gpd.GeoDataFrame([1], geometry=[region_bbox_poly], crs=TARGET_CRS)

    # --- DEM 处理 ---
    print("正在处理DEM...")
    src_files_to_mosaic = [] # 初始化以防止UnboundLocalError
    try:
        # 1. 筛选相关文件
        relevant_dem_files = get_files_in_bbox(all_dem_hgt_files, region_bbox_poly)
        if not relevant_dem_files:
            raise FileNotFoundError(f"未找到与 {region_name} 区域重叠的DEM(.hgt)文件。")
        print(f"找到 {len(relevant_dem_files)} 个相关的DEM文件。")
        
        # 2. 拼接 (DEM文件通常CRS一致，无需重投影)
        src_files_to_mosaic = [rasterio.open(fp) for fp in relevant_dem_files]
        mosaic, out_trans = merge(src_files_to_mosaic)
        
        # 3. 裁剪
        out_meta = src_files_to_mosaic[0].meta.copy()
        out_meta.update({"driver": "GTiff", "height": mosaic.shape[1], "width": mosaic.shape[2], "transform": out_trans, "crs": TARGET_CRS})
        
        # 写入内存文件，然后再用mask裁剪
        with rasterio.io.MemoryFile() as memfile:
            with memfile.open(**out_meta) as dataset:
                dataset.write(mosaic)
            
            with memfile.open() as dataset:
                out_image, out_transform = mask(dataset, crop_geometry.geometry, crop=True)

        out_meta.update({"height": out_image.shape[1], "width": out_image.shape[2], "transform": out_transform})

        dem_output_path = os.path.join(region_output_dir, "dem.tif")
        with rasterio.open(dem_output_path, "w", **out_meta) as dest:
            dest.write(out_image)
        print(f"DEM数据成功保存到: {dem_output_path}")

    except Exception as e:
        print(f"处理DEM时出错: {e}")
    finally:
        for src in src_files_to_mosaic:
            src.close()


    # --- LULC 处理 ---
    print("正在处理LULC...")
    reprojected_lulc_files = []
    try:
        # 1. 筛选相关文件
        relevant_lulc_files = get_files_in_bbox(all_lulc_tif_files, region_bbox_poly)
        if not relevant_lulc_files:
            raise FileNotFoundError(f"未找到与 {region_name} 区域重叠的LULC(.tif)文件。")
        print(f"找到 {len(relevant_lulc_files)} 个相关的LULC文件。")

        # 2. 重投影到统一CRS
        print("正在将LULC文件重投影到统一坐标系...")
        for i, fp in enumerate(relevant_lulc_files):
            reprojected_path = os.path.join(temp_dir, f"{i}_reprojected.tif")
            reproject_to_target_crs(fp, reprojected_path)
            reprojected_lulc_files.append(reprojected_path)

        # 3. 拼接
        src_files_to_mosaic = [rasterio.open(fp) for fp in reprojected_lulc_files]
        mosaic, out_trans = merge(src_files_to_mosaic)

        # 4. 裁剪
        out_meta = src_files_to_mosaic[0].meta.copy()
        out_meta.update({"driver": "GTiff", "height": mosaic.shape[1], "width": mosaic.shape[2], "transform": out_trans})
        
        with rasterio.io.MemoryFile() as memfile:
            with memfile.open(**out_meta) as dataset:
                dataset.write(mosaic)
            
            with memfile.open() as dataset:
                out_image, out_transform = mask(dataset, crop_geometry.geometry, crop=True, nodata=255)

        out_meta.update({"height": out_image.shape[1], "width": out_image.shape[2], "transform": out_transform})
        
        lulc_output_path = os.path.join(region_output_dir, "lulc.tif")
        with rasterio.open(lulc_output_path, "w", **out_meta) as dest:
            dest.write(out_image)
        print(f"LULC数据成功保存到: {lulc_output_path}")

    except Exception as e:
        print(f"处理LULC时出错: {e}")
    finally:
        for src in src_files_to_mosaic:
            src.close()
        # 清理临时文件
        for fp in reprojected_lulc_files:
            try:
                os.remove(fp)
            except OSError:
                pass
        try:
            os.rmdir(temp_dir)
        except OSError:
            pass

if __name__ == "__main__":
    regions_to_process = {
        "苏格兰高地": [-6, 56, -3, 58],
        "以色列-巴勒斯坦地区": [34, 31, 36, 33],
        "克什米尔地区": [74, 33, 78, 35],
    }
    
    # 注意：这里的路径需要指向新下载和解压的DEM文件
    all_dem_files = glob.glob("data/temp_dem_unzipped/**/*.hgt", recursive=True)
    all_lulc_files = glob.glob("data/LULC_raw/**/*.tif", recursive=True) # 假设LULC源文件被移动了
    output_data_dir = "data"

    if not all_dem_files or not all_lulc_files:
        print("警告: 原始的DEM或LULC文件未在预期位置找到。")
        print("请先运行下载和准备脚本。")
    else:
        for name, bbox in regions_to_process.items():
            process_region(name, bbox, all_dem_files, all_lulc_files, output_data_dir)

    print("\n所有数据完备区域处理完成。") 