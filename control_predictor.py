#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于深度学习的控制策略预测器 (The Brain of the System)
使用Transformer/GRU模型预测控制指令

核心功能：
1. 时序数据编码
2. 环境特征融合
3. 控制指令概率分布预测
4. 多模态支持（MDN）
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import math
from sklearn.preprocessing import StandardScaler
import joblib
import os

@dataclass
class ModelConfig:
    """模型配置"""
    # 输入特征维度
    sequence_length: int = 20          # 历史序列长度
    kinematic_dim: int = 7             # 运动学特征维度 (x,y,vx,vy,ax,ay,heading)
    environment_dim: int = 15          # 环境特征维度
    path_guidance_dim: int = 4         # 路径引导特征维度
    vehicle_type_dim: int = 4          # 车辆类型嵌入维度
    
    # 网络结构
    hidden_dim: int = 256              # 隐藏层维度
    num_layers: int = 4                # Transformer层数
    num_heads: int = 8                 # 注意力头数
    dropout: float = 0.1               # Dropout概率
    
    # 输出维度
    num_components: int = 3            # MDN混合成分数
    control_dim: int = 2               # 控制维度 (throttle_brake, steering)
    
    # 训练参数
    learning_rate: float = 1e-4
    batch_size: int = 32
    max_epochs: int = 100
    early_stop_patience: int = 10

class VehicleTypeEncoder(nn.Module):
    """车辆类型嵌入编码器"""
    
    def __init__(self, num_vehicle_types: int = 4, embed_dim: int = 16):
        super().__init__()
        self.embedding = nn.Embedding(num_vehicle_types, embed_dim)
        
        # 预定义车辆类型
        self.vehicle_types = {
            'standard': 0,      # 标准车辆
            'high_mobility': 1, # 高机动车辆
            'stealth': 2,       # 隐蔽车辆
            'mountain': 3       # 山地特种车辆
        }
        
    def forward(self, vehicle_type_ids: torch.Tensor) -> torch.Tensor:
        return self.embedding(vehicle_type_ids)

class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_seq_length: int = 100):
        super().__init__()
        
        pe = torch.zeros(max_seq_length, d_model)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:, :x.size(1)]

class TransformerControlPredictor(nn.Module):
    """基于Transformer的控制预测器"""
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        
        # 输入投影层
        self.kinematic_proj = nn.Linear(config.kinematic_dim, config.hidden_dim)
        self.environment_proj = nn.Linear(config.environment_dim, config.hidden_dim)
        self.path_guidance_proj = nn.Linear(config.path_guidance_dim, config.hidden_dim)
        
        # 车辆类型编码器
        self.vehicle_encoder = VehicleTypeEncoder(embed_dim=config.hidden_dim)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(config.hidden_dim, config.sequence_length)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.hidden_dim,
            nhead=config.num_heads,
            dim_feedforward=config.hidden_dim * 4,
            dropout=config.dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, config.num_layers)
        
        # 特征融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(config.hidden_dim * 3, config.hidden_dim),  # kinematic + env + path
            nn.ReLU(),
            nn.Dropout(config.dropout)
        )
        
        # 上下文注意力
        self.context_attention = nn.MultiheadAttention(
            config.hidden_dim, config.num_heads, batch_first=True
        )
        
        # MDN输出层
        self.output_layer = MDNOutputLayer(
            input_dim=config.hidden_dim,
            output_dim=config.control_dim,
            num_components=config.num_components
        )
        
    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            batch: 包含各种输入特征的字典
            
        Returns:
            MDN参数字典
        """
        batch_size = batch['kinematic_sequence'].shape[0]
        seq_len = batch['kinematic_sequence'].shape[1]
        
        # 1. 编码历史运动学序列
        kinematic_embed = self.kinematic_proj(batch['kinematic_sequence'])
        kinematic_embed = self.pos_encoding(kinematic_embed)
        
        # 2. Transformer编码
        kinematic_context = self.transformer(kinematic_embed)
        
        # 3. 当前环境特征编码
        env_embed = self.environment_proj(batch['environment_features'])
        
        # 4. 路径引导特征编码
        path_embed = self.path_guidance_proj(batch['path_guidance'])
        
        # 5. 车辆类型嵌入
        vehicle_embed = self.vehicle_encoder(batch['vehicle_type'])
        
        # 6. 特征融合
        # 取最后一个时间步的运动学上下文
        last_kinematic = kinematic_context[:, -1, :]
        
        # 融合当前特征
        current_features = torch.cat([
            last_kinematic,
            env_embed,
            path_embed
        ], dim=-1)
        
        fused_features = self.fusion_layer(current_features)
        
        # 7. 加入车辆类型信息
        fused_features = fused_features + vehicle_embed
        
        # 8. 上下文注意力（考虑整个历史序列）
        attended_features, attention_weights = self.context_attention(
            fused_features.unsqueeze(1),  # Query
            kinematic_context,            # Key
            kinematic_context             # Value
        )
        
        final_features = attended_features.squeeze(1)
        
        # 9. MDN输出
        mdn_params = self.output_layer(final_features)
        
        return mdn_params

class MDNOutputLayer(nn.Module):
    """混合密度网络输出层"""
    
    def __init__(self, input_dim: int, output_dim: int, num_components: int):
        super().__init__()
        self.output_dim = output_dim
        self.num_components = num_components
        
        # 混合权重
        self.pi_layer = nn.Linear(input_dim, num_components)
        
        # 均值
        self.mu_layer = nn.Linear(input_dim, output_dim * num_components)
        
        # 标准差（对数域）
        self.sigma_layer = nn.Linear(input_dim, output_dim * num_components)
        
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        batch_size = x.shape[0]
        
        # 混合权重（softmax归一化）
        pi = F.softmax(self.pi_layer(x), dim=-1)
        
        # 均值
        mu = self.mu_layer(x).view(batch_size, self.num_components, self.output_dim)
        
        # 标准差（确保为正）
        log_sigma = self.sigma_layer(x).view(batch_size, self.num_components, self.output_dim)
        sigma = torch.exp(log_sigma) + 1e-6  # 避免零方差
        
        return {
            'pi': pi,           # [batch_size, num_components]
            'mu': mu,           # [batch_size, num_components, output_dim]
            'sigma': sigma      # [batch_size, num_components, output_dim]
        }

class TrajectoryDataset(Dataset):
    """轨迹数据集"""
    
    def __init__(self, processed_trajectories: List[pd.DataFrame], 
                 config: ModelConfig, scalers: Optional[Dict] = None):
        """
        初始化数据集
        
        Args:
            processed_trajectories: 预处理后的轨迹列表
            config: 模型配置
            scalers: 特征标准化器字典
        """
        self.config = config
        self.scalers = scalers or {}
        self.samples = []
        
        # 提取所有样本
        for traj_df in processed_trajectories:
            samples = self._extract_samples_from_trajectory(traj_df)
            self.samples.extend(samples)
            
        print(f"数据集初始化完成，总样本数: {len(self.samples)}")
        
    def _extract_samples_from_trajectory(self, traj_df: pd.DataFrame) -> List[Dict]:
        """从单条轨迹中提取训练样本"""
        samples = []
        seq_len = self.config.sequence_length
        
        # 确保轨迹足够长
        if len(traj_df) <= seq_len:
            return samples
            
        # 定义特征列
        kinematic_cols = ['x', 'y', 'velocity_x', 'velocity_y', 
                         'acceleration_x', 'acceleration_y', 'heading']
        
        env_cols = [col for col in traj_df.columns if col.startswith('landcover_') or 
                   col in ['dem', 'slope', 'aspect', 'landcover', 'local_slope_magnitude', 'local_slope_direction']]
        
        control_cols = ['control_throttle_brake', 'control_steering_angle']
        
        # 滑动窗口提取样本
        for i in range(seq_len, len(traj_df)):
            # 历史序列
            kinematic_sequence = traj_df.iloc[i-seq_len:i][kinematic_cols].values
            
            # 当前环境特征
            env_features = traj_df.iloc[i][env_cols].values
            
            # 路径引导特征（简化版，可以后续扩展）
            path_guidance = self._calculate_path_guidance(traj_df, i)
            
            # 控制标签
            control_target = traj_df.iloc[i][control_cols].values
            
            # 车辆类型（从轨迹推断或预设）
            vehicle_type = self._infer_vehicle_type(traj_df)
            
            sample = {
                'kinematic_sequence': kinematic_sequence.astype(np.float32),
                'environment_features': env_features.astype(np.float32),
                'path_guidance': path_guidance.astype(np.float32),
                'vehicle_type': vehicle_type,
                'control_target': control_target.astype(np.float32)
            }
            
            samples.append(sample)
            
        return samples
        
    def _calculate_path_guidance(self, traj_df: pd.DataFrame, current_idx: int) -> np.ndarray:
        """计算路径引导特征"""
        # 简化版：使用未来几个点的方向和距离
        look_ahead = min(10, len(traj_df) - current_idx - 1)
        
        if look_ahead == 0:
            return np.array([0.0, 0.0, 0.0, 0.0])
            
        current_pos = traj_df.iloc[current_idx][['x', 'y']].values
        future_pos = traj_df.iloc[current_idx + look_ahead][['x', 'y']].values
        
        # 相对位置
        rel_pos = future_pos - current_pos
        distance = np.linalg.norm(rel_pos)
        
        if distance > 0:
            direction = rel_pos / distance
            # 用三角函数编码方向
            return np.array([
                direction[0], direction[1],  # 单位方向向量
                distance,                     # 距离
                traj_df.iloc[current_idx]['path_curvature']  # 当前曲率
            ])
        else:
            return np.array([0.0, 0.0, 0.0, 0.0])
            
    def _infer_vehicle_type(self, traj_df: pd.DataFrame) -> int:
        """推断车辆类型（简化版）"""
        # 基于速度特征简单推断
        avg_speed = traj_df['speed'].mean()
        max_speed = traj_df['speed'].max()
        
        if max_speed > 25.0:
            return 1  # high_mobility
        elif avg_speed < 10.0:
            return 2  # stealth
        else:
            return 0  # standard
            
    def __len__(self) -> int:
        return len(self.samples)
        
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.samples[idx]
        
        # 转换为张量
        return {
            'kinematic_sequence': torch.from_numpy(sample['kinematic_sequence']),
            'environment_features': torch.from_numpy(sample['environment_features']),
            'path_guidance': torch.from_numpy(sample['path_guidance']),
            'vehicle_type': torch.tensor(sample['vehicle_type'], dtype=torch.long),
            'control_target': torch.from_numpy(sample['control_target'])
        }

class MDNLoss(nn.Module):
    """混合密度网络损失函数"""
    
    def __init__(self, reduction: str = 'mean'):
        super().__init__()
        self.reduction = reduction
        
    def forward(self, mdn_params: Dict[str, torch.Tensor], 
                targets: torch.Tensor) -> torch.Tensor:
        """
        计算MDN负对数似然损失
        
        Args:
            mdn_params: MDN输出参数
            targets: 目标控制值
            
        Returns:
            损失值
        """
        pi = mdn_params['pi']       # [batch_size, num_components]
        mu = mdn_params['mu']       # [batch_size, num_components, output_dim]
        sigma = mdn_params['sigma'] # [batch_size, num_components, output_dim]
        
        batch_size, num_components, output_dim = mu.shape
        
        # 扩展目标维度以匹配混合成分
        targets = targets.unsqueeze(1).expand(-1, num_components, -1)
        
        # 计算每个混合成分的概率密度
        diff = targets - mu
        
        # 多变量高斯概率密度（假设对角协方差）
        log_prob_components = -0.5 * torch.sum((diff / sigma) ** 2, dim=-1) - \
                             torch.sum(torch.log(sigma), dim=-1) - \
                             0.5 * output_dim * math.log(2 * math.pi)
        
        # 加权求和
        log_prob_weighted = log_prob_components + torch.log(pi + 1e-8)
        
        # LogSumExp for numerical stability
        max_log_prob = torch.max(log_prob_weighted, dim=1, keepdim=True)[0]
        log_prob_total = max_log_prob + torch.log(
            torch.sum(torch.exp(log_prob_weighted - max_log_prob), dim=1, keepdim=True)
        )
        
        # 负对数似然
        nll = -log_prob_total.squeeze()
        
        if self.reduction == 'mean':
            return torch.mean(nll)
        elif self.reduction == 'sum':
            return torch.sum(nll)
        else:
            return nll

class ControlPredictorTrainer:
    """控制预测器训练器"""
    
    def __init__(self, config: ModelConfig, device: str = 'cuda'):
        self.config = config
        self.device = device
        
        # 初始化模型
        self.model = TransformerControlPredictor(config).to(device)
        
        # 损失函数和优化器
        self.criterion = MDNLoss()
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config.learning_rate)
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', patience=5, factor=0.5
        )
        
    def train(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict:
        """训练模型"""
        best_val_loss = float('inf')
        patience_counter = 0
        history = {'train_loss': [], 'val_loss': []}
        
        for epoch in range(self.config.max_epochs):
            # 训练阶段
            train_loss = self._train_epoch(train_loader)
            
            # 验证阶段
            val_loss = self._validate_epoch(val_loader)
            
            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            print(f"Epoch {epoch+1}/{self.config.max_epochs}: "
                  f"Train Loss = {train_loss:.4f}, Val Loss = {val_loss:.4f}")
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_control_predictor.pth')
            else:
                patience_counter += 1
                
            if patience_counter >= self.config.early_stop_patience:
                print(f"早停于第 {epoch+1} 轮")
                break
                
        return history
        
    def _train_epoch(self, train_loader: DataLoader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        
        for batch in train_loader:
            # 移动到设备
            batch = {k: v.to(self.device) for k, v in batch.items()}
            targets = batch.pop('control_target')
            
            # 前向传播
            self.optimizer.zero_grad()
            mdn_params = self.model(batch)
            loss = self.criterion(mdn_params, targets)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            
        return total_loss / len(train_loader)
        
    def _validate_epoch(self, val_loader: DataLoader) -> float:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for batch in val_loader:
                # 移动到设备
                batch = {k: v.to(self.device) for k, v in batch.items()}
                targets = batch.pop('control_target')
                
                # 前向传播
                mdn_params = self.model(batch)
                loss = self.criterion(mdn_params, targets)
                
                total_loss += loss.item()
                
        return total_loss / len(val_loader)

def sample_from_mdn(mdn_params: Dict[str, torch.Tensor], 
                    num_samples: int = 1) -> torch.Tensor:
    """从MDN采样控制指令"""
    pi = mdn_params['pi']       # [batch_size, num_components]
    mu = mdn_params['mu']       # [batch_size, num_components, output_dim]
    sigma = mdn_params['sigma'] # [batch_size, num_components, output_dim]
    
    batch_size = pi.shape[0]
    output_dim = mu.shape[-1]
    
    samples = []
    
    for b in range(batch_size):
        # 选择混合成分
        component_idx = torch.multinomial(pi[b], num_samples, replacement=True)
        
        batch_samples = []
        for s in range(num_samples):
            # 选择的成分
            comp = component_idx[s].item()
            
            # 从选定的高斯分布采样
            sample = torch.normal(mu[b, comp], sigma[b, comp])
            batch_samples.append(sample)
            
        samples.append(torch.stack(batch_samples))
        
    return torch.stack(samples)  # [batch_size, num_samples, output_dim]

if __name__ == "__main__":
    # 示例使用
    config = ModelConfig()
    
    # 加载预处理数据
    # processed_trajectories = [...]  # 从data_preprocessing模块加载
    
    # 创建数据集和数据加载器
    # dataset = TrajectoryDataset(processed_trajectories, config)
    # train_loader = DataLoader(dataset, batch_size=config.batch_size, shuffle=True)
    
    # 训练模型
    # trainer = ControlPredictorTrainer(config)
    # history = trainer.train(train_loader, val_loader)
    
    print("控制预测器模块创建完成")