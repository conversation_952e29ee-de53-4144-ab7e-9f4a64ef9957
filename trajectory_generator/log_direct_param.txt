加载环境数据...

加载环境地图...
环境地图加载完成
地图范围: BoundingBox(left=-1028768.5496971096, bottom=8799609.271101762, right=-929008.261578123, top=8899574.370665655)
创建验证器...
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_林地.csv
已加载林地的速度-坡度模型: speed = -0.0145 * slope + 3.32
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_灌木地.csv
已加载灌木地的速度-坡度模型: speed = -0.0096 * slope + 4.37
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_水体.csv
已加载水体的速度-坡度模型: speed = -0.0004 * slope + 1.40
已加载轨迹1-林地的模型: speed = -0.0507 * slope + 4.68
已加载轨迹1-灌木地的模型: speed = -0.0317 * slope + 4.80
已加载轨迹1-水体的模型: speed = 0.0000 * slope + 1.45
轨迹1已加载3个地类模型
已加载轨迹2-林地的模型: speed = -0.0015 * slope + 4.07
已加载轨迹2-灌木地的模型: speed = 0.0131 * slope + 3.92
已加载轨迹2-水体的模型: speed = 0.0002 * slope + 1.35
轨迹2已加载3个地类模型
已加载轨迹3-林地的模型: speed = -0.0059 * slope + 4.51
已加载轨迹3-灌木地的模型: speed = -0.0160 * slope + 4.38
已加载轨迹3-水体的模型: speed = -0.0019 * slope + 1.49
轨迹3已加载3个地类模型
加载轨迹4-林地模型失败: 'slope_bin'
已加载轨迹4-灌木地的模型: speed = -0.0037 * slope + 4.37
已加载轨迹4-水体的模型: speed = -0.0001 * slope + 1.33
轨迹4已加载2个地类模型
轨迹1-林地的残差统计: 均值=0.000, 标准差=1.766
轨迹1-灌木地的残差统计: 均值=0.000, 标准差=1.255
轨迹1-水体的残差统计: 均值=0.000, 标准差=2.303
轨迹2-林地的残差统计: 均值=0.000, 标准差=2.588
轨迹2-灌木地的残差统计: 均值=0.000, 标准差=1.922
轨迹2-水体的残差统计: 均值=0.000, 标准差=2.207
轨迹3-林地的残差统计: 均值=0.000, 标准差=2.882
轨迹3-灌木地的残差统计: 均值=0.000, 标准差=1.850
轨迹3-水体的残差统计: 均值=0.000, 标准差=2.426
轨迹4-灌木地的残差统计: 均值=0.000, 标准差=1.831
轨迹4-水体的残差统计: 均值=0.000, 标准差=2.181
加载并处理轨迹...
处理轨迹 1: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_1.csv
处理轨迹 2: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_2.csv
处理轨迹 3: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_3.csv
处理轨迹 4: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_4.csv

开始验证...

处理轨迹 1

开始轨迹验证...
坐标类型检查:
x范围: -982005.4 ~ -975744.4
y范围: 8846741.0 ~ 8852438.0
判断为UTM坐标

使用原始轨迹的所有点: 7078个点
提取的原始速度: 均值=5.08m/s, 标准差=1.56m/s

开始生成轨迹...
输入航点数量: 7078
初始状态: {'x0': -981867.09145351, 'y0': 8846764.180494713, 'vx0': -0.089999996125698, 'vy0': -0.4399999976158142, 'heading0': 258.4398696290711}
目标轨迹点数量: 7078
使用原始时间戳: 7078个点
将匹配原始轨迹点数: 7078点
使用原始速度作为参考: 7078个点
轨迹点过多，跳过预计算曲率

当前位置: (-981541.20, 8847365.39)
目标航点索引: 532/7078
目标航点: (-981521.89, 8847375.54)
轨迹点进度: 1000/7078 (14.1%)

当前位置: (-980682.60, 8847980.15)
目标航点索引: 826/7078
目标航点: (-980686.15, 8847995.89)
轨迹点进度: 2000/7078 (28.3%)

当前位置: (-979884.60, 8848234.47)
目标航点索引: 1134/7078
目标航点: (-979878.56, 8848265.48)
轨迹点进度: 3000/7078 (42.4%)

当前位置: (-979331.74, 8849085.70)
目标航点索引: 1442/7078
目标航点: (-979304.63, 8849106.71)
轨迹点进度: 4000/7078 (56.5%)

当前位置: (-978658.13, 8849820.80)
目标航点索引: 1722/7078
目标航点: (-978646.57, 8849829.34)
轨迹点进度: 5000/7078 (70.6%)

当前位置: (-978027.17, 8850709.83)
目标航点索引: 2086/7078
目标航点: (-977978.72, 8850753.89)
轨迹点进度: 6000/7078 (84.8%)

当前位置: (-977857.98, 8851259.72)
目标航点索引: 2296/7078
目标航点: (-977865.48, 8851265.33)
轨迹点进度: 7000/7078 (98.9%)
已生成足够的轨迹点 (7078/7078)，提前结束

轨迹生成完成，总步数: 7078
应用原始时间戳...

统计指标:
original_mean_speed: 5.078
generated_mean_speed: 4.109
original_std_speed: 1.562
generated_std_speed: 1.318
speed_correlation: 0.076
hausdorff_distance: 2386.423
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 2

开始轨迹验证...
坐标类型检查:
x范围: -982035.2 ~ -975741.6
y范围: 8846750.4 ~ 8852437.7
判断为UTM坐标

使用原始轨迹的所有点: 8627个点
提取的原始速度: 均值=4.24m/s, 标准差=2.28m/s

开始生成轨迹...
输入航点数量: 8627
初始状态: {'x0': -981865.3072710792, 'y0': 8846759.273459952, 'vx0': 0.3100000023841858, 'vy0': 0.2399999946355819, 'heading0': 37.74680455405379}
目标轨迹点数量: 8627
使用原始时间戳: 8627个点
将匹配原始轨迹点数: 8627点
使用原始速度作为参考: 8627个点
轨迹点过多，跳过预计算曲率

当前位置: (-981938.67, 8847027.81)
目标航点索引: 1275/8627
目标航点: (-981926.92, 8847043.10)
轨迹点进度: 1000/8627 (11.6%)

当前位置: (-981043.18, 8847641.67)
目标航点索引: 1513/8627
目标航点: (-981020.08, 8847649.10)
轨迹点进度: 2000/8627 (23.2%)

当前位置: (-980365.97, 8848252.29)
目标航点索引: 1836/8627
目标航点: (-980316.09, 8848241.70)
轨迹点进度: 3000/8627 (34.8%)

当前位置: (-979760.23, 8848678.54)
目标航点索引: 2142/8627
目标航点: (-979746.73, 8848704.02)
轨迹点进度: 4000/8627 (46.4%)

当前位置: (-979021.35, 8849437.67)
目标航点索引: 2431/8627
目标航点: (-979002.08, 8849479.24)
轨迹点进度: 5000/8627 (58.0%)

当前位置: (-978439.21, 8850248.86)
目标航点索引: 2737/8627
目标航点: (-978427.83, 8850263.78)
轨迹点进度: 6000/8627 (69.5%)

当前位置: (-977744.54, 8850943.04)
目标航点索引: 3009/8627
目标航点: (-977721.43, 8850966.11)
轨迹点进度: 7000/8627 (81.1%)

当前位置: (-977876.22, 8851552.68)
目标航点索引: 3247/8627
目标航点: (-977840.71, 8851605.70)
轨迹点进度: 8000/8627 (92.7%)
已生成足够的轨迹点 (8627/8627)，提前结束

轨迹生成完成，总步数: 8627
应用原始时间戳...

统计指标:
original_mean_speed: 4.244
generated_mean_speed: 3.917
original_std_speed: 2.280
generated_std_speed: 1.380
speed_correlation: 0.204
hausdorff_distance: 2378.607
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 3

开始轨迹验证...
坐标类型检查:
x范围: -982006.4 ~ -975744.9
y范围: 8846750.4 ~ 8852440.7
判断为UTM坐标

使用原始轨迹的所有点: 7091个点
提取的原始速度: 均值=5.05m/s, 标准差=1.88m/s

开始生成轨迹...
输入航点数量: 7091
初始状态: {'x0': -981862.8558158012, 'y0': 8846758.80141532, 'vx0': 0.0099999997764825, 'vy0': 0.0999999940395355, 'heading0': 84.28940665117028}
目标轨迹点数量: 7091
使用原始时间戳: 7091个点
将匹配原始轨迹点数: 7091点
使用原始速度作为参考: 7091个点
轨迹点过多，跳过预计算曲率

当前位置: (-981604.69, 8847335.43)
目标航点索引: 644/7091
目标航点: (-981551.43, 8847365.54)
轨迹点进度: 1000/7091 (14.1%)

当前位置: (-980696.48, 8847915.57)
目标航点索引: 952/7091
目标航点: (-980689.22, 8847926.32)
轨迹点进度: 2000/7091 (28.2%)

当前位置: (-979911.79, 8848140.94)
目标航点索引: 1218/7091
目标航点: (-979884.57, 8848164.80)
轨迹点进度: 3000/7091 (42.3%)

当前位置: (-979389.96, 8849032.05)
目标航点索引: 1512/7091
目标航点: (-979376.09, 8849045.18)
轨迹点进度: 4000/7091 (56.4%)

当前位置: (-978717.81, 8849778.21)
目标航点索引: 1764/7091
目标航点: (-978661.70, 8849813.02)
轨迹点进度: 5000/7091 (70.5%)

当前位置: (-978085.10, 8850660.04)
目标航点索引: 2044/7091
目标航点: (-978062.45, 8850681.04)
轨迹点进度: 6000/7091 (84.6%)

当前位置: (-977832.96, 8851228.08)
目标航点索引: 2254/7091
目标航点: (-977856.30, 8851246.10)
轨迹点进度: 7000/7091 (98.7%)
已生成足够的轨迹点 (7091/7091)，提前结束

轨迹生成完成，总步数: 7091
应用原始时间戳...

统计指标:
original_mean_speed: 5.048
generated_mean_speed: 4.101
original_std_speed: 1.884
generated_std_speed: 1.322
speed_correlation: 0.169
hausdorff_distance: 2363.795
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 4

开始轨迹验证...
坐标类型检查:
x范围: -982008.3 ~ -975750.5
y范围: 8846745.1 ~ 8852442.7
判断为UTM坐标

使用原始轨迹的所有点: 8073个点
提取的原始速度: 均值=4.47m/s, 标准差=2.26m/s

开始生成轨迹...
输入航点数量: 8073
初始状态: {'x0': -981859.934680198, 'y0': 8846758.975567885, 'vx0': 0.0599999986588954, 'vy0': -0.0599999986588954, 'heading0': 315.0}
目标轨迹点数量: 8073
使用原始时间戳: 8073个点
将匹配原始轨迹点数: 8073点
使用原始速度作为参考: 8073个点
轨迹点过多，跳过预计算曲率

当前位置: (-981861.29, 8847110.81)
目标航点索引: 1376/8073
目标航点: (-981824.16, 8847163.74)
轨迹点进度: 1000/8073 (12.4%)

当前位置: (-980949.63, 8847703.20)
目标航点索引: 1568/8073
目标航点: (-980956.44, 8847736.91)
轨迹点进度: 2000/8073 (24.8%)

当前位置: (-980223.04, 8848201.99)
目标航点索引: 1872/8073
目标航点: (-980203.95, 8848195.06)
轨迹点进度: 3000/8073 (37.2%)

当前位置: (-979635.63, 8848831.21)
目标航点索引: 2192/8073
目标航点: (-979593.99, 8848866.54)
轨迹点进度: 4000/8073 (49.5%)

当前位置: (-978938.36, 8849584.05)
目标航点索引: 2480/8073
目标航点: (-978923.12, 8849607.03)
轨迹点进度: 5000/8073 (61.9%)

当前位置: (-978304.29, 8850379.00)
目标航点索引: 2768/8073
目标航点: (-978278.55, 8850422.50)
轨迹点进度: 6000/8073 (74.3%)

当前位置: (-977698.94, 8851033.37)
目标航点索引: 2976/8073
目标航点: (-977709.84, 8851064.65)
轨迹点进度: 7000/8073 (86.7%)

当前位置: (-977793.99, 8851677.53)
目标航点索引: 3200/8073
目标航点: (-977765.14, 8851722.41)
轨迹点进度: 8000/8073 (99.1%)
已生成足够的轨迹点 (8073/8073)，提前结束

轨迹生成完成，总步数: 8073
应用原始时间戳...

统计指标:
original_mean_speed: 4.470
generated_mean_speed: 3.891
original_std_speed: 2.260
generated_std_speed: 1.351
speed_correlation: 0.216
hausdorff_distance: 2376.424
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results
