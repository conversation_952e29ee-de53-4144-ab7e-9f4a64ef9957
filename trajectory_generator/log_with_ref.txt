加载环境数据...

加载环境地图...
环境地图加载完成
地图范围: BoundingBox(left=-1028768.5496971096, bottom=8799609.271101762, right=-929008.261578123, top=8899574.370665655)
创建验证器...
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_林地.csv
已加载林地的速度-坡度模型: speed = -0.0145 * slope + 3.32
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_灌木地.csv
已加载灌木地的速度-坡度模型: speed = -0.0096 * slope + 4.37
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_水体.csv
已加载水体的速度-坡度模型: speed = -0.0004 * slope + 1.40
已加载轨迹1-林地的模型: speed = -0.0507 * slope + 4.68
已加载轨迹1-灌木地的模型: speed = -0.0317 * slope + 4.80
已加载轨迹1-水体的模型: speed = 0.0000 * slope + 1.45
轨迹1已加载3个地类模型
已加载轨迹2-林地的模型: speed = -0.0015 * slope + 4.07
已加载轨迹2-灌木地的模型: speed = 0.0131 * slope + 3.92
已加载轨迹2-水体的模型: speed = 0.0002 * slope + 1.35
轨迹2已加载3个地类模型
已加载轨迹3-林地的模型: speed = -0.0059 * slope + 4.51
已加载轨迹3-灌木地的模型: speed = -0.0160 * slope + 4.38
已加载轨迹3-水体的模型: speed = -0.0019 * slope + 1.49
轨迹3已加载3个地类模型
加载轨迹4-林地模型失败: 'slope_bin'
已加载轨迹4-灌木地的模型: speed = -0.0037 * slope + 4.37
已加载轨迹4-水体的模型: speed = -0.0001 * slope + 1.33
轨迹4已加载2个地类模型
轨迹1-林地的残差统计: 均值=0.000, 标准差=1.766
轨迹1-灌木地的残差统计: 均值=0.000, 标准差=1.255
轨迹1-水体的残差统计: 均值=0.000, 标准差=2.303
轨迹2-林地的残差统计: 均值=0.000, 标准差=2.588
轨迹2-灌木地的残差统计: 均值=0.000, 标准差=1.922
轨迹2-水体的残差统计: 均值=0.000, 标准差=2.207
轨迹3-林地的残差统计: 均值=0.000, 标准差=2.882
轨迹3-灌木地的残差统计: 均值=0.000, 标准差=1.850
轨迹3-水体的残差统计: 均值=0.000, 标准差=2.426
轨迹4-灌木地的残差统计: 均值=0.000, 标准差=1.831
轨迹4-水体的残差统计: 均值=0.000, 标准差=2.181
加载并处理轨迹...
处理轨迹 1: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_1.csv
处理轨迹 2: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_2.csv
处理轨迹 3: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_3.csv
处理轨迹 4: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_4.csv

开始验证...

处理轨迹 1

开始轨迹验证...
坐标类型检查:
x范围: -982005.4 ~ -975744.4
y范围: 8846741.0 ~ 8852438.0
判断为UTM坐标

使用原始轨迹的所有点: 7078个点
提取的原始速度: 均值=5.08m/s, 标准差=1.56m/s

开始生成轨迹...
输入航点数量: 7078
初始状态: {'x0': -981867.09145351, 'y0': 8846764.180494713, 'vx0': -0.089999996125698, 'vy0': -0.4399999976158142, 'heading0': 258.4398696290711}
目标轨迹点数量: 7078
使用原始时间戳: 7078个点
将匹配原始轨迹点数: 7078点
使用原始速度作为参考: 7078个点
轨迹点过多，跳过预计算曲率

当前位置: (-981186.57, 8847556.77)
目标航点索引: 616/7078
目标航点: (-981161.92, 8847567.56)
轨迹点进度: 1000/7078 (14.1%)

当前位置: (-979883.31, 8848241.56)
目标航点索引: 1134/7078
目标航点: (-979878.56, 8848265.48)
轨迹点进度: 2000/7078 (28.3%)

当前位置: (-978875.89, 8849673.57)
目标航点索引: 1666/7078
目标航点: (-978851.63, 8849698.26)
轨迹点进度: 3000/7078 (42.4%)

当前位置: (-977803.90, 8851214.71)
目标航点索引: 2282/7078
目标航点: (-977826.57, 8851236.72)
轨迹点进度: 4000/7078 (56.5%)

当前位置: (-977407.51, 8852446.07)
目标航点索引: 2744/7078
目标航点: (-977405.46, 8852437.71)
轨迹点进度: 5000/7078 (70.6%)

当前位置: (-977544.74, 8852257.26)
目标航点索引: 2898/7078
目标航点: (-977557.79, 8852231.11)
轨迹点进度: 6000/7078 (84.8%)

当前位置: (-977334.56, 8850812.69)
目标航点索引: 3556/7078
目标航点: (-977290.10, 8850820.85)
轨迹点进度: 7000/7078 (98.9%)
已生成足够的轨迹点 (7078/7078)，提前结束

轨迹生成完成，总步数: 7078
应用原始时间戳...

统计指标:
original_mean_speed: 5.078
generated_mean_speed: 7.900
original_std_speed: 1.562
generated_std_speed: 2.153
speed_correlation: 0.882
hausdorff_distance: 1942.344
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 2

开始轨迹验证...
坐标类型检查:
x范围: -982035.2 ~ -975741.6
y范围: 8846750.4 ~ 8852437.7
判断为UTM坐标

使用原始轨迹的所有点: 8627个点
提取的原始速度: 均值=4.24m/s, 标准差=2.28m/s

开始生成轨迹...
输入航点数量: 8627
初始状态: {'x0': -981865.3072710792, 'y0': 8846759.273459952, 'vx0': 0.3100000023841858, 'vy0': 0.2399999946355819, 'heading0': 37.74680455405379}
目标轨迹点数量: 8627
使用原始时间戳: 8627个点
将匹配原始轨迹点数: 8627点
使用原始速度作为参考: 8627个点
轨迹点过多，跳过预计算曲率

当前位置: (-982008.54, 8846939.30)
目标航点索引: 1224/8627
目标航点: (-981992.78, 8846961.37)
轨迹点进度: 1000/8627 (11.6%)

当前位置: (-980831.49, 8847828.00)
目标航点索引: 1615/8627
目标航点: (-980794.93, 8847846.26)
轨迹点进度: 2000/8627 (23.2%)

当前位置: (-979777.28, 8848646.45)
目标航点索引: 2142/8627
目标航点: (-979746.73, 8848704.02)
轨迹点进度: 3000/8627 (34.8%)

当前位置: (-978599.53, 8849937.31)
目标航点索引: 2618/8627
目标航点: (-978601.93, 8849967.44)
轨迹点进度: 4000/8627 (46.4%)

当前位置: (-977926.90, 8851408.14)
目标航点索引: 3196/8627
目标航点: (-977922.93, 8851448.83)
轨迹点进度: 5000/8627 (58.0%)

当前位置: (-977454.31, 8852361.50)
目标航点索引: 3502/8627
目标航点: (-977434.52, 8852382.72)
轨迹点进度: 6000/8627 (69.5%)

当前位置: (-977695.44, 8851004.55)
目标航点索引: 4148/8627
目标航点: (-977722.22, 8850963.56)
轨迹点进度: 7000/8627 (81.1%)

当前位置: (-976455.38, 8850396.10)
目标航点索引: 4760/8627
目标航点: (-976450.25, 8850386.20)
轨迹点进度: 8000/8627 (92.7%)
已生成足够的轨迹点 (8627/8627)，提前结束

轨迹生成完成，总步数: 8627
应用原始时间戳...

统计指标:
original_mean_speed: 4.244
generated_mean_speed: 6.716
original_std_speed: 2.280
generated_std_speed: 2.619
speed_correlation: 0.914
hausdorff_distance: 47.123
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 3

开始轨迹验证...
坐标类型检查:
x范围: -982006.4 ~ -975744.9
y范围: 8846750.4 ~ 8852440.7
判断为UTM坐标

使用原始轨迹的所有点: 7091个点
提取的原始速度: 均值=5.05m/s, 标准差=1.88m/s

开始生成轨迹...
输入航点数量: 7091
初始状态: {'x0': -981862.8558158012, 'y0': 8846758.80141532, 'vx0': 0.0099999997764825, 'vy0': 0.0999999940395355, 'heading0': 84.28940665117028}
目标轨迹点数量: 7091
使用原始时间戳: 7091个点
将匹配原始轨迹点数: 7091点
使用原始速度作为参考: 7091个点
轨迹点过多，跳过预计算曲率

当前位置: (-981330.00, 8847492.63)
目标航点索引: 714/7091
目标航点: (-981287.71, 8847510.79)
轨迹点进度: 1000/7091 (14.1%)

当前位置: (-979876.48, 8848227.07)
目标航点索引: 1246/7091
目标航点: (-979875.08, 8848234.93)
轨迹点进度: 2000/7091 (28.2%)

当前位置: (-978794.45, 8849733.94)
目标航点索引: 1736/7091
目标航点: (-978791.05, 8849736.17)
轨迹点进度: 3000/7091 (42.3%)
到达航点，前往下一个索引: 1750

当前位置: (-978794.45, 8849733.94)
目标航点索引: 1750/7091
目标航点: (-978721.40, 8849775.92)
轨迹点进度: 3000/7091 (42.3%)

当前位置: (-977912.58, 8851305.87)
目标航点索引: 2282/7091
目标航点: (-977921.47, 8851321.03)
轨迹点进度: 4000/7091 (56.4%)

当前位置: (-977516.49, 8852295.18)
目标航点索引: 2814/7091
目标航点: (-977518.65, 8852292.47)
轨迹点进度: 5000/7091 (70.5%)
到达航点，前往下一个索引: 2828

当前位置: (-977516.49, 8852295.18)
目标航点索引: 2828/7091
目标航点: (-977540.67, 8852254.16)
轨迹点进度: 5000/7091 (70.5%)

当前位置: (-977371.31, 8850797.46)
目标航点索引: 3458/7091
目标航点: (-977366.96, 8850798.22)
轨迹点进度: 6000/7091 (84.6%)
到达航点，前往下一个索引: 3472

当前位置: (-977371.31, 8850797.46)
目标航点索引: 3472/7091
目标航点: (-977288.46, 8850814.36)
轨迹点进度: 6000/7091 (84.6%)

当前位置: (-975758.74, 8849740.50)
目标航点索引: 4074/7091
目标航点: (-975756.71, 8849737.65)
轨迹点进度: 7000/7091 (98.7%)
到达航点，前往下一个索引: 4088

当前位置: (-975758.74, 8849740.50)
目标航点索引: 4088/7091
目标航点: (-975754.69, 8849698.05)
轨迹点进度: 7000/7091 (98.7%)
已生成足够的轨迹点 (7091/7091)，提前结束

轨迹生成完成，总步数: 7091
应用原始时间戳...

统计指标:
original_mean_speed: 5.048
generated_mean_speed: 7.847
original_std_speed: 1.884
generated_std_speed: 2.301
speed_correlation: 0.900
hausdorff_distance: 26.043
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 4

开始轨迹验证...
坐标类型检查:
x范围: -982008.3 ~ -975750.5
y范围: 8846745.1 ~ 8852442.7
判断为UTM坐标

使用原始轨迹的所有点: 8073个点
提取的原始速度: 均值=4.47m/s, 标准差=2.26m/s

开始生成轨迹...
输入航点数量: 8073
初始状态: {'x0': -981859.934680198, 'y0': 8846758.975567885, 'vx0': 0.0599999986588954, 'vy0': -0.0599999986588954, 'heading0': 315.0}
目标轨迹点数量: 8073
使用原始时间戳: 8073个点
将匹配原始轨迹点数: 8073点
使用原始速度作为参考: 8073个点
轨迹点过多，跳过预计算曲率

当前位置: (-981964.83, 8846992.18)
目标航点索引: 1280/8073
目标航点: (-981955.46, 8847002.39)
轨迹点进度: 1000/8073 (12.4%)

当前位置: (-980713.20, 8847895.56)
目标航点索引: 1664/8073
目标航点: (-980694.19, 8847914.29)
轨迹点进度: 2000/8073 (24.8%)

当前位置: (-979581.88, 8848875.90)
目标航点索引: 2208/8073
目标航点: (-979528.74, 8848916.45)
轨迹点进度: 3000/8073 (37.2%)

当前位置: (-978451.16, 8850232.77)
目标航点索引: 2720/8073
目标航点: (-978406.97, 8850284.06)
轨迹点进度: 4000/8073 (49.5%)

当前位置: (-977724.03, 8851813.75)
目标航点索引: 3232/8073
目标航点: (-977722.13, 8851819.46)
轨迹点进度: 5000/8073 (61.9%)

当前位置: (-977608.27, 8852111.05)
目标航点索引: 3712/8073
目标航点: (-977611.58, 8852104.72)
轨迹点进度: 6000/8073 (74.3%)

当前位置: (-977169.66, 8850842.26)
目标航点索引: 4336/8073
目标航点: (-977113.78, 8850855.64)
轨迹点进度: 7000/8073 (86.7%)

当前位置: (-975768.68, 8849649.50)
目标航点索引: 4928/8073
目标航点: (-975773.88, 8849629.49)
轨迹点进度: 8000/8073 (99.1%)
已生成足够的轨迹点 (8073/8073)，提前结束

轨迹生成完成，总步数: 8073
应用原始时间戳...

统计指标:
original_mean_speed: 4.470
generated_mean_speed: 7.112
original_std_speed: 2.260
generated_std_speed: 2.622
speed_correlation: 0.924
hausdorff_distance: 18.357
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results
