加载环境数据...

加载环境地图...
环境地图加载完成
地图范围: BoundingBox(left=-1028768.5496971096, bottom=8799609.271101762, right=-929008.261578123, top=8899574.370665655)
创建验证器...
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_林地.csv
已加载林地的速度-坡度模型: speed = -0.0145 * slope + 3.32
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_灌木地.csv
已加载灌木地的速度-坡度模型: speed = -0.0096 * slope + 4.37
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_水体.csv
已加载水体的速度-坡度模型: speed = -0.0004 * slope + 1.40
已加载轨迹1-林地的模型: speed = -0.0507 * slope + 4.68
已加载轨迹1-灌木地的模型: speed = -0.0317 * slope + 4.80
已加载轨迹1-水体的模型: speed = 0.0000 * slope + 1.45
轨迹1已加载3个地类模型
已加载轨迹2-林地的模型: speed = -0.0015 * slope + 4.07
已加载轨迹2-灌木地的模型: speed = 0.0131 * slope + 3.92
已加载轨迹2-水体的模型: speed = 0.0002 * slope + 1.35
轨迹2已加载3个地类模型
已加载轨迹3-林地的模型: speed = -0.0059 * slope + 4.51
已加载轨迹3-灌木地的模型: speed = -0.0160 * slope + 4.38
已加载轨迹3-水体的模型: speed = -0.0019 * slope + 1.49
轨迹3已加载3个地类模型
加载轨迹4-林地模型失败: 'slope_bin'
已加载轨迹4-灌木地的模型: speed = -0.0037 * slope + 4.37
已加载轨迹4-水体的模型: speed = -0.0001 * slope + 1.33
轨迹4已加载2个地类模型
轨迹1-林地的残差统计: 均值=0.000, 标准差=1.766
轨迹1-灌木地的残差统计: 均值=0.000, 标准差=1.255
轨迹1-水体的残差统计: 均值=0.000, 标准差=2.303
轨迹2-林地的残差统计: 均值=0.000, 标准差=2.588
轨迹2-灌木地的残差统计: 均值=0.000, 标准差=1.922
轨迹2-水体的残差统计: 均值=0.000, 标准差=2.207
轨迹3-林地的残差统计: 均值=0.000, 标准差=2.882
轨迹3-灌木地的残差统计: 均值=0.000, 标准差=1.850
轨迹3-水体的残差统计: 均值=0.000, 标准差=2.426
轨迹4-灌木地的残差统计: 均值=0.000, 标准差=1.831
轨迹4-水体的残差统计: 均值=0.000, 标准差=2.181
加载并处理轨迹...
处理轨迹 1: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_1.csv
处理轨迹 2: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_2.csv
处理轨迹 3: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_3.csv
处理轨迹 4: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_4.csv

开始验证...

处理轨迹 1

开始轨迹验证...
坐标类型检查:
x范围: -982005.4 ~ -975744.4
y范围: 8846741.0 ~ 8852438.0
判断为UTM坐标

使用原始轨迹的所有点: 7078个点
提取的原始速度: 均值=5.08m/s, 标准差=1.56m/s

开始生成轨迹...
输入航点数量: 7078
初始状态: {'x0': -981867.09145351, 'y0': 8846764.180494713, 'vx0': -0.089999996125698, 'vy0': -0.4399999976158142, 'heading0': 258.4398696290711}
目标轨迹点数量: 7078
使用原始时间戳: 7078个点
将匹配原始轨迹点数: 7078点
使用原始速度作为参考: 7078个点
轨迹点过多，跳过预计算曲率

当前位置: (-981775.71, 8847223.30)
目标航点索引: 476/7078
目标航点: (-981742.03, 8847250.37)
轨迹点进度: 1000/7078 (14.1%)

当前位置: (-981005.50, 8847620.47)
目标航点索引: 658/7078
目标航点: (-980963.25, 8847636.51)
轨迹点进度: 2000/7078 (28.3%)

当前位置: (-980568.80, 8848199.02)
目标航点索引: 924/7078
目标航点: (-980553.41, 8848209.99)
轨迹点进度: 3000/7078 (42.4%)

当前位置: (-979885.39, 8848230.37)
目标航点索引: 1134/7078
目标航点: (-979878.56, 8848265.48)
轨迹点进度: 4000/7078 (56.5%)

当前位置: (-979551.07, 8848905.53)
目标航点索引: 1372/7078
目标航点: (-979524.49, 8848925.36)
轨迹点进度: 5000/7078 (70.6%)

当前位置: (-979022.31, 8849463.24)
目标航点索引: 1596/7078
目标航点: (-979001.38, 8849496.92)
轨迹点进度: 6000/7078 (84.8%)

当前位置: (-978584.75, 8850070.28)
目标航点索引: 1834/7078
目标航点: (-978565.88, 8850091.29)
轨迹点进度: 7000/7078 (98.9%)
已生成足够的轨迹点 (7078/7078)，提前结束

轨迹生成完成，总步数: 7078
应用原始时间戳...

统计指标:
original_mean_speed: 5.078
generated_mean_speed: 3.200
original_std_speed: 1.562
generated_std_speed: 0.827
speed_correlation: 0.048
hausdorff_distance: 2819.161
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 2

开始轨迹验证...
坐标类型检查:
x范围: -982035.2 ~ -975741.6
y范围: 8846750.4 ~ 8852437.7
判断为UTM坐标

使用原始轨迹的所有点: 8627个点
提取的原始速度: 均值=4.24m/s, 标准差=2.28m/s

开始生成轨迹...
输入航点数量: 8627
初始状态: {'x0': -981865.3072710792, 'y0': 8846759.273459952, 'vx0': 0.3100000023841858, 'vy0': 0.2399999946355819, 'heading0': 37.74680455405379}
目标轨迹点数量: 8627
使用原始时间戳: 8627个点
将匹配原始轨迹点数: 8627点
使用原始速度作为参考: 8627个点
轨迹点过多，跳过预计算曲率

当前位置: (-981970.87, 8846988.51)
目标航点索引: 1258/8627
目标航点: (-981954.11, 8847007.53)
轨迹点进度: 1000/8627 (11.6%)

当前位置: (-981335.53, 8847513.15)
目标航点索引: 1462/8627
目标航点: (-981296.30, 8847534.07)
轨迹点进度: 2000/8627 (23.2%)

当前位置: (-980682.77, 8847939.77)
目标航点索引: 1666/8627
目标航点: (-980673.56, 8847975.50)
轨迹点进度: 3000/8627 (34.8%)

当前位置: (-980157.60, 8848189.37)
目标航点索引: 1887/8627
目标航点: (-980105.80, 8848177.39)
轨迹点进度: 4000/8627 (46.4%)

当前位置: (-979772.81, 8848654.81)
目标航点索引: 2142/8627
目标航点: (-979746.73, 8848704.02)
轨迹点进度: 5000/8627 (58.0%)

当前位置: (-979205.51, 8849184.16)
目标航点索引: 2329/8627
目标航点: (-979185.88, 8849199.09)
轨迹点进度: 6000/8627 (69.5%)

当前位置: (-978747.75, 8849765.51)
目标航点索引: 2550/8627
目标航点: (-978693.28, 8849797.46)
轨迹点进度: 7000/8627 (81.1%)

当前位置: (-978249.38, 8850476.29)
目标航点索引: 2839/8627
目标航点: (-978213.42, 8850523.42)
轨迹点进度: 8000/8627 (92.7%)
已生成足够的轨迹点 (8627/8627)，提前结束

轨迹生成完成，总步数: 8627
应用原始时间戳...

统计指标:
original_mean_speed: 4.244
generated_mean_speed: 3.200
original_std_speed: 2.280
generated_std_speed: 0.913
speed_correlation: 0.215
hausdorff_distance: 2437.778
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 3

开始轨迹验证...
坐标类型检查:
x范围: -982006.4 ~ -975744.9
y范围: 8846750.4 ~ 8852440.7
判断为UTM坐标

使用原始轨迹的所有点: 7091个点
提取的原始速度: 均值=5.05m/s, 标准差=1.88m/s

开始生成轨迹...
输入航点数量: 7091
初始状态: {'x0': -981862.8558158012, 'y0': 8846758.80141532, 'vx0': 0.0099999997764825, 'vy0': 0.0999999940395355, 'heading0': 84.28940665117028}
目标轨迹点数量: 7091
使用原始时间戳: 7091个点
将匹配原始轨迹点数: 7091点
使用原始速度作为参考: 7091个点
轨迹点过多，跳过预计算曲率

当前位置: (-981796.25, 8847200.91)
目标航点索引: 588/7091
目标航点: (-981759.49, 8847233.91)
轨迹点进度: 1000/7091 (14.1%)

当前位置: (-981039.91, 8847606.96)
目标航点索引: 770/7091
目标航点: (-981032.04, 8847609.43)
轨迹点进度: 2000/7091 (28.2%)

当前位置: (-980593.32, 8848182.14)
目标航点索引: 1064/7091
目标航点: (-980574.47, 8848197.38)
轨迹点进度: 3000/7091 (42.3%)

当前位置: (-979884.50, 8848175.50)
目标航点索引: 1232/7091
目标航点: (-979881.00, 8848199.88)
轨迹点进度: 4000/7091 (56.4%)

当前位置: (-979588.92, 8848874.11)
目标航点索引: 1456/7091
目标航点: (-979583.76, 8848878.46)
轨迹点进度: 5000/7091 (70.5%)

当前位置: (-979045.86, 8849405.66)
目标航点索引: 1638/7091
目标航点: (-979042.39, 8849416.71)
轨迹点进度: 6000/7091 (84.6%)

当前位置: (-978610.37, 8850016.86)
目标航点索引: 1848/7091
目标航点: (-978595.04, 8850056.28)
轨迹点进度: 7000/7091 (98.7%)
已生成足够的轨迹点 (7091/7091)，提前结束

轨迹生成完成，总步数: 7091
应用原始时间戳...

统计指标:
original_mean_speed: 5.048
generated_mean_speed: 3.188
original_std_speed: 1.884
generated_std_speed: 0.818
speed_correlation: 0.122
hausdorff_distance: 2846.595
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 4

开始轨迹验证...
坐标类型检查:
x范围: -982008.3 ~ -975750.5
y范围: 8846745.1 ~ 8852442.7
判断为UTM坐标

使用原始轨迹的所有点: 8073个点
提取的原始速度: 均值=4.47m/s, 标准差=2.26m/s

开始生成轨迹...
输入航点数量: 8073
初始状态: {'x0': -981859.934680198, 'y0': 8846758.975567885, 'vx0': 0.0599999986588954, 'vy0': -0.0599999986588954, 'heading0': 315.0}
目标轨迹点数量: 8073
使用原始时间戳: 8073个点
将匹配原始轨迹点数: 8073点
使用原始速度作为参考: 8073个点
轨迹点过多，跳过预计算曲率

当前位置: (-981886.68, 8847077.87)
目标航点索引: 1360/8073
目标航点: (-981859.43, 8847113.18)
轨迹点进度: 1000/8073 (12.4%)

当前位置: (-981194.83, 8847554.46)
目标航点索引: 1504/8073
目标航点: (-981154.24, 8847571.76)
轨迹点进度: 2000/8073 (24.8%)

当前位置: (-980689.68, 8848092.94)
目标航点索引: 1744/8073
目标航点: (-980677.47, 8848105.92)
轨迹点进度: 3000/8073 (37.2%)

当前位置: (-979994.36, 8848127.15)
目标航点索引: 1936/8073
目标航点: (-979949.47, 8848121.03)
轨迹点进度: 4000/8073 (49.5%)

当前位置: (-979670.44, 8848799.46)
目标航点索引: 2176/8073
目标航点: (-979657.86, 8848812.59)
轨迹点进度: 5000/8073 (61.9%)

当前位置: (-979077.32, 8849301.73)
目标航点索引: 2384/8073
目标航点: (-979070.97, 8849314.88)
轨迹点进度: 6000/8073 (74.3%)

当前位置: (-978611.21, 8849893.95)
目标航点索引: 2592/8073
目标航点: (-978608.32, 8849904.56)
轨迹点进度: 7000/8073 (86.7%)

当前位置: (-978103.04, 8850641.40)
目标航点索引: 2848/8073
目标航点: (-978061.01, 8850679.67)
轨迹点进度: 8000/8073 (99.1%)
已生成足够的轨迹点 (8073/8073)，提前结束

轨迹生成完成，总步数: 8073
应用原始时间戳...

统计指标:
original_mean_speed: 4.470
generated_mean_speed: 3.227
original_std_speed: 2.260
generated_std_speed: 0.882
speed_correlation: 0.207
hausdorff_distance: 2524.542
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results
