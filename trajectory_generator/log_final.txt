加载环境数据...

加载环境地图...
环境地图加载完成
地图范围: BoundingBox(left=-1028768.5496971096, bottom=8799609.271101762, right=-929008.261578123, top=8899574.370665655)
创建验证器...
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_林地.csv
已加载林地的速度-坡度模型: speed = -0.0145 * slope + 3.32
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_灌木地.csv
已加载灌木地的速度-坡度模型: speed = -0.0096 * slope + 4.37
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_水体.csv
已加载水体的速度-坡度模型: speed = -0.0004 * slope + 1.40
已加载轨迹1-林地的模型: speed = -0.0507 * slope + 4.68
已加载轨迹1-灌木地的模型: speed = -0.0317 * slope + 4.80
已加载轨迹1-水体的模型: speed = 0.0000 * slope + 1.45
轨迹1已加载3个地类模型
已加载轨迹2-林地的模型: speed = -0.0015 * slope + 4.07
已加载轨迹2-灌木地的模型: speed = 0.0131 * slope + 3.92
已加载轨迹2-水体的模型: speed = 0.0002 * slope + 1.35
轨迹2已加载3个地类模型
已加载轨迹3-林地的模型: speed = -0.0059 * slope + 4.51
已加载轨迹3-灌木地的模型: speed = -0.0160 * slope + 4.38
已加载轨迹3-水体的模型: speed = -0.0019 * slope + 1.49
轨迹3已加载3个地类模型
加载轨迹4-林地模型失败: 'slope_bin'
已加载轨迹4-灌木地的模型: speed = -0.0037 * slope + 4.37
已加载轨迹4-水体的模型: speed = -0.0001 * slope + 1.33
轨迹4已加载2个地类模型
轨迹1-林地的残差统计: 均值=0.000, 标准差=1.766
轨迹1-灌木地的残差统计: 均值=0.000, 标准差=1.255
轨迹1-水体的残差统计: 均值=0.000, 标准差=2.303
轨迹2-林地的残差统计: 均值=0.000, 标准差=2.588
轨迹2-灌木地的残差统计: 均值=0.000, 标准差=1.922
轨迹2-水体的残差统计: 均值=0.000, 标准差=2.207
轨迹3-林地的残差统计: 均值=0.000, 标准差=2.882
轨迹3-灌木地的残差统计: 均值=0.000, 标准差=1.850
轨迹3-水体的残差统计: 均值=0.000, 标准差=2.426
轨迹4-灌木地的残差统计: 均值=0.000, 标准差=1.831
轨迹4-水体的残差统计: 均值=0.000, 标准差=2.181
加载并处理轨迹...
处理轨迹 1: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_1.csv
处理轨迹 2: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_2.csv
处理轨迹 3: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_3.csv
处理轨迹 4: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_4.csv

开始验证...

处理轨迹 1

开始轨迹验证...
坐标类型检查:
x范围: -982005.4 ~ -975744.4
y范围: 8846741.0 ~ 8852438.0
判断为UTM坐标

使用原始轨迹的所有点: 7078个点
提取的原始速度: 均值=5.08m/s, 标准差=1.56m/s

开始生成轨迹...
输入航点数量: 7078
初始状态: {'x0': -981867.09145351, 'y0': 8846764.180494713, 'vx0': -0.089999996125698, 'vy0': -0.4399999976158142, 'heading0': 258.4398696290711}
目标轨迹点数量: 7078
使用原始时间戳: 7078个点
将匹配原始轨迹点数: 7078点
使用原始速度作为参考: 7078个点
轨迹点过多，跳过预计算曲率

当前位置: (-981191.36, 8847554.66)
目标航点索引: 616/7078
目标航点: (-981161.92, 8847567.56)
轨迹点进度: 1000/7078 (14.1%)

当前位置: (-979884.33, 8848236.49)
目标航点索引: 1134/7078
目标航点: (-979878.56, 8848265.48)
轨迹点进度: 2000/7078 (28.3%)

当前位置: (-978886.32, 8849662.78)
目标航点索引: 1666/7078
目标航点: (-978851.63, 8849698.26)
轨迹点进度: 3000/7078 (42.4%)

当前位置: (-977794.85, 8851203.59)
目标航点索引: 2282/7078
目标航点: (-977826.57, 8851236.72)
轨迹点进度: 4000/7078 (56.5%)

当前位置: (-977415.45, 8852431.72)
目标航点索引: 2744/7078
目标航点: (-977405.46, 8852437.71)
轨迹点进度: 5000/7078 (70.6%)

当前位置: (-977385.07, 8852428.85)
目标航点索引: 2800/7078
目标航点: (-977392.31, 8852436.60)
轨迹点进度: 6000/7078 (84.8%)

当前位置: (-977829.46, 8850770.13)
目标航点索引: 3458/7078
目标航点: (-977794.48, 8850764.29)
轨迹点进度: 7000/7078 (98.9%)
已生成足够的轨迹点 (7078/7078)，提前结束

轨迹生成完成，总步数: 7078
应用原始时间戳...

统计指标:
original_mean_speed: 5.078
generated_mean_speed: 7.752
original_std_speed: 1.562
generated_std_speed: 2.083
speed_correlation: 0.916
hausdorff_distance: 2322.177
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 2

开始轨迹验证...
坐标类型检查:
x范围: -982035.2 ~ -975741.6
y范围: 8846750.4 ~ 8852437.7
判断为UTM坐标

使用原始轨迹的所有点: 8627个点
提取的原始速度: 均值=4.24m/s, 标准差=2.28m/s

开始生成轨迹...
输入航点数量: 8627
初始状态: {'x0': -981865.3072710792, 'y0': 8846759.273459952, 'vx0': 0.3100000023841858, 'vy0': 0.2399999946355819, 'heading0': 37.74680455405379}
目标轨迹点数量: 8627
使用原始时间戳: 8627个点
将匹配原始轨迹点数: 8627点
使用原始速度作为参考: 8627个点
轨迹点过多，跳过预计算曲率

当前位置: (-982016.63, 8846919.53)
目标航点索引: 1207/8627
目标航点: (-982007.49, 8846941.63)
轨迹点进度: 1000/8627 (11.6%)

当前位置: (-980858.60, 8847812.65)
目标航点索引: 1598/8627
目标航点: (-980852.91, 8847817.52)
轨迹点进度: 2000/8627 (23.2%)

当前位置: (-979789.81, 8848619.27)
目标航点索引: 2125/8627
目标航点: (-979776.74, 8848647.71)
轨迹点进度: 3000/8627 (34.8%)

当前位置: (-978602.20, 8849900.65)
目标航点索引: 2601/8627
目标航点: (-978598.62, 8849924.41)
轨迹点进度: 4000/8627 (46.4%)

当前位置: (-977925.74, 8851369.77)
目标航点索引: 3179/8627
目标航点: (-977927.93, 8851394.40)
轨迹点进度: 5000/8627 (58.0%)

当前位置: (-977527.07, 8852269.13)
目标航点索引: 3468/8627
目标航点: (-977520.94, 8852280.03)
轨迹点进度: 6000/8627 (69.5%)

当前位置: (-977755.49, 8850929.14)
目标航点索引: 4165/8627
目标航点: (-977769.95, 8850914.29)
轨迹点进度: 7000/8627 (81.1%)

当前位置: (-976451.23, 8850388.29)
目标航点索引: 4777/8627
目标航点: (-976419.72, 8850336.15)
轨迹点进度: 8000/8627 (92.7%)
已生成足够的轨迹点 (8627/8627)，提前结束

轨迹生成完成，总步数: 8627
应用原始时间戳...

统计指标:
original_mean_speed: 4.244
generated_mean_speed: 6.561
original_std_speed: 2.280
generated_std_speed: 2.588
speed_correlation: 0.938
hausdorff_distance: 46.255
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 3

开始轨迹验证...
坐标类型检查:
x范围: -982006.4 ~ -975744.9
y范围: 8846750.4 ~ 8852440.7
判断为UTM坐标

使用原始轨迹的所有点: 7091个点
提取的原始速度: 均值=5.05m/s, 标准差=1.88m/s

开始生成轨迹...
输入航点数量: 7091
初始状态: {'x0': -981862.8558158012, 'y0': 8846758.80141532, 'vx0': 0.0099999997764825, 'vy0': 0.0999999940395355, 'heading0': 84.28940665117028}
目标轨迹点数量: 7091
使用原始时间戳: 7091个点
将匹配原始轨迹点数: 7091点
使用原始速度作为参考: 7091个点
轨迹点过多，跳过预计算曲率

当前位置: (-981339.22, 8847488.55)
目标航点索引: 714/7091
目标航点: (-981287.71, 8847510.79)
轨迹点进度: 1000/7091 (14.1%)

当前位置: (-979878.02, 8848217.63)
目标航点索引: 1246/7091
目标航点: (-979875.08, 8848234.93)
轨迹点进度: 2000/7091 (28.2%)

当前位置: (-978812.79, 8849721.85)
目标航点索引: 1736/7091
目标航点: (-978791.05, 8849736.17)
轨迹点进度: 3000/7091 (42.3%)

当前位置: (-977900.14, 8851286.45)
目标航点索引: 2282/7091
目标航点: (-977921.47, 8851321.03)
轨迹点进度: 4000/7091 (56.4%)

当前位置: (-977515.80, 8852296.04)
目标航点索引: 2814/7091
目标航点: (-977518.65, 8852292.47)
轨迹点进度: 5000/7091 (70.5%)
到达航点，前往下一个索引: 2828

当前位置: (-977515.80, 8852296.04)
目标航点索引: 2828/7091
目标航点: (-977540.67, 8852254.16)
轨迹点进度: 5000/7091 (70.5%)

当前位置: (-977438.28, 8850785.86)
目标航点索引: 3458/7091
目标航点: (-977366.96, 8850798.22)
轨迹点进度: 6000/7091 (84.6%)

当前位置: (-975877.95, 8849872.05)
目标航点索引: 4032/7091
目标航点: (-975849.93, 8849845.96)
轨迹点进度: 7000/7091 (98.7%)
已生成足够的轨迹点 (7091/7091)，提前结束

轨迹生成完成，总步数: 7091
应用原始时间戳...

统计指标:
original_mean_speed: 5.048
generated_mean_speed: 7.692
original_std_speed: 1.884
generated_std_speed: 2.300
speed_correlation: 0.929
hausdorff_distance: 195.341
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 4

开始轨迹验证...
坐标类型检查:
x范围: -982008.3 ~ -975750.5
y范围: 8846745.1 ~ 8852442.7
判断为UTM坐标

使用原始轨迹的所有点: 8073个点
提取的原始速度: 均值=4.47m/s, 标准差=2.26m/s

开始生成轨迹...
输入航点数量: 8073
初始状态: {'x0': -981859.934680198, 'y0': 8846758.975567885, 'vx0': 0.0599999986588954, 'vy0': -0.0599999986588954, 'heading0': 315.0}
目标轨迹点数量: 8073
使用原始时间戳: 8073个点
将匹配原始轨迹点数: 8073点
使用原始速度作为参考: 8073个点
轨迹点过多，跳过预计算曲率

当前位置: (-981984.24, 8846964.02)
目标航点索引: 1248/8073
目标航点: (-981981.10, 8846970.33)
轨迹点进度: 1000/8073 (12.4%)

当前位置: (-980744.40, 8847872.51)
目标航点索引: 1648/8073
目标航点: (-980728.16, 8847881.09)
轨迹点进度: 2000/8073 (24.8%)

当前位置: (-979611.56, 8848851.75)
目标航点索引: 2192/8073
目标航点: (-979593.99, 8848866.54)
轨迹点进度: 3000/8073 (37.2%)

当前位置: (-978474.60, 8850198.35)
目标航点索引: 2704/8073
目标航点: (-978451.31, 8850232.90)
轨迹点进度: 4000/8073 (49.5%)

当前位置: (-977739.45, 8851768.90)
目标航点索引: 3232/8073
目标航点: (-977722.13, 8851819.46)
轨迹点进度: 5000/8073 (61.9%)

当前位置: (-977565.87, 8852192.24)
目标航点索引: 3680/8073
目标航点: (-977569.65, 8852185.09)
轨迹点进度: 6000/8073 (74.3%)

当前位置: (-977323.91, 8850808.36)
目标航点索引: 4304/8073
目标航点: (-977286.48, 8850816.24)
轨迹点进度: 7000/8073 (86.7%)

当前位置: (-975876.14, 8849866.55)
目标航点索引: 4864/8073
目标航点: (-975828.37, 8849823.19)
轨迹点进度: 8000/8073 (99.1%)
已生成足够的轨迹点 (8073/8073)，提前结束

轨迹生成完成，总步数: 8073
应用原始时间戳...

统计指标:
original_mean_speed: 4.470
generated_mean_speed: 6.947
original_std_speed: 2.260
generated_std_speed: 2.608
speed_correlation: 0.946
hausdorff_distance: 224.643
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results
