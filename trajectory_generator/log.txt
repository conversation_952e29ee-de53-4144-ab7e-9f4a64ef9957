加载环境数据...

加载环境地图...
环境地图加载完成
地图范围: BoundingBox(left=-1028768.5496971096, bottom=8799609.271101762, right=-929008.261578123, top=8899574.370665655)
创建验证器...
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_林地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_林地.csv
已加载林地的速度-坡度模型: speed = -0.0145 * slope + 3.32
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_灌木地.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_灌木地.csv
已加载灌木地的速度-坡度模型: speed = -0.0096 * slope + 4.37
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹2_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹3_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹4_坡度分箱统计_1秒_水体.csv
正在处理: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result/轨迹1_坡度分箱统计_1秒_水体.csv
已加载水体的速度-坡度模型: speed = -0.0004 * slope + 1.40
已加载轨迹1-林地的模型: speed = -0.0507 * slope + 4.68
已加载轨迹1-灌木地的模型: speed = -0.0317 * slope + 4.80
已加载轨迹1-水体的模型: speed = 0.0000 * slope + 1.45
轨迹1已加载3个地类模型
已加载轨迹2-林地的模型: speed = -0.0015 * slope + 4.07
已加载轨迹2-灌木地的模型: speed = 0.0131 * slope + 3.92
已加载轨迹2-水体的模型: speed = 0.0002 * slope + 1.35
轨迹2已加载3个地类模型
已加载轨迹3-林地的模型: speed = -0.0059 * slope + 4.51
已加载轨迹3-灌木地的模型: speed = -0.0160 * slope + 4.38
已加载轨迹3-水体的模型: speed = -0.0019 * slope + 1.49
轨迹3已加载3个地类模型
加载轨迹4-林地模型失败: 'slope_bin'
已加载轨迹4-灌木地的模型: speed = -0.0037 * slope + 4.37
已加载轨迹4-水体的模型: speed = -0.0001 * slope + 1.33
轨迹4已加载2个地类模型
轨迹1-林地的残差统计: 均值=0.000, 标准差=1.766
轨迹1-灌木地的残差统计: 均值=0.000, 标准差=1.255
轨迹1-水体的残差统计: 均值=0.000, 标准差=2.303
轨迹2-林地的残差统计: 均值=0.000, 标准差=2.588
轨迹2-灌木地的残差统计: 均值=0.000, 标准差=1.922
轨迹2-水体的残差统计: 均值=0.000, 标准差=2.207
轨迹3-林地的残差统计: 均值=0.000, 标准差=2.882
轨迹3-灌木地的残差统计: 均值=0.000, 标准差=1.850
轨迹3-水体的残差统计: 均值=0.000, 标准差=2.426
轨迹4-灌木地的残差统计: 均值=0.000, 标准差=1.831
轨迹4-水体的残差统计: 均值=0.000, 标准差=2.181
加载并处理轨迹...
处理轨迹 1: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_1.csv
处理轨迹 2: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_2.csv
处理轨迹 3: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_3.csv
处理轨迹 4: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/trajectories/trajectory_4.csv

开始验证...

处理轨迹 1

开始轨迹验证...
坐标类型检查:
x范围: -982005.4 ~ -975744.4
y范围: 8846741.0 ~ 8852438.0
判断为UTM坐标

使用原始轨迹的所有点: 7078个点
提取的原始速度: 均值=5.08m/s, 标准差=1.56m/s

开始生成轨迹...
输入航点数量: 7078
初始状态: {'x0': -981867.09145351, 'y0': 8846764.180494713, 'vx0': -0.089999996125698, 'vy0': -0.4399999976158142, 'heading0': 258.4398696290711}
目标轨迹点数量: 7078
使用原始时间戳: 7078个点
将匹配原始轨迹点数: 7078点
使用原始速度作为参考: 7078个点
轨迹点过多，跳过预计算曲率

当前位置: (-981229.53, 8847537.54)
目标航点索引: 602/7078
目标航点: (-981223.05, 8847540.76)
轨迹点进度: 1000/7078 (14.1%)

当前位置: (-979890.34, 8848185.16)
目标航点索引: 1106/7078
目标航点: (-979890.49, 8848192.05)
轨迹点进度: 2000/7078 (28.3%)

当前位置: (-978932.93, 8849600.72)
目标航点索引: 1638/7078
目标航点: (-978920.53, 8849618.00)
轨迹点进度: 3000/7078 (42.4%)

当前位置: (-977761.81, 8851135.29)
目标航点索引: 2254/7078
目标航点: (-977782.16, 8851169.21)
轨迹点进度: 4000/7078 (56.5%)

当前位置: (-977411.38, 8852421.19)
目标航点索引: 2814/7078
目标航点: (-977408.52, 8852428.51)
轨迹点进度: 5000/7078 (70.6%)

当前位置: (-977715.64, 8850761.72)
目标航点索引: 3486/7078
目标航点: (-977655.79, 8850764.67)
轨迹点进度: 6000/7078 (84.8%)

当前位置: (-976094.33, 8850044.69)
目标航点索引: 4046/7078
目标航点: (-976086.55, 8850038.29)
轨迹点进度: 7000/7078 (98.9%)
已生成足够的轨迹点 (7078/7078)，提前结束

轨迹生成完成，总步数: 7078
应用原始时间戳...

统计指标:
original_mean_speed: 5.078
generated_mean_speed: 7.555
original_std_speed: 1.562
generated_std_speed: 2.181
speed_correlation: 0.948
hausdorff_distance: 514.349
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 2

开始轨迹验证...
坐标类型检查:
x范围: -982035.2 ~ -975741.6
y范围: 8846750.4 ~ 8852437.7
判断为UTM坐标

使用原始轨迹的所有点: 8627个点
提取的原始速度: 均值=4.24m/s, 标准差=2.28m/s

开始生成轨迹...
输入航点数量: 8627
初始状态: {'x0': -981865.3072710792, 'y0': 8846759.273459952, 'vx0': 0.3100000023841858, 'vy0': 0.2399999946355819, 'heading0': 37.74680455405379}
目标轨迹点数量: 8627
使用原始时间戳: 8627个点
将匹配原始轨迹点数: 8627点
使用原始速度作为参考: 8627个点
轨迹点过多，跳过预计算曲率

当前位置: (-982047.52, 8846886.09)
目标航点索引: 1156/8627
目标航点: (-982035.21, 8846889.03)
轨迹点进度: 1000/8627 (11.6%)

当前位置: (-980880.59, 8847685.29)
目标航点索引: 1564/8627
目标航点: (-980914.97, 8847714.34)
轨迹点进度: 2000/8627 (23.2%)

当前位置: (-979837.00, 8848444.99)
目标航点索引: 2074/8627
目标航点: (-979828.10, 8848493.10)
轨迹点进度: 3000/8627 (34.8%)

当前位置: (-978738.32, 8849771.00)
目标航点索引: 2550/8627
目标航点: (-978693.28, 8849797.46)
轨迹点进度: 4000/8627 (46.4%)

当前位置: (-977796.21, 8851221.42)
目标航点索引: 3111/8627
目标航点: (-977799.39, 8851228.26)
轨迹点进度: 5000/8627 (58.0%)

当前位置: (-977672.72, 8851943.53)
目标航点索引: 3366/8627
目标航点: (-977670.21, 8851951.45)
轨迹点进度: 6000/8627 (69.5%)

当前位置: (-977921.49, 8851328.44)
目标航点索引: 4012/8627
目标航点: (-977913.26, 8851311.81)
轨迹点进度: 7000/8627 (81.1%)

当前位置: (-976612.17, 8850824.88)
目标航点索引: 4556/8627
目标航点: (-976597.60, 8850826.96)
轨迹点进度: 8000/8627 (92.7%)
已生成足够的轨迹点 (8627/8627)，提前结束

轨迹生成完成，总步数: 8627
应用原始时间戳...

统计指标:
original_mean_speed: 4.244
generated_mean_speed: 6.316
original_std_speed: 2.280
generated_std_speed: 2.639
speed_correlation: 0.958
hausdorff_distance: 679.046
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 3

开始轨迹验证...
坐标类型检查:
x范围: -982006.4 ~ -975744.9
y范围: 8846750.4 ~ 8852440.7
判断为UTM坐标

使用原始轨迹的所有点: 7091个点
提取的原始速度: 均值=5.05m/s, 标准差=1.88m/s

开始生成轨迹...
输入航点数量: 7091
初始状态: {'x0': -981862.8558158012, 'y0': 8846758.80141532, 'vx0': 0.0099999997764825, 'vy0': 0.0999999940395355, 'heading0': 84.28940665117028}
目标轨迹点数量: 7091
使用原始时间戳: 7091个点
将匹配原始轨迹点数: 7091点
使用原始速度作为参考: 7091个点
轨迹点过多，跳过预计算曲率

当前位置: (-981420.88, 8847446.30)
目标航点索引: 686/7091
目标航点: (-981400.30, 8847457.16)
轨迹点进度: 1000/7091 (14.1%)

当前位置: (-979915.90, 8848135.75)
目标航点索引: 1218/7091
目标航点: (-979884.57, 8848164.80)
轨迹点进度: 2000/7091 (28.2%)

当前位置: (-978907.13, 8849634.65)
目标航点索引: 1708/7091
目标航点: (-978896.82, 8849648.96)
轨迹点进度: 3000/7091 (42.3%)

当前位置: (-977809.24, 8851208.98)
目标航点索引: 2240/7091
目标航点: (-977814.43, 8851214.75)
轨迹点进度: 4000/7091 (56.4%)

当前位置: (-977370.15, 8852444.43)
目标航点索引: 2702/7091
目标航点: (-977384.72, 8852436.91)
轨迹点进度: 5000/7091 (70.5%)

当前位置: (-977410.11, 8852417.11)
目标航点索引: 2744/7091
目标航点: (-977398.84, 8852425.65)
轨迹点进度: 6000/7091 (84.6%)

当前位置: (-977811.55, 8850876.51)
目标航点索引: 3290/7091
目标航点: (-977815.51, 8850873.18)
轨迹点进度: 7000/7091 (98.7%)
已生成足够的轨迹点 (7091/7091)，提前结束

轨迹生成完成，总步数: 7091
应用原始时间戳...

统计指标:
original_mean_speed: 5.048
generated_mean_speed: 7.443
original_std_speed: 1.884
generated_std_speed: 2.484
speed_correlation: 0.955
hausdorff_distance: 2357.057
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results

处理轨迹 4

开始轨迹验证...
坐标类型检查:
x范围: -982008.3 ~ -975750.5
y范围: 8846745.1 ~ 8852442.7
判断为UTM坐标

使用原始轨迹的所有点: 8073个点
提取的原始速度: 均值=4.47m/s, 标准差=2.26m/s

开始生成轨迹...
输入航点数量: 8073
初始状态: {'x0': -981859.934680198, 'y0': 8846758.975567885, 'vx0': 0.0599999986588954, 'vy0': -0.0599999986588954, 'heading0': 315.0}
目标轨迹点数量: 8073
使用原始时间戳: 8073个点
将匹配原始轨迹点数: 8073点
使用原始速度作为参考: 8073个点
轨迹点过多，跳过预计算曲率

当前位置: (-982000.50, 8846921.06)
目标航点索引: 1232/8073
目标航点: (-981994.07, 8846945.97)
轨迹点进度: 1000/8073 (12.4%)

当前位置: (-980833.54, 8847831.92)
目标航点索引: 1632/8073
目标航点: (-980777.15, 8847855.18)
轨迹点进度: 2000/8073 (24.8%)

当前位置: (-979693.82, 8848777.58)
目标航点索引: 2176/8073
目标航点: (-979657.86, 8848812.59)
轨迹点进度: 3000/8073 (37.2%)

当前位置: (-978561.37, 8850087.15)
目标航点索引: 2672/8073
目标航点: (-978520.85, 8850133.27)
轨迹点进度: 4000/8073 (49.5%)

当前位置: (-977823.94, 8851633.23)
目标航点索引: 3184/8073
目标航点: (-977796.52, 8851673.49)
轨迹点进度: 5000/8073 (61.9%)

当前位置: (-977407.78, 8852424.72)
目标航点索引: 3472/8073
目标航点: (-977401.89, 8852441.84)
轨迹点进度: 6000/8073 (74.3%)

当前位置: (-977437.02, 8852405.39)
目标航点索引: 3568/8073
目标航点: (-977419.33, 8852410.43)
轨迹点进度: 7000/8073 (86.7%)

当前位置: (-977765.13, 8850919.56)
目标航点索引: 4112/8073
目标航点: (-977784.86, 8850897.71)
轨迹点进度: 8000/8073 (99.1%)
已生成足够的轨迹点 (8073/8073)，提前结束

轨迹生成完成，总步数: 8073
应用原始时间戳...

统计指标:
original_mean_speed: 4.470
generated_mean_speed: 6.680
original_std_speed: 2.260
generated_std_speed: 2.706
speed_correlation: 0.963
hausdorff_distance: 2375.181
结果已保存到 /home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/results
