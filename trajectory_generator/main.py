"""
脚本名称: trajectory_generator/main.py
====================================

功能描述:
  作为轨迹生成器项目的主入口点，该脚本负责协调整个流程，包括加载原始轨迹数据、
  加载环境地图数据、初始化验证器、调用验证器对每个原始轨迹进行处理（提取航点、
  生成新轨迹、计算指标、生成对比图），并保存生成的结果。

主要功能:
  1. 设置项目相关的文件和目录路径 (data, environment, trajectories, results)。
  2. 加载环境地图数据 (DEM, 坡度等) 使用 `EnvironmentMaps` 类。
  3. 初始化轨迹验证器 (`TrajectoryValidator`)，并将环境地图对象传入。
  4. 循环加载指定的原始轨迹文件 (CSV格式，位于 data/trajectories)。
  5. 对每个加载的原始轨迹:
     a. 调用 `validator.validate` 方法，该方法内部会:
        - 提取关键航点 (`extract_waypoints`)。
        - 获取初始状态。
        - 调用轨迹生成器 (`TrajectoryGenerator`) 生成新的对比轨迹。
        - 计算原始轨迹与生成轨迹之间的统计指标 (`_calculate_metrics`)。
        - 可选地，生成并保存对比可视化图 (`plot_comparison`)。
     b. 打印计算出的统计指标。
     c. 将生成的对比轨迹保存为 CSV 文件。
  6. 处理过程中打印进度信息和错误信息。

输入:
  - 原始轨迹文件: 位于 `data/trajectories/` 目录下，命名格式为 `trajectory_{i}.csv`
    (脚本默认处理 i=1 到 4)。CSV 文件需要包含 UTM 坐标 (列名 'longitude' 代表 X，
    'latitude' 代表 Y)、速度分量 ('velocity_north_ms', 'velocity_east_ms')、
    时间戳 ('timestamp_ms') 和航向角 ('heading_deg')。
  - 环境数据栅格文件: 位于 `data/environment/` 目录下，由 `EnvironmentMaps` 类负责加载。
    具体需要哪些文件取决于 `EnvironmentMaps` 的实现。

输出:
  - 生成的轨迹文件: 保存到 `results/` 目录下，CSV 格式，文件名格式为
    `trajectory_{i}_generated.csv`。
  - 对比分析图: 保存到 `results/` 目录下，PNG 格式，文件名格式为
    `trajectory_{i}_comparison.png` (如果 `visualize=True` 被调用)。
  - 控制台输出: 显示加载信息、处理进度、每个轨迹的验证指标和可能的错误信息。

使用示例:
  直接运行脚本: python trajectory_generator/main.py
  脚本会按预设路径加载数据，处理轨迹 1 到 4，并将结果保存到 `results` 目录。

"""

import os
import pandas as pd
import numpy as np
from typing import Dict, Any
from src.validation import TrajectoryValidator
from src.environment import EnvironmentMaps

def load_trajectory(file_path: str) -> pd.DataFrame:
    """加载轨迹数据
    
    Args:
        file_path: 轨迹文件路径
        
    Returns:
        pd.DataFrame: 轨迹数据
    """
    df = pd.read_csv(file_path)
    
    # 确保必要的列存在
    required_cols = [
        'timestamp_ms',
        'latitude',  # UTM-Y坐标
        'longitude', # UTM-X坐标
        'velocity_north_ms',
        'velocity_east_ms',
        'heading_deg'
    ]
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"缺少必要的列: {col}")
    
    # 将latitude和longitude列重命名为x和y
    df = df.rename(columns={
        'longitude': 'x',  # UTM-X坐标
        'latitude': 'y'    # UTM-Y坐标
    })
            
    return df

def main():
    """主函数"""
    # 1. 设置路径
    base_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(base_dir, "data")
    env_dir = os.path.join(data_dir, "environment")
    traj_dir = os.path.join(data_dir, "trajectories")
    results_dir = os.path.join(base_dir, "results")
    
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    
    print("加载环境数据...")
    # 2. 加载环境地图
    env_maps = EnvironmentMaps(env_dir)
    
    print("创建验证器...")
    # 3. 创建验证器
    validator = TrajectoryValidator(env_maps)
    
    print("加载并处理轨迹...")
    # 4. 加载并处理原始轨迹
    trajectories = []
    for i in range(1, 5):  # 处理4条轨迹
        try:
            # 加载轨迹
            traj_path = os.path.join(traj_dir, f"trajectory_{i}.csv")
            print(f"处理轨迹 {i}: {traj_path}")
            
            df = load_trajectory(traj_path)
            trajectories.append((i, df))
            
        except Exception as e:
            print(f"处理轨迹{i}时出错: {e}")
            continue
    
    print("\n开始验证...")
    # 5. 验证每条轨迹
    for traj_id, original_df in trajectories:
        print(f"\n处理轨迹 {traj_id}")
        
        try:
            # 生成对比轨迹
            save_path = os.path.join(
                results_dir,
                f"trajectory_{traj_id}_comparison.png"
            )
            generated_df, metrics = validator.validate(
                original_df=original_df,
                goal_id=0,
                output_path=save_path,
                visualize=True
            )
            
            # 打印统计指标
            print("\n统计指标:")
            for key, value in metrics.items():
                print(f"{key}: {value:.3f}")
            
            # 保存生成的轨迹
            output_path = os.path.join(
                results_dir,
                f"trajectory_{traj_id}_generated.csv"
            )
            generated_df.to_csv(output_path, index=False)
            
            print(f"结果已保存到 {results_dir}")
            
        except Exception as e:
            print(f"验证轨迹{traj_id}时出错: {e}")
            continue

if __name__ == '__main__':
    main() 