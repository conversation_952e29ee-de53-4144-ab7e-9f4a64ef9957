import rasterio
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import ListedColormap, BoundaryNorm
import os

def visualize_dem(tif_path, output_path):
    """
    可视化DEM高程数据
    """
    with rasterio.open(tif_path) as src:
        dem_data = src.read(1)
        dem_data[dem_data < -1000] = np.nan  # 移除无效值

        plt.figure(figsize=(12, 10))
        plt.imshow(dem_data, cmap='terrain')
        plt.colorbar(label='高程 (米)')
        plt.title('数字高程模型 (DEM)', fontsize=22)
        plt.xlabel('像素坐标 X', fontsize=20)
        plt.ylabel('像素坐标 Y', fontsize=20)
        plt.xticks(fontsize=14)
        plt.yticks(fontsize=14)
        plt.savefig(os.path.join(output_path, 'dem_visualization.png'), dpi=300)
        plt.close()

def visualize_landcover(tif_path, output_path):
    """
    可视化地表覆盖类型数据
    """
    # 地表类型颜色规范
    cmap_dict = {
        10: '#0077BE',  # 水域
        20: '#80CCFF',  # 湿地
        30: '#90EE90',  # 草地
        40: '#228B22',  # 灌木地
        50: '#CD5C5C',  # 建筑用地
        60: '#FFD700',  # 农田
        80: '#006400',  # 森林
        90: '#DEB887',  # 荒地
        255: '#808080' # 未分类
    }
    
    with rasterio.open(tif_path) as src:
        landcover_data = src.read(1)
        
        # 获取所有唯一值
        unique_vals = np.unique(landcover_data)
        
        # 创建颜色列表和归一化边界
        colors = [cmap_dict.get(val, '#FFFFFF') for val in sorted(unique_vals) if val in cmap_dict]
        
        # 筛选出在 cmap_dict 中定义的类别
        valid_ticks = [val for val in sorted(unique_vals) if val in cmap_dict]
        
        if not valid_ticks:
            print("警告: 在landcover文件中没有找到任何预定义的类别。")
            return
            
        cmap = ListedColormap(colors)
        
        # 创建一个边界列表，确保每个颜色对应一个整数类别
        bounds = [t - 0.5 for t in valid_ticks]
        bounds.append(valid_ticks[-1] + 0.5)
        norm = BoundaryNorm(bounds, cmap.N)

        plt.figure(figsize=(12, 10))
        im = plt.imshow(landcover_data, cmap=cmap, norm=norm)
        
        # 设置颜色条
        cbar = plt.colorbar(im, ticks=valid_ticks, spacing='proportional')
        cbar.set_label('地表覆盖类型', fontsize=20)
        
        # 设置颜色条的标签
        tick_labels = {
            10: '水域', 20: '湿地', 30: '草地', 40: '灌木地', 50: '建筑用地',
            60: '农田', 80: '森林', 90: '荒地', 255: '未分类'
        }
        cbar.set_ticklabels([tick_labels.get(t, '未知') for t in valid_ticks])
        
        plt.title('地表覆盖类型', fontsize=22)
        plt.xlabel('像素坐标 X', fontsize=20)
        plt.ylabel('像素坐标 Y', fontsize=20)
        plt.xticks(fontsize=14)
        plt.yticks(fontsize=14)
        plt.grid(False)
        plt.savefig(os.path.join(output_path, 'landcover_visualization.png'), dpi=300)
        plt.close()

def visualize_generic_raster(tif_path, output_path, title, cmap='viridis', label='值'):
    """
    通用栅格数据可视化 (用于坡度和坡向)
    """
    with rasterio.open(tif_path) as src:
        data = src.read(1)
        data[data < -1000] = np.nan

        plt.figure(figsize=(12, 10))
        plt.imshow(data, cmap=cmap)
        plt.colorbar(label=label)
        plt.title(title, fontsize=22)
        plt.xlabel('像素坐标 X', fontsize=20)
        plt.ylabel('像素坐标 Y', fontsize=20)
        plt.xticks(fontsize=14)
        plt.yticks(fontsize=14)
        plt.savefig(os.path.join(output_path, f'{os.path.splitext(os.path.basename(tif_path))[0]}_visualization.png'), dpi=300)
        plt.close()

def main():
    # 设置matplotlib支持中文
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    base_path = os.path.dirname(__file__)
    
    files_to_visualize = {
        "dem_aligned.tif": lambda p, o: visualize_dem(p, o),
        "landcover_aligned.tif": lambda p, o: visualize_landcover(p, o),
        "slope_aligned.tif": lambda p, o: visualize_generic_raster(p, o, "坡度图", cmap='viridis', label='坡度 (度)'),
        "aspect_aligned.tif": lambda p, o: visualize_generic_raster(p, o, "坡向图", cmap='hsv', label='坡向 (度)')
    }

    for filename, func in files_to_visualize.items():
        file_path = os.path.join(base_path, filename)
        if os.path.exists(file_path):
            print(f"正在可视化: {filename}")
            func(file_path, base_path)
            print(f"完成: {filename}")
        else:
            print(f"文件未找到，跳过: {filename}")

if __name__ == '__main__':
    main() 
 
 
 
 
 
 
 
 
 