import pandas as pd
import numpy as np
import joblib
import os
import math
import argparse
import rasterio
from scipy.spatial import cKDTree
import logging

import config
from controllers import PIDController, PurePursuitController
from vehicle_model import VehicleState, update_state

# --- 日志设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_environment_features(x, y, yaw, path_df):
    """
    这个函数应该根据车辆的x,y,yaw，从预处理好的路径DataFrame中提取所有需要的环境特征。
    为简化仿真，我们直接使用路径上最近点的特征。
    """
    # 这是一个简化的实现，实际应用中可能需要更复杂的插值
    kdtree = cKDTree(path_df[['x', 'y']].values)
    _, nearest_idx = kdtree.query([x, y])
    
    # 返回一个包含所有预计算特征的字典
    return path_df.iloc[nearest_idx].to_dict()

def run_simulation(trajectory_id, pp_params, speed_model, show_debug_logs=False):
    """
    根据给定的参数运行单个仿真。
    
    :param trajectory_id: 要仿真的轨迹ID
    :param pp_params: Pure Pursuit控制器的参数字典
    :param speed_model: 预加载的速度预测模型
    :param show_debug_logs: 是否显示详细的每步日志
    :return: 包含仿真结果的DataFrame
    """
    # --- 数据加载 ---
    ref_path_file = os.path.join(config.PROCESSED_DATA_DIR, f'trajectory_{trajectory_id}_processed.csv')
    try:
        ref_path_df = pd.read_csv(ref_path_file)
    except FileNotFoundError:
        logging.error(f"参考路径文件未找到: {ref_path_file}")
        return None

    ref_path_kdtree = cKDTree(ref_path_df[['x', 'y']].values)

    # --- 初始化 ---
    # 修正：从路径的头两个点计算初始偏航角
    initial_yaw = np.arctan2(ref_path_df['y'].iloc[1] - ref_path_df['y'].iloc[0], 
                             ref_path_df['x'].iloc[1] - ref_path_df['x'].iloc[0])
    start_state = VehicleState(x=ref_path_df['x'].iloc[0], y=ref_path_df['y'].iloc[0], yaw=initial_yaw, v=ref_path_df['speed'].iloc[0])
    state = start_state
    
    pid_params = {'Kp': 1.8, 'Ki': 0.05, 'Kd': 0.1, 'min_output': -config.MAX_ACCEL, 'max_output': config.MAX_ACCEL}
    
    speed_controller = PIDController(Kp=pid_params['Kp'], Ki=pid_params['Ki'], Kd=pid_params['Kd'], min_output=pid_params['min_output'], max_output=pid_params['max_output'])
    path_controller = PurePursuitController(k=pp_params['k'], lookahead_min=pp_params['lookahead_min'], lookahead_max=pp_params['lookahead_max'])

    # --- 仿真循环 ---
    time = 0.0
    dt = config.DT
    trajectory_log = []

    while time < config.SIM_MAX_TIME:
        # 1. 获取环境特征
        env_features = get_environment_features(state.x, state.y, state.yaw, ref_path_df)

        # 2. 构建特征向量 (确保顺序和数量与训练时完全一致)
        # 关键修正：移除 'current_speed'，使用16个环境特征
        try:
            features = np.array([
                env_features['effective_slope'],
                env_features['curvature'],
                env_features['landcover'],
                env_features['dem'],
                env_features['curvature_max_next_10m'],
                env_features['effective_slope_mean_next_10m'],
                env_features['elevation_change_next_10m'],
                env_features['curvature_max_next_30m'],
                env_features['effective_slope_mean_next_30m'],
                env_features['elevation_change_next_30m'],
                env_features['curvature_max_next_50m'],
                env_features['effective_slope_mean_next_50m'],
                env_features['elevation_change_next_50m'],
                env_features['curvature_max_next_150m'],
                env_features['effective_slope_mean_next_150m'],
                env_features['elevation_change_next_150m']
            ])
        except KeyError as e:
            logging.error(f"从路径文件中提取特征时出错，缺少键: {e}")
            break

        # 3. 使用AI模型预测目标速度
        target_speed = speed_model.predict([features])[0]
        
        # 4. 计算控制指令
        accel = speed_controller.update(target_speed, state.v, dt)
        steer, target_point = path_controller.update(state, ref_path_df['x'].values, ref_path_df['y'].values, ref_path_kdtree)
        
        # 5. 更新车辆状态
        state = update_state(state, accel, steer, dt)
        
        # 6. 记录日志
        log_entry = {
            'time_s': time, 'x': state.x, 'y': state.y, 'yaw': state.yaw, 
            'speed': state.v, 'accel': accel, 'steer': steer, 'target_speed': target_speed
        }
        trajectory_log.append(log_entry)

        if show_debug_logs and (int(time*100) % 1000 == 0): # 每10秒打印一次
            logging.info(f"Sim Time: {time:.2f}s, Speed: {state.v:.2f} m/s, Target: {target_speed:.2f} m/s")

        # 7. 检查终止条件
        _, nearest_idx = ref_path_kdtree.query([state.x, state.y])
        if nearest_idx >= len(ref_path_df) - 2:
            logging.info("已到达参考路径终点，仿真结束。")
            break
            
        time += dt
    else:
        logging.info(f"仿真达到最大时长 {config.SIM_MAX_TIME} 秒，结束。")

    return pd.DataFrame(trajectory_log)

def main():
    """主函数，用于直接从命令行运行单个仿真并保存结果。"""
    parser = argparse.ArgumentParser(description="运行车辆仿真")
    parser.add_argument('--id', type=int, required=True, help='要仿真的轨迹ID')
    args = parser.parse_args()

    # --- 模型加载 ---
    model_path = os.path.join(config.MODELS_DIR, 'random_forest_speed_model.joblib')
    try:
        speed_model = joblib.load(model_path)
    except FileNotFoundError:
        logging.error(f"速度模型未找到: {model_path}，请先运行训练脚本。")
        return

    logging.info(f"--- 开始对轨迹 {args.id} 进行仿真 ---")
    
    # 默认参数用于命令行调用
    pp_params = {'k': 0.5, 'lookahead_min': 5.0, 'lookahead_max': 20.0}

    sim_df = run_simulation(args.id, pp_params, speed_model, show_debug_logs=True)

    if sim_df is not None:
        # --- 保存结果 ---
        # 关键修正：保存到根结果目录，并使用标准文件名
        output_path = os.path.join(config.RESULTS_DIR, 'simulation_results.csv')
        sim_df.to_csv(output_path, index=False)
        logging.info(f"仿真轨迹已保存至: {output_path}")

if __name__ == '__main__':
    main()

