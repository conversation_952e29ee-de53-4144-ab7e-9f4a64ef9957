import matplotlib.pyplot as plt
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def apply_plot_rules():
    """
    应用项目通用的matplotlib绘图规则。
    - 尝试设置中文字体 (SimHei) 以支持中文显示。
    - 解决Matplotlib中负号显示为方块的问题。
    - 如果字体设置失败，则记录警告信息。
    """
    try:
        # 步骤1：指定一个包含中文字符的字体
        plt.rcParams['font.sans-serif'] = ['SimHei'] 
        
        # 步骤2：解决负号显示问题
        plt.rcParams['axes.unicode_minus'] = False
        logger.info("成功应用绘图样式：使用SimHei字体。")
    except Exception as e:
        logger.warning(f"无法设置中文字体，图表中文可能无法正确显示: {e}") 