import os
import numpy as np

# 项目根目录
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 数据目录
DATA_DIR = os.path.join(BASE_DIR, 'data')
TRAJECTORY_DIR = os.path.join(DATA_DIR, 'trajectories')
ENVIRONMENT_DIR = os.path.join(DATA_DIR, 'environment')

# 输出目录
RESULTS_DIR = os.path.join(BASE_DIR, 'results')
MODELS_DIR = os.path.join(BASE_DIR, 'models')
# 处理后的数据现在也放入主结果目录中，以保持整洁
PROCESSED_DATA_DIR = os.path.join(DATA_DIR, 'processed')

# 创建基础输出目录
os.makedirs(PROCESSED_DATA_DIR, exist_ok=True)
os.makedirs(MODELS_DIR, exist_ok=True)

# --- V2 版本配置 ---
# 为V2版本（动态加速度模型）创建独立的目录
BASE_RESULTS_DIR_V2 = os.path.join(BASE_DIR, 'results_v2')
MODELS_DIR_V2 = os.path.join(BASE_DIR, 'models_v2')
PROCESSED_DATA_DIR_V2 = os.path.join(DATA_DIR, 'processed_v2')

# 创建V2版本目录
os.makedirs(BASE_RESULTS_DIR_V2, exist_ok=True)
os.makedirs(MODELS_DIR_V2, exist_ok=True)
os.makedirs(PROCESSED_DATA_DIR_V2, exist_ok=True)
# --- 结束 V2 配置 ---

# 定义输入文件
TRAJECTORY_FILES = [os.path.join(TRAJECTORY_DIR, f'trajectory_{i}.csv') for i in range(1, 5)]

# 环境数据层
# 注意：这里假设每个环境TIF文件都覆盖所有轨迹的区域，
# 如果每个轨迹对应一个特定的TIF，则需要修改逻辑。
ENV_FILES = {
    'dem': os.path.join(ENVIRONMENT_DIR, 'dem_aligned.tif'),
    'slope': os.path.join(ENVIRONMENT_DIR, 'slope_aligned.tif'),
    'aspect': os.path.join(ENVIRONMENT_DIR, 'aspect_aligned.tif'),
    'landcover': os.path.join(ENVIRONMENT_DIR, 'landcover_aligned.tif'),
}

# 坐标系
WGS84_CRS = "EPSG:4326"

# 车辆物理参数
WB = 2.9  # 轴距 [m]
L = 2.9   # 轴距，用于Pure Pursuit [m]
MAX_STEER = np.deg2rad(30.0)  # 最大转向角 [rad]
MAX_SPEED = 50.0 / 3.6      # 最大速度 [m/s]
MAX_ACCEL = 3.0           # 最大加速度 [m/s^2]

# 仿真参数
DT = 0.25                   # 时间步长 [s], 4Hz
SIM_MAX_TIME = 2000         # 最大仿真时间 [s]

