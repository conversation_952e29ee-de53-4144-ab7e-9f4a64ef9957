import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy.spatial import KDTree
from plot_rules import apply_plot_rules
import scipy.interpolate

def calculate_cumulative_distance(df):
    """计算轨迹的累积行驶距离"""
    points = df[['x', 'y']].values
    distances = np.sqrt(np.sum(np.diff(points, axis=0)**2, axis=1))
    return np.insert(np.cumsum(distances), 0, 0)

def main():
    """主函数，执行数据处理和生成6x5矩阵对比图。"""
    # --- 文件路径配置 ---
    REF_PATH_FILE = 'Pure_pursuit_and_Randoemforest/data/processed/trajectory_1_processed.csv'
    SIM_OUTPUT_FILE = 'Pure_pursuit_and_Randoemforest/results/trajectory_1/simulated_trajectory.csv'
    OUTPUT_PLOT_FILE = 'Pure_pursuit_and_Randoemforest/results/trajectory_1/matrix_comparison_plot_5_20m.png'

    # --- 加载数据 ---
    try:
        ref_path_df = pd.read_csv(REF_PATH_FILE)
        real_traj_df = pd.read_csv(REF_PATH_FILE)
        sim_traj_df = pd.read_csv(SIM_OUTPUT_FILE)
    except FileNotFoundError as e:
        print(f"错误：无法找到数据文件 {e.filename}。")
        return

    # --- 时间对齐与插值 ---
    # 1. 创建统一的时间轴
    real_traj_df['time'] = (real_traj_df['timestamp_ms'] - real_traj_df['timestamp_ms'].min()) / 1000.0
    
    # 2. 对两个数据集进行插值，以进行点对点的比较
    common_time = sim_traj_df['time_s'].values
    
    interp_funcs = {
        'x': scipy.interpolate.interp1d(real_traj_df['time'], real_traj_df['x'], kind='linear', bounds_error=False, fill_value="extrapolate"),
        'y': scipy.interpolate.interp1d(real_traj_df['time'], real_traj_df['y'], kind='linear', bounds_error=False, fill_value="extrapolate"),
        'speed': scipy.interpolate.interp1d(real_traj_df['time'], real_traj_df['speed'], kind='linear', bounds_error=False, fill_value="extrapolate"),
        'accel': scipy.interpolate.interp1d(real_traj_df['time'], real_traj_df['horizontal_acceleration_ms2'], kind='linear', bounds_error=False, fill_value="extrapolate")
    }
    
    real_interp_df = pd.DataFrame({'time': common_time})
    for key, func in interp_funcs.items():
        real_interp_df[key] = func(common_time)

    # 3. 计算误差
    # 重新计算横向跟踪误差
    ref_kdtree = KDTree(ref_path_df[['x', 'y']].values)
    cross_track_errors, _ = ref_kdtree.query(sim_traj_df[['x', 'y']].values)
    sim_traj_df['cross_track_error'] = cross_track_errors

    # 计算随时间变化的位置误差
    sim_traj_df['position_error'] = np.sqrt(
        (sim_traj_df['x'].values - real_interp_df['x'].values)**2 +
        (sim_traj_df['y'].values - real_interp_df['y'].values)**2
    )
    # 计算累积位移
    real_traj_df['cumulative_dist'] = calculate_cumulative_distance(real_traj_df)
    sim_traj_df['cumulative_dist'] = calculate_cumulative_distance(sim_traj_df)

    # 计算模拟轨迹的加速度
    sim_traj_df['acceleration'] = sim_traj_df['speed'].diff() / sim_traj_df['time_s'].diff()
    sim_traj_df.fillna(0, inplace=True)
    
    # --- 绘图设置 ---
    apply_plot_rules()
    fig, axes = plt.subplots(6, 5, figsize=(50, 55), constrained_layout=True)
    fig.suptitle('仿真结果矩阵对比分析 (前瞻距离: 5-20m)', fontsize=40, y=1.02)
    
    time_windows = [('全局', None), ('前60s', 60), ('前120s', 120), ('前300s', 300), ('前600s', 600)]
    
    # --- 生成矩阵图 ---
    for col, (title, time_limit) in enumerate(time_windows):
        # 筛选数据
        if time_limit:
            real_df = real_traj_df[real_traj_df['time'] <= time_limit].copy()
            sim_df = sim_traj_df[sim_traj_df['time_s'] <= time_limit].copy()
            ref_df = ref_path_df # 参考路径保持完整以提供背景
        else:
            real_df, sim_df, ref_df = real_traj_df.copy(), sim_traj_df.copy(), ref_path_df.copy()

        # --- 行 0: 路径对比 (相对坐标) ---
        ax = axes[0, col]
        temp_real_df, temp_sim_df = real_df.copy(), sim_df.copy()
        combined_x = pd.concat([temp_real_df['x'], temp_sim_df['x']])
        combined_y = pd.concat([temp_real_df['y'], temp_sim_df['y']])
        x_min, y_min = combined_x.min(), combined_y.min()
        
        ax.plot(ref_df['x'] - x_min, ref_df['y'] - y_min, 'k--', label='参考路径', lw=1.5)
        ax.plot(temp_real_df['x'] - x_min, temp_real_df['y'] - y_min, 'b-', label='真实轨迹', lw=2)
        ax.plot(temp_sim_df['x'] - x_min, temp_sim_df['y'] - y_min, 'r-', label='模拟轨迹', lw=2)
        ax.set_title(f'路径对比 ({title})', fontsize=22)
        ax.set_aspect('equal', adjustable='box')
        ax.legend()

        # --- 行 1: 速度 vs. 时间 ---
        ax = axes[1, col]
        ax.plot(real_df['time'], real_df['speed'], 'b-', label='真实速度')
        ax.plot(sim_df['time_s'], sim_df['speed'], 'r-', label='模拟速度')
        ax.set_title(f'速度 vs. 时间 ({title})', fontsize=22)
        ax.legend()
        
        # --- 行 2: 加速度 vs. 时间 ---
        ax = axes[2, col]
        ax.plot(real_df['time'], real_df['horizontal_acceleration_ms2'], 'b-', label='真实加速度')
        ax.plot(sim_df['time_s'], sim_df['accel'], 'r-', label='模拟加速度')
        ax.set_title(f'加速度 vs. 时间 ({title})', fontsize=22)
        ax.legend()

        # --- 行 3: 横向跟踪误差 vs. 时间 ---
        # (需要从全局sim_df中筛选，因为它包含误差列)
        error_df = sim_traj_df[sim_traj_df['time_s'] <= time_limit] if time_limit else sim_traj_df
        ax = axes[3, col]
        ax.plot(error_df['time_s'], error_df['cross_track_error'], 'm-', label='横向跟踪误差')
        ax.set_title(f'跟踪误差 vs. 时间 ({title})', fontsize=22)
        ax.set_ylabel('误差 (米)')
        ax.legend()
        
        # --- 行 4: 累积位移 vs. 时间 ---
        ax = axes[4, col]
        ax.plot(real_df['time'], real_df['cumulative_dist'], 'b-', label='真实累积位移')
        ax.plot(sim_df['time_s'], sim_df['cumulative_dist'], 'r-', label='模拟累积位移')
        ax.set_title(f'累积位移 vs. 时间 ({title})', fontsize=22)
        ax.set_ylabel('位移 (米)')
        ax.legend()

        # --- 行 5: 位置误差 vs. 时间 ---
        pos_error_df = sim_traj_df[sim_traj_df['time_s'] <= time_limit] if time_limit else sim_traj_df
        ax = axes[5, col]
        ax.plot(pos_error_df['time_s'], pos_error_df['position_error'], 'c-', label='仿真位置误差')
        ax.set_title(f'位置误差 vs. 时间 ({title})', fontsize=22)
        ax.set_ylabel('误差 (米)')
        ax.legend()

    # --- 统一格式 ---
    for ax in axes.flatten():
        ax.grid(True)
        ax.tick_params(axis='both', which='major', labelsize=16)
        if not ax.get_legend(): continue
        for text in ax.get_legend().get_texts():
            text.set_fontsize(18)
    
    # 设置X轴标签
    for row in range(1, 6):
        for col in range(5):
            axes[row, col].set_xlabel('时间 (秒)', fontsize=20)

    plt.savefig(OUTPUT_PLOT_FILE, dpi=150, bbox_inches='tight')
    print(f"\n矩阵对比图已成功保存至: {OUTPUT_PLOT_FILE}")

if __name__ == '__main__':
    main()

