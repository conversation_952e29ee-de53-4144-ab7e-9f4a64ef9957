import pandas as pd
import numpy as np
import joblib
import os
import sys
import logging

# --- 动态路径设置，确保可以从任何地方运行脚本 ---
# 将项目根目录添加到Python路径中
# 这使得我们可以使用 'from src import ...' 这样的绝对路径导入
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error

from src import config

# --- 日志设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def train_model():
    """
    使用全新的、带有前瞻性特征的数据集，训练一个新的速度预测模型。
    """
    logging.info("--- 开始训练新的速度预测模型 ---")

    # 1. 加载我们新生成的数据集
    data_path = os.path.join(config.PROCESSED_DATA_DIR, 'all_trajectories_processed.csv')
    try:
        df = pd.read_csv(data_path)
        logging.info(f"成功加载数据集，包含 {len(df)} 条记录。")
    except FileNotFoundError:
        logging.error(f"错误: 数据集未找到于 {data_path}。请先运行 data_preprocessing.py。")
        return

    # 2. 特征选择 (Feature Selection)
    # 我们选择所有我们精心设计的瞬时特征和前瞻性特征
    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    # !! 关键修正: 移除 'current_speed' 特征，避免数据泄露。          !!
    # !! 模型应该根据环境预测速度，而不是靠抄袭当前速度来"作弊"。        !!
    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    features = [
        # 'current_speed', # <- 移除这个特征!
        'effective_slope', 
        'curvature', 
        'landcover',
        'dem',
        'curvature_max_next_10m', 'effective_slope_mean_next_10m', 'elevation_change_next_10m',
        'curvature_max_next_30m', 'effective_slope_mean_next_30m', 'elevation_change_next_30m',
        'curvature_max_next_50m', 'effective_slope_mean_next_50m', 'elevation_change_next_50m',
        'curvature_max_next_150m', 'effective_slope_mean_next_150m', 'elevation_change_next_150m',
    ]
    
    # 目标变量是瞬时速度 (或者说是下一个时间点的目标速度)
    target = 'speed'

    logging.info(f"选用的特征: {features}")
    
    # 准备训练数据
    X = df[features]
    y = df[target]
    
    # 检查是否有无穷大或NaN值 (保险起见)
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    # y中也可能有NaN，所以基于X的有效索引来对齐y
    y = y[X.index] 
    X.dropna(inplace=True)
    y = y[X.index] # 再次对齐

    # 3. 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    logging.info(f"数据已划分为 {len(X_train)} 条训练样本和 {len(X_test)} 条测试样本。")

    # 4. 模型训练
    logging.info("正在初始化并训练 RandomForestRegressor 模型...")
    # 可以调整这些参数，但默认值通常是一个很好的起点
    model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1, oob_score=True)
    
    model.fit(X_train, y_train)
    logging.info("模型训练完成。")

    # 5. 模型评估
    logging.info("--- 模型评估 ---")
    # 使用 Out-of-Bag 分数进行快速验证
    logging.info(f"袋外分数 (OOB Score R²): {model.oob_score_:.4f}")
    
    # 在测试集上进行更正式的评估
    y_pred = model.predict(X_test)
    r2 = r2_score(y_test, y_pred)
    mse = mean_squared_error(y_test, y_pred)
    
    logging.info(f"测试集 R² 分数: {r2:.4f}")
    logging.info(f"测试集均方误差 (MSE): {mse:.4f}")

    # 6. 保存模型
    # 我们将覆盖旧的模型文件，以便仿真脚本能直接使用新模型
    model_path = os.path.join(config.MODELS_DIR, 'random_forest_speed_model.joblib')
    joblib.dump(model, model_path)
    logging.info(f"模型已成功保存至: {model_path}")
    
    if r2 > 0.7:
        logging.info("评估结果看起来很不错！R² 分数显著，模型很可能已经学到了关键信息。")
    else:
        logging.warning("警告: R² 分数较低，模型可能未能充分学习，或特征仍需改进。")

if __name__ == '__main__':
    train_model() 