import numpy as np
import math
import config

class VehicleState:
    """用于封装车辆状态的数据类"""
    def __init__(self, x=0.0, y=0.0, yaw=0.0, v=0.0):
        self.x = x
        self.y = y
        self.yaw = yaw  # 航向角
        self.v = v    # 速度

def normalize_angle(angle):
    """将角度归一化到 [-pi, pi] 范围"""
    while angle > np.pi:
        angle -= 2.0 * np.pi
    while angle < -np.pi:
        angle += 2.0 * np.pi
    return angle

def update_state(state, acceleration, delta, dt):
    """
    根据运动学自行车模型更新车辆状态。
    
    :param state: 当前车辆状态 (VehicleState)
    :param acceleration: 加速度指令 (m/s^2)
    :param delta: 前轮转角 (rad)
    :param dt: 时间步长 (s)
    :return: 更新后的车辆状态 (VehicleState)
    """
    # 修正：首先更新速度，确保位移计算使用的是当前步骤的最新速度
    state.v += acceleration * dt
    # 速度不能为负
    state.v = max(0, state.v)
    
    # 使用更新后的速度来更新位置和航向角
    state.x += state.v * math.cos(state.yaw) * dt
    state.y += state.v * math.sin(state.yaw) * dt
    state.yaw += state.v / config.L * math.tan(delta) * dt
    state.yaw = normalize_angle(state.yaw)
    
    return state

