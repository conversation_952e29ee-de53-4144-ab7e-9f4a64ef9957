import pandas as pd
import numpy as np
import rasterio
import os
import logging
from tqdm import tqdm

import config

# --- 日志设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def calculate_curvature(x, y):
    """
    计算由x, y坐标定义的路径的曲率。
    """
    dx = np.gradient(x)
    dy = np.gradient(y)
    ddx = np.gradient(dx)
    ddy = np.gradient(dy)
    curvature = np.abs(ddx * dy - ddy * dx) / (dx**2 + dy**2)**1.5
    return np.nan_to_num(curvature)

def process_single_trajectory(csv_path, env_files):
    """
    处理单个轨迹文件。不再进行栅格化，而是为原始轨迹上的每个点生成前瞻性特征。
    """
    logging.info(f"正在处理轨迹: {os.path.basename(csv_path)}")

    # 1. 加载原始轨迹数据
    try:
        df = pd.read_csv(csv_path)
        df.rename(columns={'longitude': 'x', 'latitude': 'y', 'velocity_2d_ms': 'speed'}, inplace=True)
    except FileNotFoundError:
        logging.error(f"文件未找到: {csv_path}")
        return None

    # 2. 一次性为每个点采样所有环境特征
    logging.info("正在采样环境特征...")
    coords = list(zip(df['x'], df['y']))
    for name, path in env_files.items():
        with rasterio.open(path) as src:
            # 使用rasterio的sample方法高效提取所有点的像元值
            df[name] = [val[0] for val in src.sample(coords)]

    # 3. 计算瞬时几何与派生特征
    logging.info("正在计算瞬时特征 (有效坡度, 曲率)...")
    
    # 有效坡度
    dx = np.gradient(df['x'].values)
    dy = np.gradient(df['y'].values)
    heading = np.degrees(np.arctan2(dy, dx))
    aspect_rad = np.radians(df['aspect'])
    heading_rad = np.radians((heading + 360) % 360)
    angle_diff = heading_rad - aspect_rad
    df['effective_slope'] = df['slope'] * np.cos(angle_diff)
    
    # 曲率
    df['curvature'] = calculate_curvature(df['x'].values, df['y'].values)

    # --- 速度修正 ---
    # 放弃使用原始文件中不可靠的速度值。
    # 我们根据处理后的x, y坐标和时间戳重新计算一个物理上一致的速度。
    logging.info("正在根据位移和时间重新计算速度...")
    time_diff_s = df['timestamp_ms'].diff() / 1000.0
    dist_diff_m = np.sqrt(df['x'].diff()**2 + df['y'].diff()**2)
    
    # 重新计算速度，处理第一个点(NaN)和时间差为0的情况
    new_speed = (dist_diff_m / time_diff_s).fillna(0)
    # 替换掉旧的、不准确的速度列
    df['speed'] = new_speed.replace([np.inf, -np.inf], 0)
    # --- 修正结束 ---

    # 新增：创建 'current_speed' 特征，即上一个时间点的速度
    df['current_speed'] = df['speed'].shift(1).fillna(0) # 用0填充第一个点的NaN

    # 4. 准备生成前瞻性特征
    logging.info("正在生成前瞻性特征...")
    
    # 沿路径计算累积距离
    distances = np.sqrt(np.diff(df['x'], prepend=df['x'].iloc[0])**2 + np.diff(df['y'], prepend=df['y'].iloc[0])**2)
    df['cumulative_dist'] = np.cumsum(distances)

    lookahead_distances = [10, 30, 50, 150]  # 定义多个前瞻距离（单位：米），新增150m
    
    feature_dict = {}
    # 为所有新特征列初始化空列表
    for dist in lookahead_distances:
        feature_dict[f'curvature_max_next_{dist}m'] = []
        feature_dict[f'effective_slope_mean_next_{dist}m'] = []
        feature_dict[f'elevation_change_next_{dist}m'] = []

    # 迭代每个点以计算其未来的特征
    for i in tqdm(range(len(df)), desc=f"  - 为 {os.path.basename(csv_path)} 生成前瞻特征"):
        current_dist = df['cumulative_dist'].iloc[i]
        
        for dist in lookahead_distances:
            # 找到前方 'dist' 米范围内的路径段
            future_mask = (df['cumulative_dist'] > current_dist) & (df['cumulative_dist'] <= current_dist + dist)
            future_segment = df[future_mask]

            if not future_segment.empty:
                # 为未来路段计算特征
                curvature_max = future_segment['curvature'].max()
                effective_slope_mean = future_segment['effective_slope'].mean()
                elevation_change = future_segment['dem'].iloc[-1] - df['dem'].iloc[i]
                
                feature_dict[f'curvature_max_next_{dist}m'].append(curvature_max)
                feature_dict[f'effective_slope_mean_next_{dist}m'].append(effective_slope_mean)
                feature_dict[f'elevation_change_next_{dist}m'].append(elevation_change)
            else:
                # 如果是路径末端，没有未来路段，则填充默认值
                feature_dict[f'curvature_max_next_{dist}m'].append(0)
                feature_dict[f'effective_slope_mean_next_{dist}m'].append(0)
                feature_dict[f'elevation_change_next_{dist}m'].append(0)
    
    # 将所有收集到的特征一次性赋给DataFrame
    for col_name, values in feature_dict.items():
        df[col_name] = values
        
    # 5. 保存处理后的数据
    # 我们将覆盖旧的处理文件，因为这是新的标准处理流程
    output_filename = os.path.basename(csv_path).replace('.csv', '_processed.csv')
    output_path = os.path.join(config.PROCESSED_DATA_DIR, output_filename)
    df.to_csv(output_path, index=False)
    logging.info(f"处理成功，已保存至: {output_path}")
    
    return df

def run_preprocessing():
    """
    运行全新的数据预处理流程。
    """
    all_processed_data = []
    for file_path in config.TRAJECTORY_FILES:
        processed_df = process_single_trajectory(file_path, config.ENV_FILES)
        if processed_df is not None:
            all_processed_data.append(processed_df)
    
    if not all_processed_data:
        logging.error("没有数据被处理，程序退出。")
        return

    # 将所有轨迹数据合并成一个用于训练的大文件
    final_df = pd.concat(all_processed_data, ignore_index=True)
    
    # 保存最终合并好的数据集
    final_output_path = os.path.join(config.PROCESSED_DATA_DIR, 'all_trajectories_processed.csv')
    final_df.to_csv(final_output_path, index=False)
    logging.info(f"\n所有轨迹处理并合并完成。可用于训练的数据集已保存至: {final_output_path}")
    logging.info(f"总数据点数: {len(final_df)}")
    logging.info(f"可用特征列: {final_df.columns.tolist()}")

if __name__ == '__main__':
    run_preprocessing() 