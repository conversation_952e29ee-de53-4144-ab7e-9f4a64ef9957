import os
import math
import rioxarray
from tqdm import tqdm
import sys

# --- Configuration ---

# 1. Define the regions of interest with their central points
REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}

# 2. Define the size of the square bounding box in kilometers
BOX_SIZE_KM = 200

# 3. Define the output directory for all data
OUTPUT_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data'))

# 4. Define data sources (Public on AWS S3)
DATA_SOURCES = {
    "dem": {
        "url": "https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt",
        "output_filename": "dem.tif"
    },
    "landcover": {
        "url": "https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt",
        "output_filename": "landcover.tif"
    }
}

# --- Helper Functions ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """Calculates a square bounding box given a center point and size."""
    lat_rad = math.radians(lat_deg)
    earth_radius = 6371
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    half_size_lat_deg = (size_km / 2) * lat_deg_per_km
    half_size_lon_deg = (size_km / 2) * lon_deg_per_km
    return (
        lon_deg - half_size_lon_deg,
        lat_deg - half_size_lat_deg,
        lon_deg + half_size_lon_deg,
        lat_deg + half_size_lat_deg,
    )

# --- Main Execution ---

def download_data_rioxarray():
    """Main function to download all specified GIS data using rioxarray."""
    print(f"Base output directory: {OUTPUT_BASE_DIR}")
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # Configure Rasterio to not sign AWS requests
    os.environ['AWS_NO_SIGN_REQUEST'] = 'YES'

    total_downloads = len(REGIONS) * len(DATA_SOURCES)
    errors_occurred = False
    
    with tqdm(total=total_downloads, desc="Overall Progress") as pbar:
        for region_name, coords in REGIONS.items():
            pbar.set_postfix_str(f"Region: {region_name}")
            region_dir = os.path.join(OUTPUT_BASE_DIR, region_name)
            os.makedirs(region_dir, exist_ok=True)

            bbox = calculate_bounding_box(coords["lat"], coords["lon"], BOX_SIZE_KM)
            
            for source_name, source_info in DATA_SOURCES.items():
                pbar.set_description(f"Downloading {source_name.upper()} for {region_name}")
                output_path = os.path.join(region_dir, source_info["output_filename"])

                if os.path.exists(output_path):
                    print(f"\n  - {source_name.upper()} for {region_name} already exists. Skipping.")
                    pbar.update(1)
                    continue

                try:
                    # Open remote dataset, clip it, and save
                    rds = rioxarray.open_rasterio(source_info["url"])
                    clipped = rds.rio.clip_box(
                        minx=bbox[0],
                        miny=bbox[1],
                        maxx=bbox[2],
                        maxy=bbox[3],
                        crs="EPSG:4326"
                    )
                    clipped.rio.to_raster(output_path)
                    print(f"\n  - Successfully downloaded to {output_path}")

                except Exception as e:
                    print(f"\n  - ERROR downloading {source_name.upper()} for {region_name}.")
                    print(f"  - URL: {source_info['url']}")
                    print(f"  - Error details: {e}")
                    errors_occurred = True
                
                finally:
                    pbar.update(1)

    if errors_occurred:
        print("\nOne or more downloads failed. The script will be retried automatically.")
        sys.exit(1)
    else:
        print("\n\nAll files have been successfully downloaded!")

if __name__ == "__main__":
    download_data_rioxarray() 
import math
import rioxarray
from tqdm import tqdm
import sys

# --- Configuration ---

# 1. Define the regions of interest with their central points
REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}

# 2. Define the size of the square bounding box in kilometers
BOX_SIZE_KM = 200

# 3. Define the output directory for all data
OUTPUT_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data'))

# 4. Define data sources (Public on AWS S3)
DATA_SOURCES = {
    "dem": {
        "url": "https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt",
        "output_filename": "dem.tif"
    },
    "landcover": {
        "url": "https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt",
        "output_filename": "landcover.tif"
    }
}

# --- Helper Functions ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """Calculates a square bounding box given a center point and size."""
    lat_rad = math.radians(lat_deg)
    earth_radius = 6371
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    half_size_lat_deg = (size_km / 2) * lat_deg_per_km
    half_size_lon_deg = (size_km / 2) * lon_deg_per_km
    return (
        lon_deg - half_size_lon_deg,
        lat_deg - half_size_lat_deg,
        lon_deg + half_size_lon_deg,
        lat_deg + half_size_lat_deg,
    )

# --- Main Execution ---

def download_data_rioxarray():
    """Main function to download all specified GIS data using rioxarray."""
    print(f"Base output directory: {OUTPUT_BASE_DIR}")
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # Configure Rasterio to not sign AWS requests
    os.environ['AWS_NO_SIGN_REQUEST'] = 'YES'

    total_downloads = len(REGIONS) * len(DATA_SOURCES)
    errors_occurred = False
    
    with tqdm(total=total_downloads, desc="Overall Progress") as pbar:
        for region_name, coords in REGIONS.items():
            pbar.set_postfix_str(f"Region: {region_name}")
            region_dir = os.path.join(OUTPUT_BASE_DIR, region_name)
            os.makedirs(region_dir, exist_ok=True)

            bbox = calculate_bounding_box(coords["lat"], coords["lon"], BOX_SIZE_KM)
            
            for source_name, source_info in DATA_SOURCES.items():
                pbar.set_description(f"Downloading {source_name.upper()} for {region_name}")
                output_path = os.path.join(region_dir, source_info["output_filename"])

                if os.path.exists(output_path):
                    print(f"\n  - {source_name.upper()} for {region_name} already exists. Skipping.")
                    pbar.update(1)
                    continue

                try:
                    # Open remote dataset, clip it, and save
                    rds = rioxarray.open_rasterio(source_info["url"])
                    clipped = rds.rio.clip_box(
                        minx=bbox[0],
                        miny=bbox[1],
                        maxx=bbox[2],
                        maxy=bbox[3],
                        crs="EPSG:4326"
                    )
                    clipped.rio.to_raster(output_path)
                    print(f"\n  - Successfully downloaded to {output_path}")

                except Exception as e:
                    print(f"\n  - ERROR downloading {source_name.upper()} for {region_name}.")
                    print(f"  - URL: {source_info['url']}")
                    print(f"  - Error details: {e}")
                    errors_occurred = True
                
                finally:
                    pbar.update(1)

    if errors_occurred:
        print("\nOne or more downloads failed. The script will be retried automatically.")
        sys.exit(1)
    else:
        print("\n\nAll files have been successfully downloaded!")

if __name__ == "__main__":
    download_data_rioxarray() 
import math
import rioxarray
from tqdm import tqdm
import sys

# --- Configuration ---

# 1. Define the regions of interest with their central points
REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}

# 2. Define the size of the square bounding box in kilometers
BOX_SIZE_KM = 200

# 3. Define the output directory for all data
OUTPUT_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data'))

# 4. Define data sources (Public on AWS S3)
DATA_SOURCES = {
    "dem": {
        "url": "https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt",
        "output_filename": "dem.tif"
    },
    "landcover": {
        "url": "https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt",
        "output_filename": "landcover.tif"
    }
}

# --- Helper Functions ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """Calculates a square bounding box given a center point and size."""
    lat_rad = math.radians(lat_deg)
    earth_radius = 6371
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    half_size_lat_deg = (size_km / 2) * lat_deg_per_km
    half_size_lon_deg = (size_km / 2) * lon_deg_per_km
    return (
        lon_deg - half_size_lon_deg,
        lat_deg - half_size_lat_deg,
        lon_deg + half_size_lon_deg,
        lat_deg + half_size_lat_deg,
    )

# --- Main Execution ---

def download_data_rioxarray():
    """Main function to download all specified GIS data using rioxarray."""
    print(f"Base output directory: {OUTPUT_BASE_DIR}")
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # Configure Rasterio to not sign AWS requests
    os.environ['AWS_NO_SIGN_REQUEST'] = 'YES'

    total_downloads = len(REGIONS) * len(DATA_SOURCES)
    errors_occurred = False
    
    with tqdm(total=total_downloads, desc="Overall Progress") as pbar:
        for region_name, coords in REGIONS.items():
            pbar.set_postfix_str(f"Region: {region_name}")
            region_dir = os.path.join(OUTPUT_BASE_DIR, region_name)
            os.makedirs(region_dir, exist_ok=True)

            bbox = calculate_bounding_box(coords["lat"], coords["lon"], BOX_SIZE_KM)
            
            for source_name, source_info in DATA_SOURCES.items():
                pbar.set_description(f"Downloading {source_name.upper()} for {region_name}")
                output_path = os.path.join(region_dir, source_info["output_filename"])

                if os.path.exists(output_path):
                    print(f"\n  - {source_name.upper()} for {region_name} already exists. Skipping.")
                    pbar.update(1)
                    continue

                try:
                    # Open remote dataset, clip it, and save
                    rds = rioxarray.open_rasterio(source_info["url"])
                    clipped = rds.rio.clip_box(
                        minx=bbox[0],
                        miny=bbox[1],
                        maxx=bbox[2],
                        maxy=bbox[3],
                        crs="EPSG:4326"
                    )
                    clipped.rio.to_raster(output_path)
                    print(f"\n  - Successfully downloaded to {output_path}")

                except Exception as e:
                    print(f"\n  - ERROR downloading {source_name.upper()} for {region_name}.")
                    print(f"  - URL: {source_info['url']}")
                    print(f"  - Error details: {e}")
                    errors_occurred = True
                
                finally:
                    pbar.update(1)

    if errors_occurred:
        print("\nOne or more downloads failed. The script will be retried automatically.")
        sys.exit(1)
    else:
        print("\n\nAll files have been successfully downloaded!")

if __name__ == "__main__":
    download_data_rioxarray() 
import math
import rioxarray
from tqdm import tqdm
import sys

# --- Configuration ---

# 1. Define the regions of interest with their central points
REGIONS = {
    "scottish_highlands": {"lat": 57.0, "lon": -4.5},
    "israel_palestine": {"lat": 31.5, "lon": 35.0},
    "kashmir": {"lat": 34.0, "lon": 76.0},
    "gulf_war_kuwait": {"lat": 29.3, "lon": 47.5},
    "donbas_ukraine_russia": {"lat": 48.5, "lon": 38.0},
}

# 2. Define the size of the square bounding box in kilometers
BOX_SIZE_KM = 200

# 3. Define the output directory for all data
OUTPUT_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'data'))

# 4. Define data sources (Public on AWS S3)
DATA_SOURCES = {
    "dem": {
        "url": "https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt",
        "output_filename": "dem.tif"
    },
    "landcover": {
        "url": "https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt",
        "output_filename": "landcover.tif"
    }
}

# --- Helper Functions ---

def calculate_bounding_box(lat_deg, lon_deg, size_km):
    """Calculates a square bounding box given a center point and size."""
    lat_rad = math.radians(lat_deg)
    earth_radius = 6371
    lat_deg_per_km = 1.0 / 111.0
    lon_deg_per_km = 1.0 / (111.0 * math.cos(lat_rad))
    half_size_lat_deg = (size_km / 2) * lat_deg_per_km
    half_size_lon_deg = (size_km / 2) * lon_deg_per_km
    return (
        lon_deg - half_size_lon_deg,
        lat_deg - half_size_lat_deg,
        lon_deg + half_size_lon_deg,
        lat_deg + half_size_lat_deg,
    )

# --- Main Execution ---

def download_data_rioxarray():
    """Main function to download all specified GIS data using rioxarray."""
    print(f"Base output directory: {OUTPUT_BASE_DIR}")
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # Configure Rasterio to not sign AWS requests
    os.environ['AWS_NO_SIGN_REQUEST'] = 'YES'

    total_downloads = len(REGIONS) * len(DATA_SOURCES)
    errors_occurred = False
    
    with tqdm(total=total_downloads, desc="Overall Progress") as pbar:
        for region_name, coords in REGIONS.items():
            pbar.set_postfix_str(f"Region: {region_name}")
            region_dir = os.path.join(OUTPUT_BASE_DIR, region_name)
            os.makedirs(region_dir, exist_ok=True)

            bbox = calculate_bounding_box(coords["lat"], coords["lon"], BOX_SIZE_KM)
            
            for source_name, source_info in DATA_SOURCES.items():
                pbar.set_description(f"Downloading {source_name.upper()} for {region_name}")
                output_path = os.path.join(region_dir, source_info["output_filename"])

                if os.path.exists(output_path):
                    print(f"\n  - {source_name.upper()} for {region_name} already exists. Skipping.")
                    pbar.update(1)
                    continue

                try:
                    # Open remote dataset, clip it, and save
                    rds = rioxarray.open_rasterio(source_info["url"])
                    clipped = rds.rio.clip_box(
                        minx=bbox[0],
                        miny=bbox[1],
                        maxx=bbox[2],
                        maxy=bbox[3],
                        crs="EPSG:4326"
                    )
                    clipped.rio.to_raster(output_path)
                    print(f"\n  - Successfully downloaded to {output_path}")

                except Exception as e:
                    print(f"\n  - ERROR downloading {source_name.upper()} for {region_name}.")
                    print(f"  - URL: {source_info['url']}")
                    print(f"  - Error details: {e}")
                    errors_occurred = True
                
                finally:
                    pbar.update(1)

    if errors_occurred:
        print("\nOne or more downloads failed. The script will be retried automatically.")
        sys.exit(1)
    else:
        print("\n\nAll files have been successfully downloaded!")

if __name__ == "__main__":
    download_data_rioxarray() 
 
 
 