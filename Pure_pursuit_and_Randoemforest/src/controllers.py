import numpy as np
import math
import config

class PIDController:
    """一个经典的PID速度控制器"""
    def __init__(self, Kp, Ki, Kd, min_output, max_output):
        self.kp = Kp
        self.ki = Ki
        self.kd = Kd
        self.min_output = min_output
        self.max_output = max_output
        self.p_error = 0.0
        self.i_error = 0.0
        self.d_error = 0.0

    def update(self, target_speed, current_speed, dt):
        """
        计算PID控制输出（加速度）。
        :param target_speed: 目标速度
        :param current_speed: 当前速度
        :param dt: 时间间隔
        :return: 加速度指令
        """
        error = target_speed - current_speed
        
        self.p_error = error
        self.i_error += error * dt
        self.d_error = (error - self.p_error) / dt if dt > 0 else 0.0

        output = self.kp * self.p_error + self.ki * self.i_error + self.kd * self.d_error
        
        # 限制输出范围
        return np.clip(output, self.min_output, self.max_output)


class PurePursuitController:
    """Pure Pursuit路径跟踪控制器"""
    def __init__(self, k=0.1, lookahead_min=2.0, lookahead_max=10.0):
        self.k = k  # 前瞻增益
        self.Ld_min = lookahead_min
        self.Ld_max = lookahead_max
        self.wheelbase = config.L

    def search_target_index(self, state, path_x, path_y, path_kdtree, Ld):
        """
        在路径上搜索前瞻目标点。
        """
        # 1. 找到路径上离车辆最近的点
        _, closest_idx = path_kdtree.query([state.x, state.y])

        # 2. 从最近点开始向前搜索，找到第一个超过前瞻距离的点
        search_idx = closest_idx
        while search_idx < len(path_x):
            dx = path_x[search_idx] - state.x
            dy = path_y[search_idx] - state.y
            if math.hypot(dx, dy) >= Ld:
                return search_idx, True  # 找到目标点
            search_idx += 1
        
        # 3. 如果找不到（例如接近终点），则使用最后一个点
        return len(path_x) - 1, False

    def update(self, state, path_x, path_y, path_kdtree):
        """
        计算Pure Pursuit的转向角指令。
        """
        # 1. 计算自适应前瞻距离
        Ld = self.k * state.v + self.Ld_min
        Ld = np.clip(Ld, self.Ld_min, self.Ld_max)

        # 2. 寻找前瞻点
        # 找到路径上离车辆最近点的索引
        _, nearest_idx = path_kdtree.query([state.x, state.y])
        
        # 从最近点开始，向前搜索第一个距离超过Ld的点
        target_idx = nearest_idx
        while target_idx < len(path_x) - 1:
            dist = np.hypot(path_x[target_idx] - state.x, path_y[target_idx] - state.y)
            if dist >= Ld:
                break
            target_idx += 1
            
        target_point = (path_x[target_idx], path_y[target_idx])

        # 3. 计算转向角
        alpha = np.arctan2(target_point[1] - state.y, target_point[0] - state.x) - state.yaw
        delta = np.arctan2(2.0 * self.wheelbase * np.sin(alpha), Ld)
        
        # 返回转向角和找到的前瞻点
        return delta, target_point



