# 车辆控制与速度预测模型：算法与实现文档

## 1. 项目概述

本项目旨在为模拟环境中的自动驾驶车辆开发一套完整的控制与决策系统。为了有效管理复杂性，我们将车辆的自主控制任务分解为两个核心子问题：
1.  **横向控制 (Lateral Control)**：负责车辆的**路径跟踪**。我们采用 **Pure Pursuit (纯跟踪)** 算法，确保车辆能够精确地沿着预定轨迹行驶。
2.  **纵向控制 (Longitudinal Control)**：负责车辆的**速度调节**。这是一个两阶段过程，首先由 **Random Forest (随机森林)** 模型根据前方环境预测出最优的**目标速度**，再由一个 **PID 控制器** 计算出为达到该速度所需的**加速度**。

在开发初期，速度预测模型存在严重的**数据泄露**问题。通过剔除作弊特征、重新训练，并利用 **SHAP** 进行深度可解释性分析，我们最终成功构建出一个R²分数为 **0.9522** 的"诚实"且高效的速度预测模型，其决策逻辑完全符合驾驶常识。

## 2. 系统总体控制流程

整个系统在一个循环中运行，清晰地体现了横向与纵向控制的分离与协作。

```mermaid
graph TD
    %% --- 黑白风格定义 ---
    classDef processNode fill:#fff,stroke:#333,stroke-width:2px;
    classDef decisionNode fill:#fff,stroke:#333,stroke-width:2px;

    %% --- 节点定义 ---
    A["开始/初始化"]:::processNode;
    B{"获取车辆当前状态<br>(位置, 速度, 航向)"}:::decisionNode;
    C["<b>横向控制 (方向)</b><br>Pure Pursuit 算法<br>计算: 转向角 δ"]:::processNode;
    D["<b>纵向控制 (速度)</b><br>AI决策 + PID执行<br>计算: 加速度 a"]:::processNode;
    E{"车辆物理模型<br>根据 δ 和 a 更新状态"}:::decisionNode;
    F{"是否到达终点?"}:::decisionNode;
    G["结束"]:::processNode;

    %% --- 流程连接 ---
    A --> B;
    B --> C;
    B --> D;
    C --> E;
    D --> E;
    E --> F;
    F -- "是" --> G;
    F -- "否" --> B;
```

## 3. 核心算法与模型原理

### 3.1 横向控制：Pure Pursuit (纯跟踪)

Pure Pursuit 是一种基于几何学的路径跟踪算法。其核心思想是模仿人类驾驶员的行为：**始终注视着前方一定距离的目标点，并调整方向盘使车辆朝该目标点驶去**。

#### **原理与项目实现**

1.  **确定车辆后轴中心位置**：这是所有几何计算的基准点。

2.  **寻找前瞻目标点 (Lookahead Point)**：
    - **动态前瞻距离 \(L_d\)**: 在我们的项目中，前瞻距离根据车辆**当前速度动态变化**：
      \[ L_d = k \cdot v_{current} + L_{d,min} \]
      其中 \( k \) 是增益系数，\( v_{current} \) 是车辆当前速度，\( L_{d,min} \) 是最小前瞻距离。这使得车辆在高速时看得更远以保证平顺，在低速时看得更近以保证精度。
    - **搜索目标点**: 在预定路径上，向前搜索第一个与车辆后轴中心距离超过动态前瞻距离 \(L_d\) 的点，作为追逐的目标点。

3.  **计算转向角 \( \delta \)**：这个步骤的核心是几何推导。我们将车辆后轴中心、前轴中心和前瞻目标点想象成一个圆弧的三个点。为了让车辆能够平滑地驶向目标点，就需要计算出这段"虚拟圆弧"的曲率，并据此推导出前轮应有的转向角。在我们的项目代码 `controllers.py` 中，这一过程被浓缩为以下精确的转向角公式：
    \[ \delta = \arctan\left(\frac{2L \sin(\alpha)}{L_d}\right) \]
    其中，\( \alpha \) (alpha) 是车辆当前航向与目标点方向之间的夹角，\( L \) 是我们代码中明确定义的**车辆轴距 (`wheelbase`)**，\( L_d \) 是前瞻距离。这个公式是Pure Pursuit算法的直接应用，确保了理论与代码实现的一致性。

### 3.2 纵向控制：AI决策 (随机森林) + PID执行

纵向控制分为高层决策和低层执行两个阶段。

#### **3.2.1 高层决策：随机森林速度预测**

随机森林模型扮演着"智能驾驶员大脑"的角色，负责进行高层决策，预测出在当前环境下最合理、最安全的**目标速度**。

**核心思想：群体的智慧 (Wisdom of the Crowd)**

单一的决策树模型容易产生"偏见"，可能会对训练数据中的某些特例或噪声产生过拟合，导致其泛化能力不佳。随机森林的核心思想就是"人多力量大"：它不再依赖一棵"专家树"，而是构建起由数百棵各不相同的"普通树"组成的"森林"。当需要做决策时，它会听取森林里每一棵树的"意见"，然后通过"民主投票"（回归任务中是取平均值）的方式，得到一个更健壮、更可靠的最终决策。

**工作原理：双重随机性**

为了保证森林中的每一棵树都不尽相同，随机森林在构建过程中巧妙地引入了"双重随机性"：

1.  **数据随机性 (Bagging - Bootstrap Aggregating)**: 在训练每一棵树之前，算法并不是使用全部的原始数据。相反，它会从原始训练集中进行**有放回的随机抽样**，生成一个和原始数据集大小相同但内容略有不同的"自采样数据集"。这意味着每棵树学习的"教材"都是不一样的。

2.  **特征随机性**: 在树生长的过程中，当需要在某个节点进行分裂时，算法并不会在全部的M个特征中寻找最优分裂点。它会先**随机选取m个特征 (m < M)**，然后只在这m个特征中寻找最优解。这进一步确保了即使两棵树的"教材"相似，它们在做决策时的"思考角度"也不同，从而降低了树与树之间的相关性。

**最终预测：取平均值**

当一个新数据点需要被预测时，它会被输入到森林中的每一棵树。每一棵树 \(f_b(x)\) 都会独立地给出一个自己的预测值。随机森林的最终预测 \(\hat{y}\) 就是所有这些预测值的算术平均值：
\[ \hat{y} = \frac{1}{B} \sum_{b=1}^{B} f_b(x) \]
其中 `B` 是森林中树的总数量。这个平均过程有效地平滑了单一决策树可能存在的极端预测，使得最终结果更加稳定和准确。

**随机森林工作原理流程图**

```mermaid
graph TD
    %% --- 黑白风格定义 ---
    classDef dataNode fill:#fff,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5;
    classDef processNode fill:#fff,stroke:#333,stroke-width:2px;
    classDef resultNode fill:#f9f9f9,stroke:#333,stroke-width:2px;

    subgraph "训练阶段 (Training Phase)"
        direction LR
        A["原始训练数据<br>(N个样本, M个特征)"]:::dataNode;

        subgraph "树 1"
            B1["<b>数据抽样 (Bagging)</b><br>有放回抽样得到<br>数据集 D1"]:::processNode;
            F1["<b>特征抽样</b><br>在每个节点<br>随机选 m 个特征"]:::processNode;
            T1["训练出决策树 T1"]:::resultNode;
            A --> B1 --> F1 --> T1;
        end
        subgraph "树 2"
            B2["<b>数据抽样 (Bagging)</b><br>有放回抽样得到<br>数据集 D2"]:::processNode;
            F2["<b>特征抽样</b><br>在每个节点<br>随机选 m 个特征"]:::processNode;
            T2["训练出决策树 T2"]:::resultNode;
            A --> B2 --> F2 --> T2;
        end
        subgraph "..."
            B3["..."]:::processNode;
        end
        subgraph "树 B"
            B4["<b>数据抽样 (Bagging)</b><br>有放回抽样得到<br>数据集 DB"]:::processNode;
            F4["<b>特征抽样</b><br>在每个节点<br>随机选 m 个特征"]:::processNode;
            TB["训练出决策树 TB"]:::resultNode;
            A --> B4 --> F4 --> TB;
        end
    end
    
    P["新数据点输入"]:::dataNode
    
    subgraph "预测阶段 (Prediction Phase)"
        direction TB
        R("<b>汇总结果</b><br>对所有预测值取平均<br>ŷ = (ŷ1+ŷ2+...+ŷB)/B"):::resultNode
        T1 -- "预测值 ŷ1" --> R
        T2 -- "预测值 ŷ2" --> R
        B3 -- "..." --> R
        TB -- "预测值 ŷB" --> R
    end

    P --> T1;
    P --> T2;
    P --> B3;
    P --> TB;
```

**本项目中的具体实现**

1.  **数据准备**: 我们的起点是 `trajectory.csv` 和一系列 `.tif` 地理信息文件。在 `data_loader.py` (或 `data_preprocessing.py`) 中，我们通过**特征工程**，遍历轨迹上的每个时间点，计算出其对应的16个环境特征（如前方曲率、坡度等）。最终，我们构建出一个特征矩阵 `X`（形状为：样本数 × 16）和一个目标向量 `y`（包含每个样本对应的真实速度）。

2.  **模型训练**: 在 `train_speed_model.py` 中，我们使用 `scikit-learn` 库的 `RandomForestRegressor` 类。我们实例化一个模型（例如，`model = RandomForestRegressor(n_estimators=100, random_state=42)`），然后调用 `.fit(X, y)` 方法。这个`.fit()`方法就替我们完成了上述流程图中的所有训练步骤：包括数据抽样、特征抽样和构建每一棵决策树，最终形成一个训练好的"森林"，保存在 `model` 对象中。

#### **3.2.2 低层执行：PID 加速度控制**

PID（比例-积分-微分）控制器扮演着"精准的脚"的角色，负责执行上层AI的决策。

- **工作原理**: 它的任务是，通过精确调节**加速度**，消除**目标速度**（来自随机森林模型）与**当前实际速度**之间的误差。
- **计算流程**:
    1.  **计算误差**: \( e(t) = V_{target} - V_{current}(t) \)
    2.  **计算三项控制量**:
        - **比例项 (P)**: \( K_p \cdot e(t) \)。它提供最主要的控制作用，误差越大，控制力越强。
        - **积分项 (I)**: \( K_i \cdot \int e(t) dt \)。它用于消除系统的稳态误差，即即使误差很小也能持续施加控制力，直到误差为零。
        - **微分项 (D)**: \( K_d \cdot \frac{de(t)}{dt} \)。它用于抑制速度变化的趋势，防止系统在目标值附近震荡，起到稳定作用。
    3.  **输出加速度**: 三项加总，得到最终的加速度指令：
        \[ \text{Acceleration} = K_p e(t) + K_i \int e(t) dt + K_d \frac{de(t)}{dt} \]
    这个加速度指令被传递给车辆的物理模型，从而驱动车辆向目标速度收敛。

### 3.3 模型评估与可解释性

#### **3.3.1 R² (决定系数)**

R²衡量了模型对真实数据拟合的好坏，我们的模型达到了 **0.9522**，意味着它成功解释了车辆速度变化的95.22%，拟合效果非常好。
\[ R^2 = 1 - \frac{\sum (y_i - \hat{y}_i)^2}{\sum (y_i - \bar{y})^2} \]

#### **3.3.2 SHAP (SHapley Additive exPlanations)**

- **核心思想与计算原理**:
    SHAP的核心是借鉴了合作博弈论中的**夏普利值 (Shapley Value)**，它旨在解决一个问题：一个团队完成一个项目获得了收益，如何公平地给每个成员分配功劳？
    
    在模型预测中，**"团队"**就是所有特征的集合，**"项目"**就是做出一次具体的预测，**"收益"**就是该预测值与所有样本的平均预测值之间的差额。SHAP要做的就是计算每个特征（"团队成员"）在这个项目中应该分到多少"功劳"（贡献度）。

    为了做到公平，SHAP的计算思想如下：要评估"特征A"的贡献，它会考虑所有可能的特征组合（即所有可能的"子团队"）。
    1.  在一个不包含"特征A"的子团队的基础上，计算模型的预测值。
    2.  然后，将"特征A"加入这个子团队，再次计算模型的预测值。
    3.  两者之差，就是"特征A"在**本次合作中**的边际贡献。
    
    夏普利值（即SHAP值）就是将"特征A"在**所有**可能的子团队组合下的边际贡献，进行加权平均后得到的结果。这个值唯一地满足了公平分配所需的几个性质（如对称性、有效性等）。
    
    这个过程的理论计算公式如下，但其计算量是指数级的，非常巨大：
    \[ \phi_j = \sum_{S \subseteq F \setminus \{j\}} \frac{|S|!(|F|-|S|-1)!}{|F|!} [f_{S \cup \{j\}}(x_{S \cup \{j\}}) - f_S(x_S)] \]
    幸运的是，对于我们使用的随机森林这类树模型，`shap`库采用了一种名为 **TreeSHAP** 的高效算法，它利用树的结构，可以在多项式时间内**精确计算**出SHAP值，而不是一个粗略的近似。

- **分析结果**: SHAP图清晰地显示**`前方10m最大曲率`** 的值较高时，其SHAP值为负，有力地将预测速度**拉低**，这完美符合驾驶常识。

### 3.4 系统集成与车辆状态更新

前面我们分别介绍了横向（Pure Pursuit）和纵向（PID）两个独立的控制器。那么，它们计算出的转向角 `δ` 和加速度 `a` 是如何协同工作，并最终决定车辆下一时刻的位置和姿态的呢？

#### **控制指令的并行计算**

首先，需要明确的是，横向与纵向控制是**解耦的 (Decoupled)**。在每一个时间步 `t`，两个控制器基于**相同**的当前车辆状态，进行**并行计算**：

-   **横向控制器 (Pure Pursuit)**：根据车辆当前的位置和航向，独立计算出所需的**转向角 `δ`**。它不关心速度。
-   **纵向控制器 (PID)**：根据目标速度与当前速度的误差，独立计算出所需的**加速度 `a`**。它不关心方向。

这两个指令是相互独立的，并非一前一后的顺序关系。

#### **车辆运动学模型与状态更新**

计算出的转向角 `δ` 和加速度 `a` 会被同时送入车辆的**运动学模型 (Kinematic Bicycle Model)** 中，通过积分来预测车辆在 `dt` 时间后的新状态。

在我们的项目中，车辆轴距 `L` 被设定为 **2.9米**。

假设在 `t` 时刻，车辆的状态为 \( (x_t, y_t, \psi_t, v_t) \)，其中 \(x, y\) 是位置，\(\psi\) 是航向角，\(v\) 是速度。控制器输出的指令为 \(a_t\) 和 \(\delta_t\)。那么，在 `t+1` 时刻的新状态可以通过以下离散公式进行更新：

1.  **更新速度**:
    \[ v_{t+1} = v_t + a_t \cdot dt \]

2.  **更新航向角**:
    \[ \psi_{t+1} = \psi_t + \frac{v_t}{L} \tan(\delta_t) \cdot dt \]

3.  **更新位置**:
    \[ x_{t+1} = x_t + v_t \cos(\psi_t) \cdot dt \]
    \[ y_{t+1} = y_t + v_t \sin(\psi_t) \cdot dt \]

通过在每个控制周期内不断重复"感知 -> 决策 -> 执行（更新状态）"这个循环，我们就实现了车辆的完整动态仿真。

## 4. 系统实现与验证

### 4.1 关键代码模块说明

- `Pure_pursuit_and_Randoemforest/src/data_loader.py`: 负责加载轨迹和所有地理空间数据，并实现特征提取。
- `Pure_pursuit_and_Randoemforest/src/train_speed_model.py`: 训练、评估和保存随机森林模型。
- `Pure_pursuit_and_Randoemforest/src/analyze_model_contributions.py`: 使用`shap`进行可解释性分析。
- `Pure_pursuit_and_Randoemforest/src/create_animation.py`: 最终的可视化脚本，生成模型验证动画。

### 4.2 可视化验证

我们创建了一个**直接的模型验证**动画，而非物理仿真。动画的每一帧都从真实轨迹中提取环境特征，输入模型得到瞬时预测速度，并与真实速度进行对比。最终的视频显示，两条速度曲线几乎完美重合，直观地证明了模型的有效性。

## 5. 如何复现

1.  确保已安装 `requirements.txt` 中列出的所有Python依赖库。
2.  执行 `python src/data_preprocessing.py` 生成处理好的特征数据。
3.  执行 `python src/train_speed_model.py` 训练速度预测模型。
4.  执行 `python src/create_animation.py` 生成最终的模型验证动画。 

### 附录：模型输入特征详解

| 特征名称 (Chinese Name) | 分类 | 详细说明 |
| :--- | :--- | :--- |
| `瞬时有效坡度` | **瞬时特征** | 车辆当前位置的**有效坡度**。结合了地形坡度、坡向和车辆航向，计算出的车辆前进方向上的实际等效坡度。 |
| `瞬时曲率` | **瞬时特征** | 车辆当前位置的**路径曲率**。代表了当前位置道路的弯曲程度，值越大表示弯道越急。 |
| `瞬时地表覆盖类型` | **瞬时特征** | 车辆当前位置的**地表覆盖类型**。例如，森林、草地、建筑用地等，用一个数字代码表示。 |
| `瞬时海拔` | **瞬时特征** | 车辆当前位置的**海拔高度 (DEM)**。 |
| `前方10米内最大曲率` | **前瞻特征** | 从车辆当前位置起，**前方10米内**路径上的**最大曲率**。这是预判前方近距离急弯的关键指标。 |
| `前方10米内平均有效坡度` | **前瞻特征** | **前方10米内**路径上的**平均有效坡度**。 |
| `前方10米内海拔变化量` | **前瞻特征** | **前方10米内**路径的**海拔总变化量**。 |
| `前方30米内最大曲率` | **前瞻特征** | **前方30米内**路径上的**最大曲率**。 |
| `前方30米内平均有效坡度` | **前瞻特征** | **前方30米内**路径上的**平均有效坡度**。 |
| `前方30米内海拔变化量` | **前瞻特征** | **前方30米内**路径的**海拔总变化量**。 |
| `前方50米内最大曲率` | **前瞻特征** | **前方50米内**路径上的**最大曲率**。提供了中距离的弯道预判。 |
| `前方50米内平均有效坡度` | **前瞻特征** | **前方50米内**路径上的**平均有效坡度**。 |
| `前方50米内海拔变化量` | **前瞻特征** | **前方50米内**路径的**海拔总变化量**。 |
| `前方150米内最大曲率` | **前瞻特征** | **前方150米内**路径上的**最大曲率**。用于远距离的宏观路径规划，例如预判高速公路的大半径弯道。 |
| `前方150米内平均有效坡度`| **前瞻特征** | **前方150米内**路径上的**平均有效坡度**。 |
| `前方150米内海拔变化量`| **前瞻特征** | **前方150米内**路径的**海拔总变化量**。 |
