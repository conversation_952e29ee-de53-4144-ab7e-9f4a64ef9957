#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESA Copernicus Open Access Hub 数据下载器
用于下载 Sentinel-2 卫星数据
"""

import os
import requests
import json
import time
from datetime import datetime, timedelta
import zipfile
from pathlib import Path
import xml.etree.ElementTree as ET

class CopernicusDownloader:
    def __init__(self, username=None, password=None):
        """
        初始化 Copernicus 下载器
        
        Args:
            username: Copernicus 账户用户名
            password: Copernicus 账户密码
        """
        self.base_url = "https://scihub.copernicus.eu/dhus"
        self.username = username
        self.password = password
        self.session = requests.Session()
        
        if username and password:
            self.session.auth = (username, password)
        
        # 创建输出目录
        self.output_dir = Path("data/copernicus_downloads")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def search_products(self, area_of_interest, start_date, end_date, 
                       product_type="S2MSI2A", cloud_cover_percentage=20):
        """
        搜索符合条件的产品
        
        Args:
            area_of_interest: 感兴趣区域 (WKT格式)
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            product_type: 产品类型 (默认: S2MSI2A - Sentinel-2 Level-2A)
            cloud_cover_percentage: 最大云覆盖率
            
        Returns:
            list: 产品列表
        """
        print(f"搜索 {start_date} 到 {end_date} 期间的产品...")
        
        # 构建查询参数
        query_params = {
            'q': f'producttype:{product_type} AND footprint:"Intersects({area_of_interest})" AND beginposition:[{start_date}T00:00:00.000Z TO {end_date}T23:59:59.999Z] AND cloudcoverpercentage:[0 TO {cloud_cover_percentage}]',
            'rows': 100,
            'start': 0
        }
        
        try:
            response = self.session.get(f"{self.base_url}/search", params=query_params)
            response.raise_for_status()
            
            # 解析 XML 响应
            root = ET.fromstring(response.content)
            
            # 提取产品信息
            products = []
            for entry in root.findall('.//{http://www.w3.org/2005/Atom}entry'):
                product = {}
                
                # 获取产品标题
                title_elem = entry.find('.//{http://www.w3.org/2005/Atom}title')
                if title_elem is not None:
                    product['title'] = title_elem.text
                
                # 获取产品ID
                id_elem = entry.find('.//{http://www.w3.org/2005/Atom}id')
                if id_elem is not None:
                    product['id'] = id_elem.text
                
                # 获取云覆盖率
                cloud_elem = entry.find('.//{http://www.esa.int/esa/safe/sentinel/sentinel-1.0}Cloud_Coverage_Assessment')
                if cloud_elem is not None:
                    product['cloud_cover'] = float(cloud_elem.text)
                
                # 获取开始时间
                begin_elem = entry.find('.//{http://www.esa.int/esa/safe/sentinel/sentinel-1.0}beginposition')
                if begin_elem is not None:
                    product['begin_position'] = begin_elem.text
                
                products.append(product)
            
            print(f"找到 {len(products)} 个产品")
            return products
            
        except Exception as e:
            print(f"搜索产品时出错: {e}")
            return []
    
    def download_product(self, product_id, output_path=None):
        """
        下载指定产品
        
        Args:
            product_id: 产品ID
            output_path: 输出路径
            
        Returns:
            bool: 下载是否成功
        """
        if output_path is None:
            output_path = self.output_dir / f"{product_id}.zip"
        
        print(f"开始下载产品: {product_id}")
        
        try:
            # 获取下载链接
            download_url = f"{self.base_url}/odata/v1/Products('{product_id}')/$value"
            
            # 下载文件
            response = self.session.get(download_url, stream=True)
            response.raise_for_status()
            
            # 保存文件
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"下载完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"下载产品 {product_id} 时出错: {e}")
            return False
    
    def extract_product(self, zip_path, extract_dir=None):
        """
        解压下载的产品
        
        Args:
            zip_path: ZIP文件路径
            extract_dir: 解压目录
            
        Returns:
            str: 解压后的目录路径
        """
        if extract_dir is None:
            extract_dir = zip_path.parent / zip_path.stem
        
        print(f"解压文件: {zip_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            print(f"解压完成: {extract_dir}")
            return str(extract_dir)
            
        except Exception as e:
            print(f"解压文件时出错: {e}")
            return None

def create_wkt_polygon(center_lon, center_lat, size_km):
    """
    创建 WKT 格式的多边形
    
    Args:
        center_lon: 中心经度
        center_lat: 中心纬度
        size_km: 区域大小（公里）
        
    Returns:
        str: WKT 格式的多边形
    """
    # 计算边界框（简化计算）
    half_size_deg = (size_km / 2) / 111.0
    
    min_lon = center_lon - half_size_deg
    max_lon = center_lon + half_size_deg
    min_lat = center_lat - half_size_deg
    max_lat = center_lat + half_size_deg
    
    # 创建 WKT 多边形
    wkt = f"POLYGON(({min_lon} {min_lat}, {max_lon} {min_lat}, {max_lon} {max_lat}, {min_lon} {max_lat}, {min_lon} {min_lat}))"
    
    return wkt

def main():
    """
    主函数
    """
    print("=== ESA Copernicus Open Access Hub 数据下载器 ===")
    
    # 定义要下载的区域
    regions = {
        "Donbas_Region": {"center_lon": 37.8, "center_lat": 48.0},
        "Gulf_War_Kuwait": {"center_lon": 47.5, "center_lat": 29.5},
        "Kashmir_Region": {"center_lon": 76.5, "center_lat": 34.0},
        "Scottish_Highlands": {"center_lon": -4.5, "center_lat": 57.0},
        "Israel_Palestine": {"center_lon": 35.0, "center_lat": 31.5}
    }
    
    # 设置下载参数
    size_km = 200  # 200km x 200km
    start_date = '2023-01-01'
    end_date = '2023-12-31'
    max_cloud_cover = 20  # 最大云覆盖率20%
    
    # 创建下载器实例
    # 注意：您需要注册 Copernicus 账户并获取用户名和密码
    # 注册地址：https://scihub.copernicus.eu/dhus/#/home
    downloader = CopernicusDownloader()
    
    print("=" * 60)
    print("注意：使用此脚本前，请先注册 Copernicus 账户：")
    print("https://scihub.copernicus.eu/dhus/#/home")
    print("然后更新脚本中的用户名和密码")
    print("=" * 60)
    
    # 为每个区域搜索和下载数据
    for region_name, coords in regions.items():
        print(f"\n处理区域: {region_name}")
        print(f"中心坐标: ({coords['center_lon']}, {coords['center_lat']})")
        
        # 创建 WKT 多边形
        wkt_polygon = create_wkt_polygon(
            coords['center_lon'], 
            coords['center_lat'], 
            size_km
        )
        
        print(f"搜索区域: {wkt_polygon}")
        
        # 搜索产品
        products = downloader.search_products(
            area_of_interest=wkt_polygon,
            start_date=start_date,
            end_date=end_date,
            cloud_cover_percentage=max_cloud_cover
        )
        
        if products:
            print(f"找到 {len(products)} 个产品，选择云覆盖率最低的...")
            
            # 按云覆盖率排序，选择最好的产品
            products.sort(key=lambda x: x.get('cloud_cover', 100))
            best_product = products[0]
            
            print(f"选择产品: {best_product['title']}")
            print(f"云覆盖率: {best_product.get('cloud_cover', 'N/A')}%")
            
            # 下载产品
            output_path = downloader.output_dir / f"{region_name}_{best_product['id']}.zip"
            success = downloader.download_product(best_product['id'], output_path)
            
            if success:
                # 解压产品
                extract_dir = downloader.extract_product(output_path)
                if extract_dir:
                    print(f"{region_name} 数据处理完成: {extract_dir}")
                else:
                    print(f"{region_name} 解压失败")
            else:
                print(f"{region_name} 下载失败")
        else:
            print(f"{region_name} 没有找到符合条件的产品")
    
    print("\n所有区域处理完成！")
    print(f"下载的文件保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main() 