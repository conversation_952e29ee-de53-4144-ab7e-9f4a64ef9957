#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星云图下载器
从公开数据源下载卫星云图数据
"""

import os
import requests
import json
import time
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

class SatelliteImageryDownloader:
    def __init__(self):
        """
        初始化卫星云图下载器
        """
        self.output_dir = Path("data/satellite_imagery")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def download_cloud_cover_data(self, region_name, bounds, date):
        """
        下载云覆盖数据
        
        Args:
            region_name: 区域名称
            bounds: 边界框 [min_lon, min_lat, max_lon, max_lat]
            date: 日期 (YYYY-MM-DD)
            
        Returns:
            str: 输出文件路径
        """
        print(f"开始下载 {region_name} 的云覆盖数据...")
        
        try:
            # 创建示例数据
            cloud_data = self._create_sample_cloud_data(bounds, date)
            
            # 保存数据
            output_file = self.output_dir / f"{region_name}_cloud_cover_{date}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(cloud_data, f, ensure_ascii=False, indent=2)
            
            print(f"  ✅ {region_name} 云覆盖数据下载完成: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"  ❌ {region_name} 云覆盖数据下载失败: {e}")
            return None
    
    def _create_sample_cloud_data(self, bounds, date):
        """
        创建示例云覆盖数据
        
        Args:
            bounds: 边界框
            date: 日期
            
        Returns:
            dict: 示例数据
        """
        # 创建网格数据
        min_lon, min_lat, max_lon, max_lat = bounds
        lons = np.linspace(min_lon, max_lon, 100)
        lats = np.linspace(min_lat, max_lat, 100)
        
        # 生成随机云覆盖数据
        cloud_cover = np.random.uniform(0, 100, (len(lats), len(lons)))
        
        # 创建数据结构
        data = {
            'date': date,
            'bounds': bounds,
            'resolution': '0.01 degrees',
            'cloud_cover_data': {
                'longitudes': lons.tolist(),
                'latitudes': lats.tolist(),
                'values': cloud_cover.tolist()
            },
            'metadata': {
                'source': 'Sample Data',
                'description': '示例云覆盖数据，用于测试',
                'units': 'percentage'
            }
        }
        
        return data

def main():
    """
    主函数
    """
    print("=== 卫星云图下载器 ===")
    
    # 定义要下载云图的区域
    regions = {
        "以色列-巴勒斯坦地区": [35.0, 31.5, 200],  # [center_lon, center_lat, size_km]
        "克什米尔地区": [76.5, 34.0, 200],
        "苏格兰高地": [-4.5, 57.0, 200]
    }
    
    # 创建下载器
    downloader = SatelliteImageryDownloader()
    
    # 设置日期范围（最近30天）
    end_date = datetime.now()
    
    downloaded_files = []
    
    # 为每个区域下载云图数据
    for region_name, coords in regions.items():
        center_lon, center_lat, size_km = coords
        
        # 计算边界框
        half_size_deg = (size_km / 2) / 111.0
        bounds = [
            center_lon - half_size_deg,  # min_lon
            center_lat - half_size_deg,  # min_lat
            center_lon + half_size_deg,  # max_lon
            center_lat + half_size_deg   # max_lat
        ]
        
        print(f"\n处理区域: {region_name}")
        print(f"边界框: {bounds}")
        
        # 下载最近几天的云图数据
        for i in range(3):  # 下载最近3天的数据
            date = end_date - timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            
            print(f"  下载 {date_str} 的云图数据...")
            
            cloud_file = downloader.download_cloud_cover_data(region_name, bounds, date_str)
            
            if cloud_file:
                downloaded_files.append((region_name, cloud_file, date_str))
        
        # 避免请求过于频繁
        time.sleep(1)
    
    # 总结
    print(f"\n=== 下载完成 ===")
    print(f"成功下载 {len(downloaded_files)} 个云图数据文件:")
    for region_name, file_path, date in downloaded_files:
        print(f"  - {region_name} ({date}): {file_path}")
    
    print(f"\n所有云图数据保存在: {downloader.output_dir}")
    print("\n注意: 这是示例数据，实际使用时需要配置真实的卫星数据 API")

if __name__ == "__main__":
    main() 