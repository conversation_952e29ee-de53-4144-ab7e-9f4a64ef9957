import os
from functools import partial

import cv2
import numpy as np
import pandas as pd
import ee
import geemap
from matplotlib import pyplot as plt
from osgeo import gdal, osr, ogr
from pyproj import Transformer
import mmengine
import time

# 设置Google Cloud服务账户密钥文件路径
service_account_key_path = "just-landing-468209-u0-d7f8ef6de834.json"

# 使用服务账户进行Earth Engine身份验证
if os.path.exists(service_account_key_path):
    print(f"使用服务账户密钥文件: {service_account_key_path}")
    credentials = ee.ServiceAccountCredentials(
        "<EMAIL>",
        service_account_key_path
    )
    ee.Initialize(credentials)
    print("Earth Engine 初始化成功！")
else:
    print(f"错误：找不到服务账户密钥文件 {service_account_key_path}")
    exit(1)


def to_proj_4326(src_crs, x, y):
    transformer = Transformer.from_crs(src_crs, 'epsg:4326')
    x, y = transformer.transform(x, y)
    return x, y

def to_4326_osr(x, y, epsg=None, zone_number=None, is_northern_hemisphere=True):
    source_srs = osr.SpatialReference()
    if epsg is not None:
        source_srs.ImportFromEPSG(int(epsg.split(':')[1]))
    else:
        source_srs.SetUTM(zone_number, is_northern_hemisphere)

    target_srs = osr.SpatialReference()
    target_srs.ImportFromEPSG(4326)
    transform = osr.CoordinateTransformation(source_srs, target_srs)
    point = ogr.Geometry(ogr.wkbPoint)
    point.AddPoint(x, y)
    point.Transform(transform)

    return point.GetX(), point.GetY()

def download_tif_by_productid(data, vis=False):
    try:
        collections = ee.ImageCollection('COPERNICUS/S2_SR')
        product_id = data['S2_PRODUCT_ID']
        geometry = data['geometry']
        img_collection = collections.filter(ee.Filter.eq('PRODUCT_ID', product_id))
        assert img_collection.size().getInfo() == 1
        img = img_collection.first()
        # img = img.select('B2', 'B3', 'B4')

        geometry_utm = geometry.split('((')[1].split('))')[0]
        geometry_utm = geometry_utm.replace(', ', ',').replace(' ', ',')
        geometry_utm = geometry_utm.split(',')
        geometry_utm = [[float(geometry_utm[i]), float(geometry_utm[i + 1])] for i in range(0, len(geometry_utm), 2)]
        geometry = [to_proj_4326(data['crs'], *x) for x in geometry_utm]
        geometry = [[x[1], x[0]] for x in geometry]
        mean_lon = np.mean([x[0] for x in geometry[0:4]])
        mean_lat = np.mean([x[1] for x in geometry[0:4]])
        assert np.abs(mean_lon - data['lon']) < 0.001
        assert np.abs(mean_lat - data['lat']) < 0.001

        zone_number = int(data['utm'].split('N')[0])
        is_northern_hemisphere = True if data['utm'].split('N')[1] == 'N' else False
        geometry_osr = [to_4326_osr(*x, None, zone_number, is_northern_hemisphere) for x in geometry_utm]
        assert np.sum(np.abs(np.array(geometry) - np.array(geometry_osr)) > 0.00001)

        geometry = ee.Geometry.Polygon(geometry)

        img_filename = data['save_folder'] + '/' + data['dw_id']
        geemap.download_ee_image(img, filename=img_filename + '.tif', scale=10, region=geometry)
        if vis:
            # vis rgb bands
            rgb_img = visualize_tif(img_filename + '.tif', bounds=[1, 2, 3])
            cv2.imwrite(img_filename + '.png', rgb_img)
            # vis label
            label_filename = 'original_files' + '/' + data['labeler'].replace('-', '_') + '/' + data['hemisphere'] + '/' + str(data['biome']) + '/' + data['dw_id'] + '.tif'
            rgb_label = visualize_label(label_filename)
            plt.imsave(img_filename + '_label.png', rgb_label)
    except Exception as e:
        print(e)
        print(data['dw_id'])


def download_tif_by_bbox(region_name, center_lon, center_lat, size_km, start_date, end_date, save_folder, scale=10, max_cloud_cover=10):
    try:
        print(f"  [步骤1] 计算 {region_name} 的边界框...")
        # Calculate bounding box (approximate for 200km)
        # 1 degree of latitude is approximately 111 km
        # 1 degree of longitude is approximately 111 * cos(latitude) km
        delta_lat = (size_km / 2) / 111.0
        delta_lon = (size_km / 2) / (111.0 * np.cos(np.radians(center_lat)))

        min_lon = center_lon - delta_lon
        max_lon = center_lon + delta_lon
        min_lat = center_lat - delta_lat
        max_lat = center_lat + delta_lat

        print(f"  [步骤2] 边界框坐标: 经度[{min_lon:.4f}, {max_lon:.4f}], 纬度[{min_lat:.4f}, {max_lat:.4f}]")

        # Create an Earth Engine geometry for the bounding box
        geometry = ee.Geometry.Rectangle([min_lon, min_lat, max_lon, max_lat])

        print(f"  [步骤3] 查询Sentinel-2影像集合...")
        # Filter Sentinel-2 ImageCollection (using updated dataset)
        collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED') \
            .filterDate(start_date, end_date) \
            .filterBounds(geometry)

        # 移除云量过滤条件
        # print(f"  [步骤3.5] 尝试按云量过滤...")
        # try:
        #     collection = collection.filterMetadata('CLOUD_COVERAGE_MEAN', 'less_than', max_cloud_cover)
        #     print(f"  [步骤3.5] 云量过滤成功")
        # except Exception as e:
        #     print(f"  [步骤3.5] 云量字段不存在，跳过云量过滤: {e}")

        print(f"  [步骤4] 检查可用影像数量...")
        # Check if collection has any images
        collection_size = collection.size().getInfo()
        print(f"  [步骤4] 找到 {collection_size} 个符合条件的影像")
        
        if collection_size == 0:
            print(f"  [错误] 没有找到符合条件的影像： {region_name}")
            return

        print(f"  [步骤5] 选择第一个影像...")
        # Get the first image (could be improved with more selection logic)
        img = collection.first()

        # 将所有波段转换为浮点型，确保数据类型兼容性
        img = img.toFloat()

        # 选择 Sentinel-2 的常用光学波段
        selected_bands = ['B2', 'B3', 'B4', 'B8', 'B11', 'B12'] # 蓝色, 绿色, 红色, 近红外, 短波红外1, 短波红外2
        img = img.select(selected_bands)

        # 定义导出任务的描述和文件夹
        task_description = f"LULC_Export_{region_name.replace(' ', '_')}"
        folder_name = "EE_Exports_LULC" # Google Drive 中的文件夹名

        print(f"  [步骤6] 准备导出 {region_name} 的数据到 Google Drive (文件夹: {folder_name})...")
        print(f"  [注意] 这可能需要一段时间，具体取决于数据量和Earth Engine服务器负载...")

        # 启动导出任务
        task = ee.batch.Export.image.toDrive(**{
            'image': img,
            'description': task_description,
            'folder': folder_name,
            'scale': scale, # 使用与下载相同的缩放比例
            'region': geometry,  # 直接传递 ee.Geometry 对象
            'maxPixels': 1e13 # 允许处理更多的像素
        })
        task.start()

        print(f"  [步骤7] 导出任务已启动 (ID: {task.id})... 等待任务完成")

        # 轮询任务状态
        while task.active():
            print(f"  任务 {task.id} 正在进行中... 状态: {task.status()['state']}")
            time.sleep(30) # 每30秒检查一次

        # 任务完成后打印结果
        if task.status()['state'] == 'COMPLETED':
            print(f"  [完成] {region_name} 数据已成功导出到 Google Drive 文件夹 '{folder_name}'！")
            print(f"  您可以在 Google Drive 中找到文件: {task_description}.tif")
        else:
            print(f"  [错误] {region_name} 数据导出失败！状态: {task.status()['state']}")
            print(f"  错误信息: {task.status()['error_message']}")

    except Exception as e:
        print(f"  [错误] 下载 {region_name} 数据时发生错误: {e}")
        import traceback
        traceback.print_exc()


def visualize_tif(tif_file, bounds):
    dataset = gdal.Open(tif_file)
    img_gdal = dataset.ReadAsArray()[bounds]
    img_gdal = img_gdal.transpose(1, 2, 0)
    img_gdal = img_gdal / 8
    img_gdal[img_gdal > 255] = 255
    img_gdal[img_gdal < 0] = 0
    img_gdal = img_gdal.astype('uint8')
    return img_gdal

def visualize_label(label_filename):
    dataset = gdal.Open(label_filename)
    img_gdal = dataset.ReadAsArray()
    color_map = np.zeros_like(img_gdal)
    for i in range(1, 11):
        color_map[img_gdal == i] = i * 25
    color_map = color_map.astype('uint8')
    colors = plt.cm.ScalarMappable(cmap="viridis").to_rgba(color_map)
    colors = colors[:, :, 0:3]
    return colors



if __name__ == '__main__':
    print("开始执行Earth Engine数据下载脚本...")
    print("=" * 60)
    
    # 定义要下载的区域和参数
    regions = {
        "Donbas_Region": {"center_lon": 37.8, "center_lat": 48.0},
        "Gulf_War_Kuwait": {"center_lon": 47.5, "center_lat": 29.5},
        "Kashmir_Region": {"center_lon": 76.5, "center_lat": 34.0},
        "Scottish_Highlands": {"center_lon": -4.5, "center_lat": 57.0},
        "Israel_Palestine": {"center_lon": 35.0, "center_lat": 31.5},
    }
    
    size_km = 200  # 200km x 200km
    start_date = '2023-01-01'
    end_date = '2023-12-31'
    # 注意：save_folder 在这里仅用于打印信息，实际导出将保存到 Google Drive 的 "EE_Exports_LULC" 文件夹中
    save_folder = 'downloaded_lulc_data'  
    os.makedirs(save_folder, exist_ok=True) # 仍然创建本地文件夹，以便未来可能的本地保存或其他用途

    print(f"将为以下区域启动导出任务，数据将保存到 Google Drive 的 'EE_Exports_LULC' 文件夹中:")
    for region_name, coords in regions.items():
        print(f"  - {region_name}: 中心坐标 ({coords['center_lon']}, {coords['center_lat']}), 区域大小 {size_km}km x {size_km}km")
    print("=" * 60)

    # 逐个区域启动下载任务
    for region_name, coords in regions.items():
        print(f"\n开始处理 {region_name} 的导出任务...")
        try:
            download_tif_by_bbox(
                region_name, 
                coords["center_lon"], 
                coords["center_lat"], 
                size_km, 
                start_date, 
                end_date, 
                save_folder, # 传递 save_folder，尽管主要用于信息输出
                scale=10
            )
            print(f"{region_name} 的导出任务已提交。请检查Earth Engine任务管理器和Google Drive。")
        except Exception as e:
            print(f"{region_name} 导出任务启动失败: {e}")
    
    print("\n所有区域的导出任务已提交。请登录 Google Earth Engine 任务管理器 (Tasks tab) 查看任务进度，并在 Google Drive 的 'EE_Exports_LULC' 文件夹中查看导出的文件。")
