#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenStreetMap 路网数据下载器
从 OpenStreetMap 下载指定区域的路网数据
"""

import os
import requests
import json
import time
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

class OSMRoadNetworkDownloader:
    def __init__(self):
        """
        初始化 OSM 路网下载器
        """
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.output_dir = Path("data/road_networks")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def download_road_network(self, region_name, bounds):
        """
        下载指定区域的路网数据
        
        Args:
            region_name: 区域名称
            bounds: 边界框 [min_lon, min_lat, max_lon, max_lat]
            
        Returns:
            str: 输出文件路径
        """
        print(f"开始下载 {region_name} 的路网数据...")
        
        # 构建 Overpass API 查询
        query = self._build_overpass_query(bounds)
        
        try:
            # 发送请求
            print("  发送查询请求...")
            response = requests.post(self.base_url, data=query, timeout=300)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            
            if 'elements' not in data or len(data['elements']) == 0:
                print(f"  ⚠️  {region_name} 没有找到路网数据")
                return None
            
            print(f"  找到 {len(data['elements'])} 个路网元素")
            
            # 保存原始数据
            output_file = self.output_dir / f"{region_name}_roads.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"  ✅ {region_name} 路网数据下载完成: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"  ❌ {region_name} 路网数据下载失败: {e}")
            return None
    
    def _build_overpass_query(self, bounds):
        """
        构建 Overpass API 查询语句
        
        Args:
            bounds: 边界框 [min_lon, min_lat, max_lon, max_lat]
            
        Returns:
            str: 查询语句
        """
        min_lat, min_lon, max_lat, max_lon = bounds[1], bounds[0], bounds[3], bounds[2]
        
        query = f"""
        [out:json][timeout:300];
        (
          way["highway"]({min_lat},{min_lon},{max_lat},{max_lon});
          relation["highway"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out body;
        >;
        out skel qt;
        """
        
        return query

def main():
    """
    主函数
    """
    print("=== OpenStreetMap 路网数据下载器 ===")
    
    # 定义要下载路网的区域
    regions = {
        "以色列-巴勒斯坦地区": [35.0, 31.5, 200],  # [center_lon, center_lat, size_km]
        "克什米尔地区": [76.5, 34.0, 200],
        "苏格兰高地": [-4.5, 57.0, 200]
    }
    
    # 创建下载器
    downloader = OSMRoadNetworkDownloader()
    
    downloaded_files = []
    
    # 为每个区域下载路网数据
    for region_name, coords in regions.items():
        center_lon, center_lat, size_km = coords
        
        # 计算边界框
        half_size_deg = (size_km / 2) / 111.0
        bounds = [
            center_lon - half_size_deg,  # min_lon
            center_lat - half_size_deg,  # min_lat
            center_lon + half_size_deg,  # max_lon
            center_lat + half_size_deg   # max_lat
        ]
        
        print(f"\n处理区域: {region_name}")
        print(f"边界框: {bounds}")
        
        # 下载路网数据
        road_file = downloader.download_road_network(region_name, bounds)
        
        if road_file:
            downloaded_files.append((region_name, road_file))
        
        # 避免请求过于频繁
        time.sleep(2)
    
    # 总结
    print(f"\n=== 下载完成 ===")
    print(f"成功下载 {len(downloaded_files)} 个区域的路网数据:")
    for region_name, file_path in downloaded_files:
        print(f"  - {region_name}: {file_path}")
    
    print(f"\n所有路网数据保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main() 