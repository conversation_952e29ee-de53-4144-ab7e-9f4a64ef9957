#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用现有数据脚本
检查项目中已有的 DEM 和 LULC 数据，避免重复下载
"""

import os
from pathlib import Path
import glob
from datetime import datetime

def check_existing_data():
    """
    检查项目中已有的数据
    """
    print("=== 检查项目中已有的数据 ===")
    
    # 检查 DEM 数据
    print("\n1. 检查 DEM 数据:")
    dem_paths = [
        "data/DEM_raw/",
        "data/temp_dem_unzipped/",
        "data/temp_dem_zip/",
        "data/以色列-巴勒斯坦地区/",
        "data/克什米尔地区/",
        "data/苏格兰高地/",
        "data/顿巴斯地区/",
        "data/海湾战争区域(科威特)/"
    ]
    
    dem_files = []
    for path in dem_paths:
        if os.path.exists(path):
            files = glob.glob(f"{path}**/*.tif", recursive=True)
            dem_files.extend(files)
            if files:
                print(f"  ✓ {path}: 找到 {len(files)} 个 .tif 文件")
            else:
                print(f"  - {path}: 无 .tif 文件")
        else:
            print(f"  ✗ {path}: 目录不存在")
    
    # 检查 LULC 数据
    print("\n2. 检查 LULC 数据:")
    lulc_paths = [
        "data/lulc/",
        "data/LULC_raw/",
        "data/以色列-巴勒斯坦地区/",
        "data/克什米尔地区/",
        "data/苏格兰高地/",
        "data/顿巴斯地区/",
        "data/海湾战争区域(科威特)/"
    ]
    
    lulc_files = []
    for path in lulc_paths:
        if os.path.exists(path):
            files = glob.glob(f"{path}**/*.tif", recursive=True)
            lulc_files.extend(files)
            if files:
                print(f"  ✓ {path}: 找到 {len(files)} 个 .tif 文件")
            else:
                print(f"  - {path}: 无 .tif 文件")
        else:
            print(f"  ✗ {path}: 目录不存在")
    
    # 检查其他相关数据
    print("\n3. 检查其他相关数据:")
    other_paths = [
        "data/",
        "geospatial_trajectory_generator/data/",
        "geospatial_trajectory_generator/data/raw/",
        "geospatial_trajectory_generator/data/processed/"
    ]
    
    for path in other_paths:
        if os.path.exists(path):
            files = glob.glob(f"{path}**/*", recursive=True)
            if files:
                print(f"  ✓ {path}: 找到 {len(files)} 个文件/目录")
    
    # 总结
    print(f"\n=== 数据总结 ===")
    print(f"DEM 文件总数: {len(dem_files)}")
    print(f"LULC 文件总数: {len(lulc_files)}")
    
    if dem_files:
        print(f"\n可用的 DEM 文件:")
        for file in dem_files[:5]:  # 只显示前5个
            print(f"  - {file}")
        if len(dem_files) > 5:
            print(f"  ... 还有 {len(dem_files) - 5} 个文件")
    
    if lulc_files:
        print(f"\n可用的 LULC 文件:")
        for file in lulc_files[:5]:  # 只显示前5个
            print(f"  - {file}")
        if len(lulc_files) > 5:
            print(f"  ... 还有 {len(lulc_files) - 5} 个文件")
    
    return dem_files, lulc_files

def suggest_next_steps(dem_files, lulc_files):
    """
    根据现有数据建议下一步操作
    """
    print(f"\n=== 建议的下一步操作 ===")
    
    if dem_files and lulc_files:
        print("✅ 您已经有足够的 DEM 和 LULC 数据！")
        print("建议:")
        print("1. 直接使用现有数据进行轨迹生成")
        print("2. 检查数据质量，确保覆盖您需要的区域")
        print("3. 如果数据质量不满足要求，再考虑下载新数据")
        
    elif dem_files and not lulc_files:
        print("⚠️  您有 DEM 数据，但缺少 LULC 数据")
        print("建议:")
        print("1. 使用现有的 DEM 数据")
        print("2. 只下载 LULC 数据")
        print("3. 或者使用 DEM 数据生成简化的 LULC 分类")
        
    elif lulc_files and not dem_files:
        print("⚠️  您有 LULC 数据，但缺少 DEM 数据")
        print("建议:")
        print("1. 使用现有的 LULC 数据")
        print("2. 只下载 DEM 数据")
        print("3. 或者使用公开的 SRTM DEM 数据")
        
    else:
        print("❌ 缺少 DEM 和 LULC 数据")
        print("建议:")
        print("1. 注册地理空间数据云账户下载数据")
        print("2. 或者使用公开的全球数据集")
        print("3. 考虑使用模拟数据进行测试")

def create_data_summary():
    """
    创建数据摘要报告
    """
    print(f"\n=== 创建数据摘要报告 ===")
    
    report_path = "data/data_summary_report.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 项目数据摘要报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 数据目录结构\n\n")
        
        # 扫描主要数据目录
        data_dirs = ["data/", "geospatial_trajectory_generator/data/"]
        
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                f.write(f"### {data_dir}\n\n")
                for root, dirs, files in os.walk(data_dir):
                    level = root.replace(data_dir, '').count(os.sep)
                    indent = '  ' * level
                    f.write(f"{indent}- {os.path.basename(root)}/\n")
                    for file in files[:10]:  # 只显示前10个文件
                        f.write(f"{indent}  - {file}\n")
                    if len(files) > 10:
                        f.write(f"{indent}  - ... 还有 {len(files) - 10} 个文件\n")
                    f.write("\n")
    
    print(f"数据摘要报告已保存到: {report_path}")

def main():
    """
    主函数
    """
    print("开始检查项目中的现有数据...")
    
    # 检查现有数据
    dem_files, lulc_files = check_existing_data()
    
    # 建议下一步操作
    suggest_next_steps(dem_files, lulc_files)
    
    # 创建数据摘要报告
    create_data_summary()
    
    print(f"\n=== 检查完成 ===")
    print("如果您决定使用现有数据，可以直接进行轨迹生成。")
    print("如果需要下载新数据，请参考 README_alternative_data_sources.md")

if __name__ == "__main__":
    main() 