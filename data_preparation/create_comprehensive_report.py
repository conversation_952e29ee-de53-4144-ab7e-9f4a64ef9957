#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合数据报告生成器
汇总所有已获取的数据，包括 DEM、LULC、路网和卫星云图
"""

import os
import json
from pathlib import Path
from datetime import datetime
import matplotlib.pyplot as plt
import numpy as np

def create_comprehensive_report():
    """
    创建综合数据报告
    """
    print("=== 生成综合数据报告 ===")
    
    # 定义区域
    regions = {
        "以色列-巴勒斯坦地区": [35.0, 31.5, 200],
        "克什米尔地区": [76.5, 34.0, 200],
        "苏格兰高地": [-4.5, 57.0, 200]
    }
    
    report_data = {
        "生成时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "数据概览": {},
        "文件统计": {},
        "数据质量评估": {}
    }
    
    # 检查各类数据
    data_types = {
        "DEM数据": "data/{region}/temp_dem_mosaic.tif",
        "LULC数据": "data/{region}/lulc.tif",
        "路网数据": "data/road_networks/{region}_roads.json",
        "卫星云图": "data/satellite_imagery/{region}_cloud_cover_*.json"
    }
    
    for region_name, coords in regions.items():
        print(f"\n检查 {region_name} 的数据...")
        
        region_data = {
            "中心坐标": f"({coords[0]}, {coords[1]})",
            "区域大小": f"{coords[2]}km x {coords[2]}km",
            "数据文件": {}
        }
        
        # 检查 DEM 和 LULC 数据
        dem_file = data_types["DEM数据"].format(region=region_name)
        lulc_file = data_types["LULC数据"].format(region=region_name)
        
        if os.path.exists(dem_file):
            file_size = os.path.getsize(dem_file) / (1024 * 1024)  # MB
            region_data["数据文件"]["DEM"] = {
                "文件路径": dem_file,
                "文件大小": f"{file_size:.1f} MB",
                "状态": "✅ 可用"
            }
        else:
            region_data["数据文件"]["DEM"] = {"状态": "❌ 不可用"}
        
        if os.path.exists(lulc_file):
            file_size = os.path.getsize(lulc_file) / (1024 * 1024)  # MB
            region_data["数据文件"]["LULC"] = {
                "文件路径": lulc_file,
                "文件大小": f"{file_size:.1f} MB",
                "状态": "✅ 可用"
            }
        else:
            region_data["数据文件"]["LULC"] = {"状态": "❌ 不可用"}
        
        # 检查路网数据
        road_file = data_types["路网数据"].format(region=region_name)
        if os.path.exists(road_file):
            file_size = os.path.getsize(road_file) / (1024 * 1024)  # MB
            region_data["数据文件"]["路网"] = {
                "文件路径": road_file,
                "文件大小": f"{file_size:.1f} MB",
                "状态": "✅ 可用"
            }
        else:
            region_data["数据文件"]["路网"] = {"状态": "❌ 不可用"}
        
        # 检查卫星云图数据
        import glob
        cloud_files = glob.glob(data_types["卫星云图"].format(region=region_name))
        if cloud_files:
            total_size = sum(os.path.getsize(f) for f in cloud_files) / (1024 * 1024)  # MB
            region_data["数据文件"]["卫星云图"] = {
                "文件数量": len(cloud_files),
                "总大小": f"{total_size:.1f} MB",
                "日期范围": f"{cloud_files[0].split('_')[-1].replace('.json', '')} - {cloud_files[-1].split('_')[-1].replace('.json', '')}",
                "状态": "✅ 可用"
            }
        else:
            region_data["数据文件"]["卫星云图"] = {"状态": "❌ 不可用"}
        
        report_data["数据概览"][region_name] = region_data
    
    # 生成文件统计
    print("\n生成文件统计...")
    report_data["文件统计"] = {
        "总文件数": 0,
        "总大小": "0 MB",
        "按类型统计": {}
    }
    
    # 统计各类文件
    for data_type in ["DEM", "LULC", "路网", "卫星云图"]:
        count = 0
        total_size = 0
        
        for region_name in regions.keys():
            if data_type in report_data["数据概览"][region_name]["数据文件"]:
                file_info = report_data["数据概览"][region_name]["数据文件"][data_type]
                if "文件数量" in file_info:
                    count += file_info["文件数量"]
                    total_size += float(file_info["总大小"].replace(" MB", ""))
                elif "文件大小" in file_info:
                    count += 1
                    total_size += float(file_info["文件大小"].replace(" MB", ""))
        
        report_data["文件统计"]["按类型统计"][data_type] = {
            "文件数": count,
            "总大小": f"{total_size:.1f} MB"
        }
        report_data["文件统计"]["总文件数"] += count
    
    # 计算总大小
    total_size_mb = sum(
        float(info["总大小"].replace(" MB", "")) 
        for info in report_data["文件统计"]["按类型统计"].values()
    )
    report_data["文件统计"]["总大小"] = f"{total_size_mb:.1f} MB"
    
    # 数据质量评估
    print("生成数据质量评估...")
    report_data["数据质量评估"] = {
        "完整性": {},
        "可用性": {},
        "建议": []
    }
    
    for region_name in regions.keys():
        region_data = report_data["数据概览"][region_name]["数据文件"]
        
        # 检查完整性
        available_types = [data_type for data_type, info in region_data.items() if info["状态"] == "✅ 可用"]
        completeness = len(available_types) / len(region_data) * 100
        
        report_data["数据质量评估"]["完整性"][region_name] = f"{completeness:.1f}%"
        report_data["数据质量评估"]["可用性"][region_name] = available_types
        
        if completeness == 100:
            report_data["数据质量评估"]["建议"].append(f"{region_name}: 数据完整，可直接用于轨迹生成")
        elif completeness >= 75:
            report_data["数据质量评估"]["建议"].append(f"{region_name}: 数据较完整，建议补充缺失数据")
        else:
            report_data["数据质量评估"]["建议"].append(f"{region_name}: 数据不完整，需要获取更多数据")
    
    # 保存报告
    report_file = "data/comprehensive_data_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    # 生成 Markdown 报告
    md_report_file = "data/comprehensive_data_report.md"
    with open(md_report_file, 'w', encoding='utf-8') as f:
        f.write("# 综合数据报告\n\n")
        f.write(f"**生成时间**: {report_data['生成时间']}\n\n")
        
        f.write("## 📊 数据概览\n\n")
        for region_name, region_data in report_data["数据概览"].items():
            f.write(f"### {region_name}\n\n")
            f.write(f"- **中心坐标**: {region_data['中心坐标']}\n")
            f.write(f"- **区域大小**: {region_data['区域大小']}\n\n")
            
            f.write("| 数据类型 | 状态 | 文件大小 | 备注 |\n")
            f.write("|---------|------|----------|------|\n")
            
            for data_type, info in region_data["数据文件"].items():
                status = info["状态"]
                file_size = info.get("文件大小", "N/A")
                notes = ""
                
                if "文件数量" in info:
                    notes = f"{info['文件数量']} 个文件, {info['日期范围']}"
                elif "文件路径" in info:
                    notes = "单个文件"
                
                f.write(f"| {data_type} | {status} | {file_size} | {notes} |\n")
            
            f.write("\n")
        
        f.write("## 📈 文件统计\n\n")
        f.write(f"- **总文件数**: {report_data['文件统计']['总文件数']}\n")
        f.write(f"- **总大小**: {report_data['文件统计']['总大小']}\n\n")
        
        f.write("| 数据类型 | 文件数 | 总大小 |\n")
        f.write("|---------|--------|--------|\n")
        for data_type, stats in report_data["文件统计"]["按类型统计"].items():
            f.write(f"| {data_type} | {stats['文件数']} | {stats['总大小']} |\n")
        
        f.write("\n## 🎯 数据质量评估\n\n")
        
        f.write("### 完整性\n\n")
        for region_name, completeness in report_data["数据质量评估"]["完整性"].items():
            f.write(f"- **{region_name}**: {completeness}\n")
        
        f.write("\n### 可用性\n\n")
        for region_name, available_types in report_data["数据质量评估"]["可用性"].items():
            f.write(f"- **{region_name}**: {', '.join(available_types)}\n")
        
        f.write("\n### 建议\n\n")
        for suggestion in report_data["数据质量评估"]["建议"]:
            f.write(f"- {suggestion}\n")
        
        f.write("\n## 🚀 下一步行动\n\n")
        f.write("1. **立即开始轨迹生成**: 使用现有完整数据进行轨迹生成\n")
        f.write("2. **数据验证**: 检查数据质量和覆盖范围\n")
        f.write("3. **补充数据**: 为缺失的顿巴斯地区和海湾战争区域获取数据\n")
        f.write("4. **路网集成**: 将路网数据集成到轨迹生成算法中\n")
        f.write("5. **云图分析**: 使用卫星云图数据优化轨迹规划\n")
    
    print(f"✅ 综合数据报告已生成:")
    print(f"   - JSON 格式: {report_file}")
    print(f"   - Markdown 格式: {md_report_file}")
    
    return report_data

def create_data_visualization():
    """
    创建数据可视化
    """
    print("\n=== 创建数据可视化 ===")
    
    # 创建输出目录
    output_dir = Path("data/visualizations")
    output_dir.mkdir(exist_ok=True)
    
    # 读取报告数据
    with open("data/comprehensive_data_report.json", 'r', encoding='utf-8') as f:
        report_data = json.load(f)
    
    # 创建数据完整性图表
    regions = list(report_data["数据概览"].keys())
    completeness = [float(report_data["数据质量评估"]["完整性"][region].replace("%", "")) for region in regions]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 数据完整性柱状图
    bars = ax1.bar(regions, completeness, color=['#2E8B57', '#4682B4', '#CD853F'])
    ax1.set_title('各区域数据完整性', fontsize=14, fontweight='bold')
    ax1.set_ylabel('完整性 (%)')
    ax1.set_ylim(0, 100)
    
    # 添加数值标签
    for bar, value in zip(bars, completeness):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{value:.1f}%', ha='center', va='bottom')
    
    # 数据类型可用性热力图
    data_types = ["DEM", "LULC", "路网", "卫星云图"]
    availability_matrix = []
    
    for region in regions:
        region_availability = []
        for data_type in data_types:
            if data_type in report_data["数据质量评估"]["可用性"][region]:
                region_availability.append(1)
            else:
                region_availability.append(0)
        availability_matrix.append(region_availability)
    
    im = ax2.imshow(availability_matrix, cmap='RdYlGn', aspect='auto')
    ax2.set_xticks(range(len(data_types)))
    ax2.set_yticks(range(len(regions)))
    ax2.set_xticklabels(data_types, rotation=45)
    ax2.set_yticklabels(regions)
    ax2.set_title('数据类型可用性热力图', fontsize=14, fontweight='bold')
    
    # 添加数值标签
    for i in range(len(regions)):
        for j in range(len(data_types)):
            text = ax2.text(j, i, '✓' if availability_matrix[i][j] else '✗',
                           ha="center", va="center", color="black", fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'data_completeness_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 数据可视化已保存: {output_dir / 'data_completeness_visualization.png'}")

def main():
    """
    主函数
    """
    print("开始生成综合数据报告...")
    
    # 生成综合报告
    report_data = create_comprehensive_report()
    
    # 创建可视化
    create_data_visualization()
    
    print("\n=== 综合数据报告生成完成 ===")
    print("现在您可以:")
    print("1. 查看 data/comprehensive_data_report.md 了解详细情况")
    print("2. 开始使用现有数据进行轨迹生成")
    print("3. 根据需要补充缺失的数据")

if __name__ == "__main__":
    main() 