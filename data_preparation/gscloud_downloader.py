#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理空间数据云 (GSCloud) 数据下载器
用于下载 Landsat 和 Sentinel 卫星数据
"""

import os
import requests
import json
import time
from datetime import datetime, timedelta
import zipfile
from pathlib import Path
import xml.etree.ElementTree as ET

class GSCloudDownloader:
    def __init__(self, username=None, password=None):
        """
        初始化地理空间数据云下载器
        
        Args:
            username: 地理空间数据云账户用户名
            password: 地理空间数据云账户密码
        """
        self.base_url = "http://www.gscloud.cn"
        self.username = username
        self.password = password
        self.session = requests.Session()
        
        # 创建输出目录
        self.output_dir = Path("data/gscloud_downloads")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def login(self):
        """
        登录地理空间数据云
        
        Returns:
            bool: 登录是否成功
        """
        if not self.username or not self.password:
            print("错误：需要提供用户名和密码")
            return False
        
        login_url = f"{self.base_url}/user/login"
        login_data = {
            "username": self.username,
            "password": self.password
        }
        
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("success"):
                print("地理空间数据云登录成功！")
                return True
            else:
                print(f"登录失败: {result.get('message')}")
                return False
                
        except Exception as e:
            print(f"登录时出错: {e}")
            return False
    
    def search_products(self, dataset_name, spatial_filter, temporal_filter):
        """
        搜索产品
        
        Args:
            dataset_name: 数据集名称
            spatial_filter: 空间过滤器
            temporal_filter: 时间过滤器
            
        Returns:
            list: 产品列表
        """
        search_url = f"{self.base_url}/api/search"
        
        search_params = {
            "dataset": dataset_name,
            "spatial": spatial_filter,
            "temporal": temporal_filter,
            "page": 1,
            "size": 50
        }
        
        try:
            response = self.session.get(search_url, params=search_params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("success"):
                return result.get("data", [])
            else:
                print(f"搜索失败: {result.get('message')}")
                return []
                
        except Exception as e:
            print(f"搜索产品时出错: {e}")
            return []
    
    def download_product(self, product_id, output_path=None):
        """
        下载产品
        
        Args:
            product_id: 产品ID
            output_path: 输出路径
            
        Returns:
            bool: 下载是否成功
        """
        if output_path is None:
            output_path = self.output_dir / f"{product_id}.zip"
        
        print(f"开始下载产品: {product_id}")
        
        download_url = f"{self.base_url}/api/download/{product_id}"
        
        try:
            response = self.session.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"下载完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"下载产品 {product_id} 时出错: {e}")
            return False

def create_spatial_filter(center_lon, center_lat, size_km):
    """
    创建空间过滤器
    
    Args:
        center_lon: 中心经度
        center_lat: 中心纬度
        size_km: 区域大小（公里）
        
    Returns:
        dict: 空间过滤器
    """
    # 计算边界框
    half_size_deg = (size_km / 2) / 111.0
    
    min_lon = center_lon - half_size_deg
    max_lon = center_lon + half_size_deg
    min_lat = center_lat - half_size_deg
    max_lat = center_lat + half_size_deg
    
    return {
        "type": "bbox",
        "coordinates": [min_lon, min_lat, max_lon, max_lat]
    }

def create_temporal_filter(start_date, end_date):
    """
    创建时间过滤器
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        
    Returns:
        dict: 时间过滤器
    """
    return {
        "start": start_date,
        "end": end_date
    }

def main():
    """
    主函数
    """
    print("=== 地理空间数据云 (GSCloud) 数据下载器 ===")
    
    # 定义要下载的区域
    regions = {
        "Donbas_Region": {"center_lon": 37.8, "center_lat": 48.0},
        "Gulf_War_Kuwait": {"center_lon": 47.5, "center_lat": 29.5},
        "Kashmir_Region": {"center_lon": 76.5, "center_lat": 34.0},
        "Scottish_Highlands": {"center_lon": -4.5, "center_lat": 57.0},
        "Israel_Palestine": {"center_lon": 35.0, "center_lat": 31.5}
    }
    
    # 设置下载参数
    size_km = 200  # 200km x 200km
    start_date = '2023-01-01'
    end_date = '2023-12-31'
    
    # 创建下载器实例
    downloader = GSCloudDownloader()
    
    print("=" * 60)
    print("注意：使用此脚本前，请先注册地理空间数据云账户：")
    print("http://www.gscloud.cn/")
    print("然后更新脚本中的用户名和密码")
    print("=" * 60)
    
    # 登录
    if not downloader.login():
        print("登录失败，无法继续")
        return
    
    # 为每个区域搜索和下载数据
    for region_name, coords in regions.items():
        print(f"\n处理区域: {region_name}")
        print(f"中心坐标: ({coords['center_lon']}, {coords['center_lat']})")
        
        # 创建过滤器
        spatial_filter = create_spatial_filter(
            coords['center_lon'], 
            coords['center_lat'], 
            size_km
        )
        
        temporal_filter = create_temporal_filter(start_date, end_date)
        
        # 搜索产品
        products = downloader.search_products(
            dataset_name="Landsat8",
            spatial_filter=spatial_filter,
            temporal_filter=temporal_filter
        )
        
        if products:
            print(f"找到 {len(products)} 个产品")
            
            # 选择第一个产品进行下载
            product = products[0]
            product_id = product.get("id")
            
            if product_id:
                print(f"选择产品: {product.get('title', product_id)}")
                
                # 下载产品
                output_path = downloader.output_dir / f"{region_name}_{product_id}.zip"
                success = downloader.download_product(product_id, output_path)
                
                if success:
                    print(f"{region_name} 下载完成: {output_path}")
                else:
                    print(f"{region_name} 下载失败")
            else:
                print(f"{region_name} 产品ID无效")
        else:
            print(f"{region_name} 没有找到符合条件的数据")
    
    print("\n所有区域处理完成！")
    print(f"下载的文件保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main() 