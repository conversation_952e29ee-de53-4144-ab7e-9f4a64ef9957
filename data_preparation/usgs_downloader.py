#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
USGS Earth Explorer 数据下载器
用于下载 Landsat 和 Sentinel 卫星数据
"""

import os
import requests
import json
import time
from datetime import datetime, timedelta
import zipfile
from pathlib import Path
import xml.etree.ElementTree as ET

class USGSDownloader:
    def __init__(self, username=None, password=None):
        """
        初始化 USGS 下载器
        
        Args:
            username: USGS 账户用户名
            password: USGS 账户密码
        """
        self.base_url = "https://earthexplorer.usgs.gov/inventory/json/v/1.4.1"
        self.username = username
        self.password = password
        self.session = requests.Session()
        
        # 创建输出目录
        self.output_dir = Path("data/usgs_downloads")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def login(self):
        """
        登录 USGS Earth Explorer
        
        Returns:
            bool: 登录是否成功
        """
        if not self.username or not self.password:
            print("错误：需要提供用户名和密码")
            return False
        
        login_url = f"{self.base_url}/login"
        login_data = {
            "username": self.username,
            "password": self.password,
            "catalogId": "EE"
        }
        
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errorCode"):
                print(f"登录失败: {result.get('errorMessage')}")
                return False
            
            print("USGS 登录成功！")
            return True
            
        except Exception as e:
            print(f"登录时出错: {e}")
            return False
    
    def search_datasets(self, dataset_name="LANDSAT_TM_C1"):
        """
        搜索可用的数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            dict: 数据集信息
        """
        search_url = f"{self.base_url}/datasets"
        params = {
            "datasetName": dataset_name,
            "includeMessages": "false"
        }
        
        try:
            response = self.session.get(search_url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errorCode"):
                print(f"搜索数据集失败: {result.get('errorMessage')}")
                return None
            
            return result.get("data", [])
            
        except Exception as e:
            print(f"搜索数据集时出错: {e}")
            return None
    
    def search_scenes(self, dataset_name, spatial_filter, temporal_filter, 
                     additional_criteria=None):
        """
        搜索场景
        
        Args:
            dataset_name: 数据集名称
            spatial_filter: 空间过滤器
            temporal_filter: 时间过滤器
            additional_criteria: 附加条件
            
        Returns:
            list: 场景列表
        """
        search_url = f"{self.base_url}/search"
        
        search_criteria = {
            "datasetName": dataset_name,
            "spatialFilter": spatial_filter,
            "temporalFilter": temporal_filter
        }
        
        if additional_criteria:
            search_criteria.update(additional_criteria)
        
        try:
            response = self.session.post(search_url, json=search_criteria)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errorCode"):
                print(f"搜索场景失败: {result.get('errorMessage')}")
                return []
            
            return result.get("data", {}).get("results", [])
            
        except Exception as e:
            print(f"搜索场景时出错: {e}")
            return []
    
    def download_scene(self, scene_id, output_path=None):
        """
        下载场景
        
        Args:
            scene_id: 场景ID
            output_path: 输出路径
            
        Returns:
            bool: 下载是否成功
        """
        if output_path is None:
            output_path = self.output_dir / f"{scene_id}.zip"
        
        print(f"开始下载场景: {scene_id}")
        
        # 获取下载链接
        download_url = f"{self.base_url}/download"
        download_data = {
            "datasetName": "LANDSAT_TM_C1",
            "entityIds": [scene_id]
        }
        
        try:
            response = self.session.post(download_url, json=download_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errorCode"):
                print(f"获取下载链接失败: {result.get('errorMessage')}")
                return False
            
            # 获取下载链接
            download_info = result.get("data", [{}])[0]
            download_link = download_info.get("downloadUrl")
            
            if not download_link:
                print("未找到下载链接")
                return False
            
            # 下载文件
            print(f"从 {download_link} 下载...")
            file_response = self.session.get(download_link, stream=True)
            file_response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in file_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"下载完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"下载场景 {scene_id} 时出错: {e}")
            return False

def create_spatial_filter(center_lon, center_lat, size_km):
    """
    创建空间过滤器
    
    Args:
        center_lon: 中心经度
        center_lat: 中心纬度
        size_km: 区域大小（公里）
        
    Returns:
        dict: 空间过滤器
    """
    # 计算边界框
    half_size_deg = (size_km / 2) / 111.0
    
    min_lon = center_lon - half_size_deg
    max_lon = center_lon + half_size_deg
    min_lat = center_lat - half_size_deg
    max_lat = center_lat + half_size_deg
    
    return {
        "filterType": "mbr",
        "lowerLeft": {
            "latitude": min_lat,
            "longitude": min_lon
        },
        "upperRight": {
            "latitude": max_lat,
            "longitude": max_lon
        }
    }

def create_temporal_filter(start_date, end_date):
    """
    创建时间过滤器
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        
    Returns:
        dict: 时间过滤器
    """
    return {
        "startDate": start_date,
        "endDate": end_date
    }

def main():
    """
    主函数
    """
    print("=== USGS Earth Explorer 数据下载器 ===")
    
    # 定义要下载的区域
    regions = {
        "Donbas_Region": {"center_lon": 37.8, "center_lat": 48.0},
        "Gulf_War_Kuwait": {"center_lon": 47.5, "center_lat": 29.5},
        "Kashmir_Region": {"center_lon": 76.5, "center_lat": 34.0},
        "Scottish_Highlands": {"center_lon": -4.5, "center_lat": 57.0},
        "Israel_Palestine": {"center_lon": 35.0, "center_lat": 31.5}
    }
    
    # 设置下载参数
    size_km = 200  # 200km x 200km
    start_date = '2023-01-01'
    end_date = '2023-12-31'
    
    # 创建下载器实例
    # 注意：您需要注册 USGS 账户并获取用户名和密码
    # 注册地址：https://ers.cr.usgs.gov/register/
    downloader = USGSDownloader()
    
    print("=" * 60)
    print("注意：使用此脚本前，请先注册 USGS 账户：")
    print("https://ers.cr.usgs.gov/register/")
    print("然后更新脚本中的用户名和密码")
    print("=" * 60)
    
    # 登录
    if not downloader.login():
        print("登录失败，无法继续")
        return
    
    # 为每个区域搜索和下载数据
    for region_name, coords in regions.items():
        print(f"\n处理区域: {region_name}")
        print(f"中心坐标: ({coords['center_lon']}, {coords['center_lat']})")
        
        # 创建过滤器
        spatial_filter = create_spatial_filter(
            coords['center_lon'], 
            coords['center_lat'], 
            size_km
        )
        
        temporal_filter = create_temporal_filter(start_date, end_date)
        
        # 搜索场景
        scenes = downloader.search_scenes(
            dataset_name="LANDSAT_TM_C1",
            spatial_filter=spatial_filter,
            temporal_filter=temporal_filter,
            additional_criteria={
                "maxCloudCover": 20,
                "maxResults": 10
            }
        )
        
        if scenes:
            print(f"找到 {len(scenes)} 个场景")
            
            # 选择第一个场景进行下载
            scene = scenes[0]
            scene_id = scene.get("entityId")
            
            if scene_id:
                print(f"选择场景: {scene_id}")
                
                # 下载场景
                output_path = downloader.output_dir / f"{region_name}_{scene_id}.zip"
                success = downloader.download_scene(scene_id, output_path)
                
                if success:
                    print(f"{region_name} 下载完成: {output_path}")
                else:
                    print(f"{region_name} 下载失败")
            else:
                print(f"{region_name} 场景ID无效")
        else:
            print(f"{region_name} 没有找到符合条件的数据")
    
    print("\n所有区域处理完成！")
    print(f"下载的文件保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main() 