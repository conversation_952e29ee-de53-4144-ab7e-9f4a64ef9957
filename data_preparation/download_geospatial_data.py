import ee
import geemap
import os

# 定义服务账号密钥文件路径
SERVICE_ACCOUNT_KEY_PATH = '/home/<USER>/下载/just-landing-468209-u0-d7f8ef6de834.json'

# 服务账号的邮箱地址，可以在Google Cloud Console的服务账号详情中找到
# 例如: <EMAIL>
SERVICE_ACCOUNT_EMAIL = '<EMAIL>'

def download_data_for_region(region_name, bbox, output_dir):
    """
    下载指定区域的DEM和LULC数据。

    Args:
        region_name (str): 区域名称，用于创建输出目录。
        bbox (list): 区域的边界框 [min_lon, min_lat, max_lon, max_lat]。
        output_dir (str): 数据的根输出目录。
    """
    print(f"开始下载 {region_name} 的地理空间数据...")

    # 使用服务账号密钥初始化Earth Engine
    try:
        # 如果已经初始化过，ee.Initialize() 会检查并避免重复初始化
        ee.Initialize(ee.ServiceAccountCredentials(
            SERVICE_ACCOUNT_EMAIL,
            key_file=SERVICE_ACCOUNT_KEY_PATH
        ))
        print("Earth Engine 已通过服务账号成功初始化。")
    except Exception as e:
        print(f"Earth Engine 初始化失败: {e}")
        print("请检查服务账号密钥文件路径和权限。")
        return # 初始化失败，退出函数

    # 创建区域的几何对象
    region = ee.Geometry.BBox(bbox[0], bbox[1], bbox[2], bbox[3])

    # 定义输出路径
    region_output_dir = os.path.join(output_dir, region_name)
    os.makedirs(region_output_dir, exist_ok=True)

    # 下载DEM数据
    print(f"下载 {region_name} 的DEM数据...")
    dem = ee.Image('USGS/SRTMGL1_003')
    dem_output_path = os.path.join(region_output_dir, 'temp_dem_mosaic.tif')
    try:
        geemap.download_ee_image(dem, dem_output_path, scale=30, region=region)
        print(f"DEM数据已下载到: {dem_output_path}")
    except Exception as e:
        print(f"下载 {region_name} 的DEM数据失败: {e}")

    # 下载LULC数据 (ESA WorldCover)
    print(f"下载 {region_name} 的LULC数据 (ESA WorldCover)...")
    # 选择最新年份的WorldCover数据
    lulc = ee.ImageCollection("ESA/WorldCover/v100").first()
    lulc_output_path = os.path.join(region_output_dir, 'lulc.tif')
    try:
        geemap.download_ee_image(lulc, lulc_output_path, scale=10, region=region)
        print(f"LULC数据已下载到: {lulc_output_path}")
    except Exception as e:
        print(f"下载 {region_name} 的LULC数据失败: {e}")

if __name__ == '__main__':
    base_data_dir = 'data'

    # 海湾战争区域(科威特)
    kuwait_bbox = [45.0, 28.0, 49.5, 30.5]
    download_data_for_region("海湾战争区域(科威特)", kuwait_bbox, base_data_dir)

    # 顿巴斯地区
    donbas_bbox = [35.0, 46.0, 40.0, 50.0]
    download_data_for_region("顿巴斯地区", donbas_bbox, base_data_dir) 