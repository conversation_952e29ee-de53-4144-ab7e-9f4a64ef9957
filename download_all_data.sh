#!/bin/bash

# This script provides a robust way to download all necessary data
# by repeatedly calling the Python download script until it succeeds.
# The Python script is designed to skip already downloaded files,
# so this acts as a resilient "retry" mechanism for unstable connections.

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PYTHON_SCRIPT_PATH="$SCRIPT_DIR/Pure_pursuit_and_Randoemforest/src/download_raw_data.py"

echo "Starting resilient download process..."
echo "If the script appears to fail, it will automatically restart in 5 seconds."
echo "This will continue until all files are successfully downloaded."
echo "-----------------------------------------------------------------------"

# Loop until the python script exits with a success code (0)
while ! python3 "$PYTHON_SCRIPT_PATH"; do
    echo "-----------------------------------------------------------------------"
    echo "Download script failed. Retrying in 5 seconds... (Press Ctrl+C to cancel)"
    echo "-----------------------------------------------------------------------"
    sleep 5
done

echo "-----------------------------------------------------------------------"
echo "Success! The download script completed without errors."
echo "All data should now be available in the 'data' directory."
echo "-----------------------------------------------------------------------" 

# This script provides a robust way to download all necessary data
# by repeatedly calling the Python download script until it succeeds.
# The Python script is designed to skip already downloaded files,
# so this acts as a resilient "retry" mechanism for unstable connections.

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PYTHON_SCRIPT_PATH="$SCRIPT_DIR/Pure_pursuit_and_Randoemforest/src/download_raw_data.py"

echo "Starting resilient download process..."
echo "If the script appears to fail, it will automatically restart in 5 seconds."
echo "This will continue until all files are successfully downloaded."
echo "-----------------------------------------------------------------------"

# Loop until the python script exits with a success code (0)
while ! python3 "$PYTHON_SCRIPT_PATH"; do
    echo "-----------------------------------------------------------------------"
    echo "Download script failed. Retrying in 5 seconds... (Press Ctrl+C to cancel)"
    echo "-----------------------------------------------------------------------"
    sleep 5
done

echo "-----------------------------------------------------------------------"
echo "Success! The download script completed without errors."
echo "All data should now be available in the 'data' directory."
echo "-----------------------------------------------------------------------" 

# This script provides a robust way to download all necessary data
# by repeatedly calling the Python download script until it succeeds.
# The Python script is designed to skip already downloaded files,
# so this acts as a resilient "retry" mechanism for unstable connections.

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PYTHON_SCRIPT_PATH="$SCRIPT_DIR/Pure_pursuit_and_Randoemforest/src/download_raw_data.py"

echo "Starting resilient download process..."
echo "If the script appears to fail, it will automatically restart in 5 seconds."
echo "This will continue until all files are successfully downloaded."
echo "-----------------------------------------------------------------------"

# Loop until the python script exits with a success code (0)
while ! python3 "$PYTHON_SCRIPT_PATH"; do
    echo "-----------------------------------------------------------------------"
    echo "Download script failed. Retrying in 5 seconds... (Press Ctrl+C to cancel)"
    echo "-----------------------------------------------------------------------"
    sleep 5
done

echo "-----------------------------------------------------------------------"
echo "Success! The download script completed without errors."
echo "All data should now be available in the 'data' directory."
echo "-----------------------------------------------------------------------" 

# This script provides a robust way to download all necessary data
# by repeatedly calling the Python download script until it succeeds.
# The Python script is designed to skip already downloaded files,
# so this acts as a resilient "retry" mechanism for unstable connections.

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PYTHON_SCRIPT_PATH="$SCRIPT_DIR/Pure_pursuit_and_Randoemforest/src/download_raw_data.py"

echo "Starting resilient download process..."
echo "If the script appears to fail, it will automatically restart in 5 seconds."
echo "This will continue until all files are successfully downloaded."
echo "-----------------------------------------------------------------------"

# Loop until the python script exits with a success code (0)
while ! python3 "$PYTHON_SCRIPT_PATH"; do
    echo "-----------------------------------------------------------------------"
    echo "Download script failed. Retrying in 5 seconds... (Press Ctrl+C to cancel)"
    echo "-----------------------------------------------------------------------"
    sleep 5
done

echo "-----------------------------------------------------------------------"
echo "Success! The download script completed without errors."
echo "All data should now be available in the 'data' directory."
echo "-----------------------------------------------------------------------" 
 
 
 