#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据准备与预处理模块
实现环境地图构建和轨迹特征提取，为深度学习模型准备训练数据

核心功能：
1. 精细的环境地图构建和查询
2. 轨迹数据特征提取与标注
3. 控制输入的反向推断
"""

import os
import numpy as np
import pandas as pd
import rasterio
from rasterio.warp import reproject, Resampling
from scipy.interpolate import interp1d
from scipy.ndimage import gaussian_filter1d
import math
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import joblib

class HighResolutionEnvironmentMaps:
    """环境地图类"""
    
    def __init__(self, env_data_dir: str, use_original_resolution: bool = True):
        """
        初始化环境地图
        
        Args:
            env_data_dir: 环境数据目录
            use_original_resolution: 是否使用原始分辨率（推荐True）
        """
        self.env_data_dir = env_data_dir
        self.use_original_resolution = use_original_resolution
        self.maps = {}
        
        print(f"初始化环境地图，使用原始分辨率: {use_original_resolution}")
        self._load_and_align_maps()
        
    def _load_and_align_maps(self):
        """加载并对齐所有环境地图"""
        
        # 定义数据文件
        data_files = {
            'dem': 'dem_aligned.tif',
            'slope': 'slope_aligned.tif', 
            'aspect': 'aspect_aligned.tif',
            'landcover': 'landcover_aligned.tif'
        }
        
        # 加载参考数据集（DEM）
        dem_path = os.path.join(self.env_data_dir, data_files['dem'])
        with rasterio.open(dem_path) as dem_ds:
            self.reference_transform = dem_ds.transform
            self.reference_crs = dem_ds.crs
            self.bounds = dem_ds.bounds
            
            if self.use_original_resolution:
                # 使用原始分辨率，直接读取数据
                self.target_transform = dem_ds.transform
                width, height = dem_ds.width, dem_ds.height
                self.resolution = abs(dem_ds.transform[0])  # 获取实际分辨率
                print(f"使用原始分辨率: {self.resolution:.1f}m，尺寸: {width}x{height}")
            else:
                # 如果需要重采样（不推荐）
                width = int((self.bounds.right - self.bounds.left) / 30.0)
                height = int((self.bounds.top - self.bounds.bottom) / 30.0)
                self.target_transform = rasterio.transform.from_bounds(
                    self.bounds.left, self.bounds.bottom, 
                    self.bounds.right, self.bounds.top,
                    width, height
                )
                self.resolution = 30.0
                print(f"重采样到尺寸: {width}x{height}")
            
        # 加载所有数据层
        for name, filename in data_files.items():
            filepath = os.path.join(self.env_data_dir, filename)
            if os.path.exists(filepath):
                if self.use_original_resolution:
                    self.maps[name] = self._load_original_layer(filepath)
                else:
                    self.maps[name] = self._resample_layer(filepath, width, height)
                print(f"加载 {name}: {self.maps[name].shape}")
            else:
                print(f"警告: 未找到 {filename}")
                
        # 计算坡度大小和方向（如果只有坡度数据）
        if 'slope' in self.maps and 'aspect' not in self.maps:
            self._calculate_slope_aspects()
            
    def _load_original_layer(self, filepath: str) -> np.ndarray:
        """直接加载原始数据层"""
        with rasterio.open(filepath) as src:
            return src.read(1)
            
    def _resample_layer(self, filepath: str, width: int, height: int) -> np.ndarray:
        """重采样单个数据层"""
        with rasterio.open(filepath) as src:
            # 创建目标数组
            dst_array = np.empty((height, width), dtype=src.dtypes[0])
            
            # 重采样
            reproject(
                source=rasterio.band(src, 1),
                destination=dst_array,
                src_transform=src.transform,
                src_crs=src.crs,
                dst_transform=self.target_transform,
                dst_crs=self.reference_crs,
                resampling=Resampling.bilinear
            )
            
            return dst_array
            
    def _calculate_slope_aspects(self):
        """从DEM计算坡度和坡向"""
        if 'dem' not in self.maps:
            return
            
        dem = self.maps['dem']
        dy, dx = np.gradient(dem, self.resolution)
        
        # 计算坡度（度）
        slope = np.arctan(np.sqrt(dx**2 + dy**2)) * 180 / np.pi
        
        # 计算坡向（度，从北向顺时针）
        aspect = np.arctan2(-dx, dy) * 180 / np.pi
        aspect = (aspect + 360) % 360
        
        self.maps['slope'] = slope
        self.maps['aspect'] = aspect
        
    def query_features(self, x: float, y: float) -> Dict[str, float]:
        """
        查询指定UTM坐标的环境特征
        
        Args:
            x, y: UTM坐标
            
        Returns:
            环境特征字典
        """
        # 转换为栅格坐标
        col, row = ~self.target_transform * (x, y)
        col, row = int(round(col)), int(round(row))
        
        # 检查边界
        height, width = self.maps['dem'].shape
        if not (0 <= row < height and 0 <= col < width):
            return self._get_default_features()
            
        features = {}
        
        # 提取所有可用特征
        for name, data in self.maps.items():
            features[name] = float(data[row, col])
            
        # 计算局部坡度（如果有DEM）
        if 'dem' in self.maps:
            local_slope_mag, local_slope_dir = self._calculate_local_slope(row, col)
            features['local_slope_magnitude'] = local_slope_mag
            features['local_slope_direction'] = local_slope_dir
            
        # 地表类型独热编码
        if 'landcover' in features:
            landcover_onehot = self._encode_landcover(int(features['landcover']))
            features.update(landcover_onehot)
            
        return features
        
    def _calculate_local_slope(self, row: int, col: int, window: int = 3) -> Tuple[float, float]:
        """计算局部坡度（3x3窗口）"""
        dem = self.maps['dem']
        height, width = dem.shape
        
        # 定义窗口范围
        r_start = max(0, row - window // 2)
        r_end = min(height, row + window // 2 + 1)
        c_start = max(0, col - window // 2)
        c_end = min(width, col + window // 2 + 1)
        
        # 提取局部DEM
        local_dem = dem[r_start:r_end, c_start:c_end]
        
        # 计算梯度
        dy, dx = np.gradient(local_dem, self.resolution)
        
        # 中心点的梯度
        center_r = row - r_start
        center_c = col - c_start
        
        if center_r < dy.shape[0] and center_c < dy.shape[1]:
            dx_val = dx[center_r, center_c]
            dy_val = dy[center_r, center_c]
            
            # 坡度大小（度）
            slope_mag = np.arctan(np.sqrt(dx_val**2 + dy_val**2)) * 180 / np.pi
            
            # 坡度方向（度）
            slope_dir = np.arctan2(-dx_val, dy_val) * 180 / np.pi
            slope_dir = (slope_dir + 360) % 360
            
            return slope_mag, slope_dir
        else:
            return 0.0, 0.0
            
    def _encode_landcover(self, landcover_code: int) -> Dict[str, float]:
        """地表类型独热编码"""
        # 定义地表类型
        landcover_types = {
            10: 'water',      # 水域
            20: 'wetland',    # 湿地  
            30: 'grassland',  # 草地
            40: 'shrubland',  # 灌木地
            50: 'built',      # 建筑用地
            60: 'cropland',   # 农田
            80: 'forest',     # 森林
            90: 'barren',     # 荒地
            255: 'unclassified'  # 未分类
        }
        
        # 创建独热编码
        onehot = {}
        for code, name in landcover_types.items():
            onehot[f'landcover_{name}'] = 1.0 if landcover_code == code else 0.0
            
        return onehot
        
    def _get_default_features(self) -> Dict[str, float]:
        """返回默认特征值"""
        features = {
            'dem': 0.0,
            'slope': 0.0,
            'aspect': 0.0,
            'landcover': 255.0,
            'local_slope_magnitude': 0.0,
            'local_slope_direction': 0.0
        }
        
        # 添加地表类型独热编码
        landcover_onehot = self._encode_landcover(255)
        features.update(landcover_onehot)
        
        return features

class TrajectoryFeatureExtractor:
    """轨迹特征提取器"""
    
    def __init__(self, env_maps: HighResolutionEnvironmentMaps, 
                 time_step: float = 0.25):
        """
        初始化轨迹特征提取器
        
        Args:
            env_maps: 环境地图对象
            time_step: 目标时间步长（秒），OORD数据是4Hz，所以默认0.25秒
        """
        self.env_maps = env_maps
        self.time_step = time_step
        
    def process_trajectory(self, trajectory_df: pd.DataFrame) -> pd.DataFrame:
        """
        处理单条轨迹，提取所有需要的特征
        
        Args:
            trajectory_df: 原始轨迹数据
            
        Returns:
            标准化的时序特征序列
        """
        print(f"处理轨迹，原始点数: {len(trajectory_df)}")
        
        # 1. 时间标准化和插值
        standardized_traj = self._standardize_time_sequence(trajectory_df)
        
        # 2. 计算运动学特征
        standardized_traj = self._calculate_kinematics(standardized_traj)
        
        # 3. 添加环境特征
        standardized_traj = self._add_environment_features(standardized_traj)
        
        # 4. 推断控制输入
        standardized_traj = self._infer_control_inputs(standardized_traj)
        
        # 5. 计算路径曲率
        standardized_traj = self._calculate_path_curvature(standardized_traj)
        
        print(f"处理完成，标准化点数: {len(standardized_traj)}")
        return standardized_traj
        
    def _standardize_time_sequence(self, df: pd.DataFrame) -> pd.DataFrame:
        """时间序列标准化和插值"""
        
        # 转换时间戳
        if 'timestamp_ms' in df.columns:
            time_col = df['timestamp_ms'] / 1000.0  # 转换为秒
        else:
            # 如果没有时间戳，假设均匀间隔
            time_col = np.arange(len(df)) * 1.0
            
        # 检查是否需要坐标转换
        if 'latitude' in df.columns and 'longitude' in df.columns:
            # 从经纬度转换为UTM（这里需要根据实际情况调整）
            x_coords = df['longitude'].values  # 假设已经是UTM
            y_coords = df['latitude'].values
        else:
            x_coords = df['x'].values
            y_coords = df['y'].values
            
        # 创建均匀时间网格
        t_start = time_col.iloc[0]
        t_end = time_col.iloc[-1]
        t_uniform = np.arange(t_start, t_end + self.time_step, self.time_step)
        
        # 插值位置
        f_x = interp1d(time_col, x_coords, kind='linear', 
                       bounds_error=False, fill_value='extrapolate')
        f_y = interp1d(time_col, y_coords, kind='linear', 
                       bounds_error=False, fill_value='extrapolate')
        
        x_interp = f_x(t_uniform)
        y_interp = f_y(t_uniform)
        
        # 创建标准化数据框
        result_df = pd.DataFrame({
            'timestamp': t_uniform,
            'x': x_interp,
            'y': y_interp
        })
        
        return result_df
        
    def _calculate_kinematics(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算运动学特征"""
        
        dt = self.time_step
        
        # 计算速度（数值微分）
        vx = np.gradient(df['x'].values, dt)
        vy = np.gradient(df['y'].values, dt)
        
        # 平滑速度
        vx = gaussian_filter1d(vx, sigma=1.0)
        vy = gaussian_filter1d(vy, sigma=1.0)
        
        # 速度大小和方向
        speed = np.sqrt(vx**2 + vy**2)
        heading = np.arctan2(vy, vx) * 180 / np.pi  # 度
        heading = (heading + 360) % 360
        
        # 计算加速度
        ax = np.gradient(vx, dt)
        ay = np.gradient(vy, dt)
        
        # 平滑加速度
        ax = gaussian_filter1d(ax, sigma=1.0)
        ay = gaussian_filter1d(ay, sigma=1.0)
        
        # 纵向和侧向加速度
        heading_rad = heading * np.pi / 180
        cos_h = np.cos(heading_rad)
        sin_h = np.sin(heading_rad)
        
        a_long = ax * cos_h + ay * sin_h  # 纵向加速度
        a_lat = -ax * sin_h + ay * cos_h  # 侧向加速度
        
        # 添加到数据框
        df = df.copy()
        df['velocity_x'] = vx
        df['velocity_y'] = vy
        df['speed'] = speed
        df['heading'] = heading
        df['acceleration_x'] = ax
        df['acceleration_y'] = ay
        df['acceleration_longitudinal'] = a_long
        df['acceleration_lateral'] = a_lat
        
        return df
        
    def _add_environment_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加环境特征"""
        
        env_features = []
        
        for _, row in df.iterrows():
            features = self.env_maps.query_features(row['x'], row['y'])
            env_features.append(features)
            
        # 转换为数据框并合并
        env_df = pd.DataFrame(env_features)
        result_df = pd.concat([df.reset_index(drop=True), 
                              env_df.reset_index(drop=True)], axis=1)
        
        return result_df
        
    def _infer_control_inputs(self, df: pd.DataFrame) -> pd.DataFrame:
        """推断控制输入"""
        
        # 纵向控制：油门/制动（基于纵向加速度）
        throttle_brake = df['acceleration_longitudinal'].values
        
        # 转向控制：基于侧向加速度和速度计算等效转向角
        # 使用自行车模型：a_lat = v^2 * tan(delta) / L
        # 其中 L 是轴距，假设为2.7m
        wheelbase = 2.7
        
        steering_angle = np.zeros_like(df['speed'].values)
        for i, (speed, a_lat) in enumerate(zip(df['speed'].values, 
                                             df['acceleration_lateral'].values)):
            if speed > 1.0:  # 避免除零
                # delta = arctan(a_lat * L / v^2)
                delta = np.arctan2(a_lat * wheelbase, speed**2) * 180 / np.pi
                steering_angle[i] = np.clip(delta, -30, 30)  # 限制在合理范围
                
        df = df.copy()
        df['control_throttle_brake'] = throttle_brake
        df['control_steering_angle'] = steering_angle
        
        return df
        
    def _calculate_path_curvature(self, df: pd.DataFrame, window_size: int = 5) -> pd.DataFrame:
        """计算路径曲率"""
        
        x = df['x'].values
        y = df['y'].values
        
        # 计算曲率（使用数值方法）
        curvature = np.zeros_like(x)
        
        for i in range(len(x)):
            # 定义窗口
            start = max(0, i - window_size // 2)
            end = min(len(x), i + window_size // 2 + 1)
            
            if end - start >= 3:  # 至少需要3个点
                x_window = x[start:end]
                y_window = y[start:end]
                
                # 计算一阶和二阶导数
                dx = np.gradient(x_window)
                dy = np.gradient(y_window)
                d2x = np.gradient(dx)
                d2y = np.gradient(dy)
                
                # 曲率公式：κ = |x'y'' - y'x''| / (x'^2 + y'^2)^(3/2)
                center_idx = i - start
                if center_idx < len(dx):
                    numerator = abs(dx[center_idx] * d2y[center_idx] - 
                                  dy[center_idx] * d2x[center_idx])
                    denominator = (dx[center_idx]**2 + dy[center_idx]**2)**(3/2)
                    
                    if denominator > 1e-6:
                        curvature[i] = numerator / denominator
                        
        df = df.copy()
        df['path_curvature'] = curvature
        
        return df

def batch_process_trajectories(trajectory_files: List[str], 
                             env_data_dir: str,
                             output_dir: str,
                             use_original_resolution: bool = True,
                             time_step: float = 0.25) -> None:
    """
    批量处理轨迹文件
    
    Args:
        trajectory_files: 轨迹文件列表
        env_data_dir: 环境数据目录
        output_dir: 输出目录
        use_original_resolution: 是否使用原始分辨率
        time_step: 时间步长
    """
    
    # 初始化环境地图和特征提取器
    print("初始化环境地图...")
    env_maps = HighResolutionEnvironmentMaps(env_data_dir, use_original_resolution)
    
    print("初始化特征提取器...")
    extractor = TrajectoryFeatureExtractor(env_maps, time_step)
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理每个轨迹文件
    for i, traj_file in enumerate(trajectory_files):
        print(f"\n处理轨迹 {i+1}/{len(trajectory_files)}: {traj_file}")
        
        try:
            # 读取轨迹数据
            df = pd.read_csv(traj_file)
            
            # 处理轨迹
            processed_df = extractor.process_trajectory(df)
            
            # 保存结果
            output_file = os.path.join(output_dir, f"processed_trajectory_{i:03d}.csv")
            processed_df.to_csv(output_file, index=False)
            
            print(f"保存到: {output_file}")
            
        except Exception as e:
            print(f"处理轨迹 {traj_file} 时出错: {e}")
            continue
            
    print(f"\n批量处理完成！共处理 {len(trajectory_files)} 个轨迹文件")

if __name__ == "__main__":
    # 示例使用
    env_data_dir = "trajectory_generation_module_pkg/examples/data/environment"
    trajectory_files = [
        "core_trajectories/converted_sequence_1_core.csv",
        "core_trajectories/converted_sequence_2_core.csv", 
        "core_trajectories/converted_sequence_3_core.csv"
    ]
    output_dir = "processed_trajectories"
    
    batch_process_trajectories(trajectory_files, env_data_dir, output_dir)