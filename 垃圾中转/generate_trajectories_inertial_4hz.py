#!/usr/bin/env python3
"""
4Hz采样的惯性模型 - 与真实OORD数据保持一致的采样频率
基于原始惯性模型，但采用250ms时间间隔以匹配真实数据
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import rasterio
from collections import deque
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class VehicleState4Hz:
    """4Hz采样的车辆状态类"""
    def __init__(self, mass=1800, power=150000, drag_coeff=0.35):
        self.mass = mass
        self.max_power = power
        self.drag_coefficient = drag_coeff
        
        # 速度历史记录（用于惯性计算）
        self.speed_history = deque(maxlen=20)  # 保持5秒历史（20个点 × 0.25秒）
        self.current_speed = 0.0
        self.last_acceleration = 0.0
        
    def update_speed(self, new_speed):
        """更新速度状态"""
        self.speed_history.append(self.current_speed)
        self.current_speed = new_speed
        
    def get_momentum_speed(self):
        """计算基于动量的预期速度"""
        if len(self.speed_history) < 4:  # 至少需要1秒的历史
            return self.current_speed
        
        # 使用最近2秒的数据计算趋势（8个点）
        recent_speeds = list(self.speed_history)[-8:]
        if len(recent_speeds) >= 4:
            # 线性回归计算趋势
            x = np.arange(len(recent_speeds))
            y = np.array(recent_speeds)
            slope = np.polyfit(x, y, 1)[0]  # 斜率
            
            # 预测下一个时间点的速度
            momentum_speed = self.current_speed + slope * 2  # 预测2步
            return max(0.1, momentum_speed)
        
        return self.current_speed

class TrajectoryGenerator4Hz:
    """4Hz采样的轨迹生成器"""
    def __init__(self):
        self.vehicle = VehicleState4Hz()
        
        # 速度参数（与原始模型保持一致）
        self.speed_models = {
            10: 8.0,   # 水域
            20: 12.0,  # 湿地
            30: 25.0,  # 草地
            40: 20.0,  # 灌木地
            50: 15.0,  # 建筑用地
            60: 22.0,  # 农田
            80: 18.0,  # 森林
            90: 20.0,  # 荒地
            255: 20.0  # 未分类
        }
        
        # 坡度影响因子
        self.slope_factors = {
            'flat': 1.0,
            'gentle': 0.9,
            'moderate': 0.75,
            'steep': 0.6
        }
        
        # 物理约束参数（针对250ms时间步长调整）
        self.max_acceleration = 2.5   # m/s²
        self.max_deceleration = 4.0   # m/s²
        self.startup_acceleration = 1.5  # m/s²
        
        # 动态权重参数
        self.base_inertia_weight = 0.4
        self.base_env_weight = 0.6
        
    def load_environment_data(self, dem_path, landcover_path, aspect_path=None):
        """加载环境数据"""
        print("加载环境数据...")
        
        with rasterio.open(dem_path) as dem_src:
            self.dem = dem_src.read(1)
            self.dem_transform = dem_src.transform
            self.dem_bounds = dem_src.bounds
            
        with rasterio.open(landcover_path) as lc_src:
            self.landcover = lc_src.read(1)
            
        if aspect_path and Path(aspect_path).exists():
            with rasterio.open(aspect_path) as aspect_src:
                self.aspect = aspect_src.read(1)
        else:
            self.aspect = None
            
        print(f"环境数据加载完成，DEM形状: {self.dem.shape}")
    
    def get_environment_at_point(self, x, y):
        """获取指定点的环境信息"""
        try:
            col = int((x - self.dem_bounds.left) / abs(self.dem_transform.a))
            row = int((self.dem_bounds.top - y) / abs(self.dem_transform.e))
            
            if 0 <= row < self.dem.shape[0] and 0 <= col < self.dem.shape[1]:
                elevation = self.dem[row, col]
                landcover = self.landcover[row, col]
                aspect = self.aspect[row, col] if self.aspect is not None else 0
                return elevation, landcover, aspect
            else:
                return 0, 30, 0
        except:
            return 0, 30, 0
    
    def calculate_slope_factor(self, current_elev, next_elev, distance):
        """计算坡度影响因子"""
        if distance <= 0:
            return 1.0
            
        slope_angle = np.arctan(abs(next_elev - current_elev) / distance)
        slope_deg = np.degrees(slope_angle)
        
        if slope_deg < 5:
            return self.slope_factors['flat']
        elif slope_deg < 15:
            return self.slope_factors['gentle']
        elif slope_deg < 30:
            return self.slope_factors['moderate']
        else:
            return self.slope_factors['steep']
    
    def calculate_speed(self, landcover, slope_factor, prev_speed, is_startup=False):
        """计算当前点的速度"""
        # 获取基础环境速度
        base_speed = self.speed_models.get(landcover, 20.0)
        env_speed = base_speed * slope_factor
        
        # 获取动量速度
        momentum_speed = self.vehicle.get_momentum_speed()
        
        # 计算动态权重（使用sigmoid函数）
        speed_diff = abs(env_speed - prev_speed)
        sigmoid_factor = 1 / (1 + np.exp(-2 * (speed_diff - 3)))  # 中心点在3 m/s
        
        # 根据速度差异调整权重
        env_weight = self.base_env_weight + sigmoid_factor * 0.3
        inertia_weight = 1 - env_weight
        
        # 计算目标速度
        target_speed = env_speed * env_weight + momentum_speed * inertia_weight
        
        # 应用物理约束（考虑250ms时间步长）
        dt = 0.25  # 250ms
        if is_startup and prev_speed < 2.0:
            max_accel = self.startup_acceleration
        else:
            max_accel = self.max_acceleration
        
        # 限制加速度
        if target_speed > prev_speed:
            final_speed = min(target_speed, prev_speed + max_accel * dt)
        else:
            final_speed = max(target_speed, prev_speed - self.max_deceleration * dt)
        
        # 确保速度在合理范围内
        final_speed = max(0.1, min(final_speed, 35.0))
        
        return final_speed
    
    def generate_trajectory(self, path_points, goal_id=0, time_step_ms=250):
        """生成4Hz采样的轨迹"""
        print(f"开始生成4Hz轨迹，共{len(path_points)}个路径点，时间步长: {time_step_ms}ms")
        
        trajectory_data = []
        self.vehicle = VehicleState4Hz()  # 重置车辆状态
        
        prev_speed = 0.1  # 初始速度
        
        for i, (x, y) in enumerate(path_points):
            # 获取环境信息
            elevation, landcover, aspect = self.get_environment_at_point(x, y)
            
            # 计算坡度因子
            if i > 0:
                prev_x, prev_y = path_points[i-1]
                prev_elev, _, _ = self.get_environment_at_point(prev_x, prev_y)
                distance = np.sqrt((x - prev_x)**2 + (y - prev_y)**2)
                slope_factor = self.calculate_slope_factor(prev_elev, elevation, distance)
            else:
                slope_factor = 1.0
            
            # 计算速度
            is_startup = i < 20  # 前20个点（5秒）为启动阶段
            current_speed = self.calculate_speed(landcover, slope_factor, prev_speed, is_startup)
            
            # 更新车辆状态
            self.vehicle.update_speed(current_speed)
            
            # 计算方向角
            if i > 0:
                dx = x - path_points[i-1][0]
                dy = y - path_points[i-1][1]
                heading = np.degrees(np.arctan2(dy, dx))
            else:
                heading = 0.0
            
            # 计算速度分量
            velocity_north = current_speed * np.sin(np.radians(heading))
            velocity_east = current_speed * np.cos(np.radians(heading))
            
            # 计算加速度
            acceleration = (current_speed - prev_speed) / (time_step_ms / 1000.0)
            
            # 记录轨迹点
            trajectory_data.append({
                'timestamp_ms': i * time_step_ms,
                'x': x,
                'y': y,
                'velocity_north_ms': velocity_north,
                'velocity_east_ms': velocity_east,
                'heading_deg': heading,
                'acceleration_x_ms2': acceleration * np.cos(np.radians(heading)),
                'acceleration_y_ms2': acceleration * np.sin(np.radians(heading)),
                'goal_id': goal_id,
                'landcover': landcover,
                'elevation': elevation,
                'speed_ms': current_speed
            })
            
            prev_speed = current_speed
        
        df = pd.DataFrame(trajectory_data)
        avg_speed = df['speed_ms'].mean()
        duration = len(df) * time_step_ms / 1000.0
        print(f"轨迹生成完成，平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.1f} km/h)")
        print(f"轨迹时长: {duration:.1f}秒，数据点: {len(df)}个，采样频率: {len(df)/duration:.1f}Hz")
        
        return df
    
    def save_trajectory(self, df, output_path):
        """保存轨迹数据"""
        df.to_csv(output_path, index=False)
        print(f"轨迹数据已保存到: {output_path}")
        
    def plot_speed_analysis(self, df, output_path):
        """绘制速度分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('4Hz惯性模型速度分析', fontsize=20, fontweight='bold')
        
        # 速度时间序列
        ax1 = axes[0, 0]
        ax1.plot(df['timestamp_ms']/1000, df['speed_ms'], 'b-', alpha=0.7, linewidth=1)
        ax1.set_title('速度时间序列', fontsize=18)
        ax1.set_xlabel('时间 (秒)', fontsize=16)
        ax1.set_ylabel('速度 (m/s)', fontsize=16)
        ax1.grid(True, alpha=0.3)
        
        # 速度分布
        ax2 = axes[0, 1]
        ax2.hist(df['speed_ms'], bins=30, alpha=0.7, color='green')
        ax2.set_title('速度分布', fontsize=18)
        ax2.set_xlabel('速度 (m/s)', fontsize=16)
        ax2.set_ylabel('频次', fontsize=16)
        ax2.grid(True, alpha=0.3)
        
        # 加速度分析
        ax3 = axes[1, 0]
        acceleration = df['speed_ms'].diff() / (df['timestamp_ms'].diff() / 1000.0)
        ax3.plot(df['timestamp_ms'][1:]/1000, acceleration[1:], 'r-', alpha=0.7, linewidth=1)
        ax3.set_title('加速度时间序列', fontsize=18)
        ax3.set_xlabel('时间 (秒)', fontsize=16)
        ax3.set_ylabel('加速度 (m/s²)', fontsize=16)
        ax3.grid(True, alpha=0.3)
        
        # 统计信息
        ax4 = axes[1, 1]
        ax4.axis('off')
        duration = len(df) * 0.25
        stats_text = f"""
        统计信息
        
        平均速度: {df['speed_ms'].mean():.2f} m/s
        标准差: {df['speed_ms'].std():.2f} m/s
        最大速度: {df['speed_ms'].max():.2f} m/s
        最小速度: {df['speed_ms'].min():.2f} m/s
        
        平均速度: {df['speed_ms'].mean()*3.6:.1f} km/h
        最大速度: {df['speed_ms'].max()*3.6:.1f} km/h
        
        数据点数: {len(df)}
        轨迹时长: {duration:.1f} 秒
        采样频率: {len(df)/duration:.1f} Hz
        """
        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, 
                fontsize=16, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"速度分析图已保存到: {output_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("运行4Hz惯性模型测试")
    print("=" * 60)
    
    generator = TrajectoryGenerator4Hz()
    
    # 加载环境数据
    dem_path = "trajectory_generator/data/environment/dem_aligned.tif"
    landcover_path = "trajectory_generator/data/environment/landcover_aligned.tif"
    aspect_path = "trajectory_generator/data/environment/aspect_aligned.tif"
    
    if not Path(dem_path).exists():
        print(f"错误: 找不到DEM文件 {dem_path}")
        return
    
    generator.load_environment_data(dem_path, landcover_path, aspect_path)
    
    # 加载测试路径
    path_file = "gen_exp/high_mobility/path_000_000_high_mobility.npy"
    if not Path(path_file).exists():
        print(f"错误: 找不到路径文件 {path_file}")
        return
    
    path_points = np.load(path_file)
    print(f"加载路径文件: {path_file}, 包含 {len(path_points)} 个点")
    
    # 生成轨迹
    trajectory_df = generator.generate_trajectory(path_points, goal_id=0, time_step_ms=250)
    
    # 保存结果
    output_dir = Path("inertial_4hz_output")
    output_dir.mkdir(exist_ok=True)
    
    trajectory_file = output_dir / "trajectory_4hz_inertial.csv"
    analysis_file = output_dir / "trajectory_4hz_inertial_analysis.png"
    
    generator.save_trajectory(trajectory_df, trajectory_file)
    generator.plot_speed_analysis(trajectory_df, analysis_file)
    
    print("\n4Hz惯性模型测试完成！")
    print(f"输出目录: {output_dir}")

if __name__ == "__main__":
    main() 