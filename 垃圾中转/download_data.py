import requests
import os
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed

# 之前定义的五个区域的中心点和大致范围
REGIONS = {
    "scottish_highlands": {"lat": 57.48, "lon": -4.22, "deg_radius_lat": 1.0, "deg_radius_lon": 1.8},
    "israel_palestine": {"lat": 31.77, "lon": 35.21, "deg_radius_lat": 1.0, "deg_radius_lon": 1.1},
    "kashmir": {"lat": 34.08, "lon": 74.80, "deg_radius_lat": 1.0, "deg_radius_lon": 1.1},
    "gulf_war_kuwait": {"lat": 29.38, "lon": 47.98, "deg_radius_lat": 1.0, "deg_radius_lon": 1.1},
    "donbas": {"lat": 48.02, "lon": 37.80, "deg_radius_lat": 1.0, "deg_radius_lon": 1.4},
}

BASE_URL = "https://www.viewfinderpanoramas.org/dem3"
OUTPUT_DIR = "data/DEM"

def get_tile_urls(region_info):
    """根据区域的经纬度范围计算需要下载的DEM瓦片URL列表"""
    urls = []
    lat_min = int(region_info["lat"] - region_info["deg_radius_lat"])
    lat_max = int(region_info["lat"] + region_info["deg_radius_lat"])
    lon_min = int(region_info["lon"] - region_info["deg_radius_lon"])
    lon_max = int(region_info["lon"] + region_info["deg_radius_lon"])

    for lat in range(lat_min, lat_max + 1):
        for lon in range(lon_min, lon_max + 1):
            if lat >= 0:
                lat_str = f"N{lat:02d}"
            else:
                lat_str = f"S{-lat:02d}"
            
            if lon >= 0:
                lon_str = f"E{lon:03d}"
            else:
                lon_str = f"W{-lon:03d}"

            # 文件名格式如 N57W005.hgt
            # 网站上的压缩包名是 N57W005.zip
            zip_filename = f"{lat_str}{lon_str}.zip"
            
            # 构造URL，例如 https://www.viewfinderpanoramas.org/dem3/N57.zip
            # 根据网站结构，所有相同纬度的文件打包在一个zip里
            url = f"{BASE_URL}/{lat_str}.zip"
            if url not in urls:
                urls.append(url)
    return urls

def download_file(url, target_dir):
    """下载单个文件并显示进度条"""
    local_filename = url.split('/')[-1]
    local_filepath = os.path.join(target_dir, local_filename)

    if os.path.exists(local_filepath):
        print(f"文件 {local_filename} 已存在，跳过下载。")
        return local_filepath, True

    try:
        with requests.get(url, stream=True) as r:
            r.raise_for_status()
            total_size = int(r.headers.get('content-length', 0))
            
            with open(local_filepath, 'wb') as f, tqdm(
                desc=local_filename,
                total=total_size,
                unit='iB',
                unit_scale=True,
                unit_divisor=1024,
            ) as bar:
                for chunk in r.iter_content(chunk_size=8192):
                    size = f.write(chunk)
                    bar.update(size)
        return local_filepath, True
    except requests.exceptions.RequestException as e:
        print(f"下载 {url} 失败: {e}")
        if os.path.exists(local_filepath):
             os.remove(local_filepath) #移除不完整的文件
        return local_filepath, False


def main():
    """主函数，执行DEM数据下载"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    all_urls = []
    for region_name, region_info in REGIONS.items():
        region_dir = os.path.join(OUTPUT_DIR, region_name)
        os.makedirs(region_dir, exist_ok=True)
        urls = get_tile_urls(region_info)
        all_urls.extend(urls)

    # 去重
    unique_urls = sorted(list(set(all_urls)))
    
    print(f"总共需要下载 {len(unique_urls)} 个压缩文件。")

    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = {executor.submit(download_file, url, OUTPUT_DIR): url for url in unique_urls}
        
        for future in as_completed(futures):
            url = futures[future]
            try:
                filepath, success = future.result()
                if success:
                    print(f"成功下载: {filepath}")
                else:
                    print(f"下载失败: {url}")
            except Exception as exc:
                print(f"处理 {url} 时产生异常: {exc}")


if __name__ == "__main__":
    main() 