import os
import math
import glob
import rasterio
from rasterio.warp import transform_bounds
from shapely.geometry import box, Polygon

def get_available_lulc_coverage(lulc_files):
    """
    计算所有可用LULC文件的总覆盖范围。
    返回一个 Shapely Polygon 对象代表总覆盖区域。
    """
    total_coverage = Polygon()
    if not lulc_files:
        return total_coverage

    print("正在计算可用的LULC数据总覆盖范围...")
    for file_path in lulc_files:
        try:
            with rasterio.open(file_path) as src:
                lonlat_bounds = transform_bounds(src.crs, 'EPSG:4326', *src.bounds)
                tile_box = box(lonlat_bounds[0], lonlat_bounds[1], lonlat_bounds[2], lonlat_bounds[3])
                total_coverage = total_coverage.union(tile_box)
        except Exception as e:
            print(f"读取LULC文件 {os.path.basename(file_path)} 时出错: {e}")
    
    return total_coverage

def get_available_dem_lat_bands(dem_dir):
    """
    获取所有已下载的DEM文件的纬度带。
    返回一个整数集合，代表可用的纬度。
    """
    available_bands = set()
    zip_files = glob.glob(os.path.join(dem_dir, "N*.zip"))
    for zip_path in zip_files:
        filename = os.path.basename(zip_path)
        # 确认文件是完整的 (大小 > 0)
        if os.path.getsize(zip_path) > 1024: # 大于1KB认为是有效文件
            try:
                lat_band = int(filename[1:3])
                available_bands.add(lat_band)
            except (ValueError, IndexError):
                continue
    return available_bands

def check_region_data_completeness(region_name, bbox, lulc_coverage, dem_bands):
    """
    检查单个区域的数据是否完整。
    bbox: (min_lon, min_lat, max_lon, max_lat)
    """
    print(f"\n--- 正在检查区域: {region_name} ---")
    region_box = box(bbox[0], bbox[1], bbox[2], bbox[3])

    # 1. 检查LULC覆盖
    is_lulc_covered = lulc_coverage.contains(region_box)
    if is_lulc_covered:
        print("LULC 数据: ✔  完全覆盖")
    else:
        print("LULC 数据: ❌  不完全覆盖或无覆盖")

    # 2. 检查DEM覆盖
    required_dem_bands = set(range(math.floor(bbox[1]), math.ceil(bbox[3])))
    missing_dem_bands = required_dem_bands - dem_bands
    
    if not missing_dem_bands:
        print(f"DEM 数据:  ✔  完全覆盖 (需要纬度带: {sorted(list(required_dem_bands))})")
        is_dem_covered = True
    else:
        print(f"DEM 数据:  ❌  缺少纬度带: {sorted(list(missing_dem_bands))}")
        is_dem_covered = False
        
    # 3. 最终结论
    if is_lulc_covered and is_dem_covered:
        print(f"结论: ✔  {region_name}区域的数据已准备就绪，可以进行分析。")
        return True
    else:
        print(f"结论: ❌  {region_name}区域数据不完整。")
        return False

if __name__ == "__main__":
    # 定义原始的五个研究区域的边界框 [min_lon, min_lat, max_lon, max_lat]
    regions = {
        "苏格兰高地": [-6, 56, -3, 58],
        "以色列-巴勒斯坦地区": [34, 31, 36, 33],
        "克什米尔地区": [74, 33, 78, 35],
        "海湾战争区域(科威特)": [46, 28, 49, 31],
        "顿巴斯地区": [37, 47, 40, 49]
    }

    # 获取现有数据
    lulc_files = glob.glob("./data/N*_2010LC030/*.tif")
    available_lulc_poly = get_available_lulc_coverage(lulc_files)
    available_dem_bands = get_available_dem_lat_bands("data/DEM")

    print("\n--- 数据可用性检查报告 ---")
    print(f"当前可用的LULC数据覆盖了 {available_lulc_poly.area:.2f} 平方度的范围。")
    print(f"当前可用的DEM纬度带有: {sorted(list(available_dem_bands))}")
    
    ready_regions_count = 0
    for name, Bbox in regions.items():
        if check_region_data_completeness(name, Bbox, available_lulc_poly, available_dem_bands):
            ready_regions_count += 1
            
    print("\n---------------------------------")
    if ready_regions_count > 0:
        print(f"总共有 {ready_regions_count} 个区域的数据已准备就绪。")
    else:
        print("目前没有一个原始区域的数据是完全齐备的。")
    print("---------------------------------") 