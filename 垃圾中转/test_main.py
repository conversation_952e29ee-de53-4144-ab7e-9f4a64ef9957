#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试脚本
验证重构后的主要功能是否正常
"""

import os
import sys
import subprocess
import tempfile
import numpy as np

def test_config_import():
    """测试配置文件导入"""
    try:
        import config
        print("✓ 配置文件导入成功")
        print(f"  - 目标速度: {config.TARGET_SPEED_KMH} km/h")
        print(f"  - 时间步长: {config.TIME_STEP_MS} ms")
        return True
    except Exception as e:
        print(f"✗ 配置文件导入失败: {e}")
        return False

def test_generate_trajectories_import():
    """测试轨迹生成模块导入"""
    try:
        from generate_trajectories import TrajectoryGenerator
        print("✓ 轨迹生成器导入成功")
        return True
    except Exception as e:
        print(f"✗ 轨迹生成器导入失败: {e}")
        return False

def test_environment_data():
    """测试环境数据是否存在"""
    import config
    env_dir = config.get_env_data_dir()
    
    required_files = [
        'dem_aligned.tif',
        'slope_aligned.tif', 
        'landcover_aligned.tif',
        'aspect_aligned.tif'
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = os.path.join(env_dir, file_name)
        if os.path.exists(file_path):
            print(f"✓ {file_name} 存在")
        else:
            print(f"✗ {file_name} 不存在")
            all_exist = False
    
    return all_exist

def test_path_data():
    """测试路径数据是否存在"""
    import config
    paths_dir = config.get_paths_dir('high_mobility')
    
    if not os.path.exists(paths_dir):
        print(f"✗ 路径目录不存在: {paths_dir}")
        return False
    
    npy_files = [f for f in os.listdir(paths_dir) if f.endswith('.npy')]
    if len(npy_files) > 0:
        print(f"✓ 找到 {len(npy_files)} 个路径文件")
        return True
    else:
        print("✗ 未找到路径文件")
        return False

def test_main_script_help():
    """测试主脚本帮助信息"""
    try:
        result = subprocess.run([sys.executable, 'main.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ 主脚本帮助信息正常")
            return True
        else:
            print(f"✗ 主脚本帮助信息失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 主脚本测试失败: {e}")
        return False

def test_single_file_generation():
    """测试单文件轨迹生成"""
    import config
    
    # 查找一个测试文件
    paths_dir = config.get_paths_dir('high_mobility')
    npy_files = [f for f in os.listdir(paths_dir) if f.endswith('.npy')]
    
    if len(npy_files) == 0:
        print("✗ 没有可用的测试文件")
        return False
    
    test_file = npy_files[0]
    test_file_path = os.path.join(paths_dir, test_file)
    
    try:
        # 运行单文件测试
        cmd = [sys.executable, 'main.py', '--single_file', test_file_path, '--mode', 'high_mobility']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✓ 单文件生成测试成功: {test_file}")
            return True
        else:
            print(f"✗ 单文件生成测试失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 单文件生成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("基于环境约束的轨迹生成系统 - 功能测试")
    print("="*60)
    
    tests = [
        ("配置文件导入", test_config_import),
        ("轨迹生成器导入", test_generate_trajectories_import), 
        ("环境数据检查", test_environment_data),
        ("路径数据检查", test_path_data),
        ("主脚本帮助", test_main_script_help),
        ("单文件生成", test_single_file_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"结果: 通过")
            else:
                print(f"结果: 失败")
        except Exception as e:
            print(f"结果: 异常 - {e}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 系统运行正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 