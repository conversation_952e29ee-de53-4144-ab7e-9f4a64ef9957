#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脚本名称: compare_simulated_trajectories.py
===========================================

功能描述:
  该脚本用于比较基于参考轨迹1生成的仿真轨迹与实际核心轨迹(1-4)的速度特性。脚本创建四个时间窗口(1s、5s、10s、15s)
  的比较图，每个图包含四个子图，每个子图对比不同轨迹间的速度相关性。

主要功能:
  1. 加载指定的核心轨迹数据 (CSV格式)。
  2. 使用模拟函数，基于核心轨迹1的位置点生成仿真轨迹速度。
  3. 使用不同的时间窗口(1s、5s、10s和15s)聚合数据。
  4. 创建比较图，显示核心轨迹1与其他轨迹的速度对比及相关系数。
  5. 为每个时间窗口生成2x2的子图矩阵，包括：
     - 子图1: 核心轨迹1 vs 仿真轨迹
     - 子图2: 核心轨迹1 vs 核心轨迹2
     - 子图3: 核心轨迹1 vs 核心轨迹3
     - 子图4: 核心轨迹1 vs 核心轨迹4

输入:
  - 核心轨迹文件: 存储在指定目录下的1-4号核心轨迹CSV文件，必须包含'timestamp_ms'和'velocity_2d_ms'列。

输出:
  - 速度对比图: 保存到'visualization_results'目录下，文件名格式为
    'trajectory_comparison_{window_size}秒.png'，包含四个子图，每个子图显示速度对比及相关系数。

使用示例:
  直接运行脚本: python compare_simulated_trajectories.py
  脚本将自动查找核心轨迹文件并生成对比图。
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import rasterio
from rasterio.sample import sample_gen
import warnings
warnings.filterwarnings('ignore')

# 从simulate_trajectory.py导入必要的函数
from simulate_trajectory import (
    get_env_data, calculate_effective_slope, calculate_speed, 
    SLOPE_FILE, ASPECT_FILE, LANDCOVER_FILE
)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 14
plt.rcParams['axes.labelsize'] = 16
plt.rcParams['axes.titlesize'] = 18
plt.rcParams['xtick.labelsize'] = 14
plt.rcParams['ytick.labelsize'] = 14
plt.rcParams['legend.fontsize'] = 14

# 输出目录
OUTPUT_DIR = 'visualization_results'
os.makedirs(OUTPUT_DIR, exist_ok=True)

def load_trajectory(traj_file):
    """加载轨迹数据"""
    df = pd.read_csv(traj_file)
    
    # 确保数据包含必要的列
    required_cols = ['timestamp_ms', 'velocity_2d_ms']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"轨迹数据缺少必要的列: {col}")
    
    # 按时间戳排序
    df = df.sort_values('timestamp_ms')
    
    # 计算总长度
    total_points = len(df)
    
    # 去掉前10分钟和后3分钟的数据（基于数据点数量）
    start_idx = int(total_points * (10/60))  # 10分钟
    end_idx = total_points - int(total_points * (3/60))  # 3分钟
    df = df.iloc[start_idx:end_idx].copy()
    
    # 重置索引
    df = df.reset_index(drop=True)
    
    # 添加相对时间列（秒）
    df['time_s'] = np.arange(len(df)) / 10  # 假设数据采样率为10Hz
    
    return df

def simulate_trajectory_from_reference(ref_df):
    """基于参考轨迹的位置点生成仿真轨迹速度
    
    Args:
        ref_df: 参考轨迹的DataFrame，必须包含经纬度和时间戳
        
    Returns:
        DataFrame: 包含原始数据和仿真速度的DataFrame
    """
    print("基于参考轨迹生成仿真轨迹...")
    
    # 创建副本，避免修改原始数据
    df = ref_df.copy()
    
    # 获取环境数据
    df['slope'] = get_env_data(df, SLOPE_FILE)
    df['aspect'] = get_env_data(df, ASPECT_FILE)
    df['landcover'] = get_env_data(df, LANDCOVER_FILE)
    
    print(f"环境数据获取成功:")
    print(f"  坡度数据: {df['slope'].notna().sum()} 个有效值")
    print(f"  坡向数据: {df['aspect'].notna().sum()} 个有效值")
    print(f"  土地覆盖: {df['landcover'].notna().sum()} 个有效值")
    
    # 计算移动方向
    dlon = df['longitude'].diff()
    dlat = df['latitude'].diff()
    df['heading'] = np.degrees(np.arctan2(dlon * np.cos(np.radians(df['latitude'])), dlat)) % 360
    df.loc[df.index[0], 'heading'] = df.loc[df.index[1], 'heading'] if len(df) > 1 else np.nan
    
    # 计算有效坡度
    df['effective_slope'] = df.apply(
        lambda row: calculate_effective_slope(
            row['slope'], 
            row['aspect'], 
            row['heading']
        ), 
        axis=1
    )
    print(f"有效坡度计算成功: {df['effective_slope'].notna().sum()} 个有效值")
    
    # 模拟速度
    simulated_speeds = []
    prev_speed = None
    prev_landcover = None
    for _, row in df.iterrows():
        speed = calculate_speed(
            row['effective_slope'], 
            row['landcover'], 
            prev_speed,
            prev_landcover
        )
        simulated_speeds.append(speed)
        prev_speed = speed
        prev_landcover = row['landcover']
    
    df['simulated_speed'] = simulated_speeds
    print(f"速度模拟成功: {df['simulated_speed'].notna().sum()} 个有效值")
    
    return df

def aggregate_by_window(df, window_size):
    """按时间窗口聚合数据
    
    Args:
        df: 包含time_s和速度数据的DataFrame
        window_size: 时间窗口大小（秒）
        
    Returns:
        DataFrame: 聚合后的DataFrame
    """
    # 创建窗口索引
    df['window_idx'] = (df['time_s'] / window_size).astype(int)
    
    # 动态构建聚合字典
    agg_dict = {
        'time_s': 'mean',
        'velocity_2d_ms': 'mean'
    }
    if 'simulated_speed' in df.columns:
        agg_dict['simulated_speed'] = 'mean'
        
    # 聚合数据
    agg_df = df.groupby('window_idx').agg(agg_dict).reset_index()
    
    return agg_df

def calculate_correlation(df1, df2, col1, col2):
    """计算两个DataFrame中指定列的相关系数
    
    Args:
        df1, df2: 两个DataFrame
        col1, col2: 两个要比较的列名
        
    Returns:
        float: 相关系数，如果无法计算则返回NaN
    """
    # 合并两个DataFrame，基于时间戳
    merged = pd.merge(
        df1[['timestamp', col1]],
        df2[['timestamp', col2]],
        on='timestamp',
        how='inner'
    )
    
    # 检查合并后的数据量
    if len(merged) < 2:
        return np.nan
    
    # 计算相关系数
    return merged[col1].corr(merged[col2])

def create_comparison_figure(ref_df, sim_df, core_dfs, window_size):
    """创建比较图，包含四个子图
    
    Args:
        ref_df: 参考轨迹DataFrame（核心轨迹1）
        sim_df: 仿真轨迹DataFrame
        core_dfs: 其他核心轨迹DataFrame的列表 (2-4)
        window_size: 时间窗口大小
    """
    # 创建2x2的子图矩阵
    fig, axs = plt.subplots(2, 2, figsize=(20, 15))
    fig.suptitle(f'{window_size}秒窗口轨迹速度比较', fontsize=22)
    
    # 扁平化axs数组，方便迭代
    axs = axs.flatten()
    
    # 准备数据
    ref_agg = aggregate_by_window(ref_df, window_size)
    sim_agg = aggregate_by_window(sim_df, window_size)
    core_aggs = [aggregate_by_window(df, window_size) for df in core_dfs]
    
    # 子图1: 核心轨迹1 vs 仿真轨迹
    # 确保长度一致（取较短的那个）
    min_len = min(len(ref_agg), len(sim_agg))
    ref_agg_trim = ref_agg.iloc[:min_len]
    sim_agg_trim = sim_agg.iloc[:min_len]
    
    # 计算相关系数
    corr = ref_agg_trim['velocity_2d_ms'].corr(sim_agg_trim['simulated_speed'])
    
    # 绘制速度曲线
    axs[0].plot(ref_agg_trim['time_s'], ref_agg_trim['velocity_2d_ms'], 'b-', label='核心轨迹1', linewidth=1.5)
    axs[0].plot(sim_agg_trim['time_s'], sim_agg_trim['simulated_speed'], 'r-', label='仿真轨迹', linewidth=1.5)
    
    # 设置坐标轴范围
    y_min = min(ref_agg_trim['velocity_2d_ms'].min(), sim_agg_trim['simulated_speed'].min()) * 0.9
    y_max = max(ref_agg_trim['velocity_2d_ms'].max(), sim_agg_trim['simulated_speed'].max()) * 1.1
    axs[0].set_ylim(y_min, y_max)
    
    axs[0].set_title(f'核心轨迹1 vs 仿真轨迹 (r = {corr:.3f})')
    axs[0].set_xlabel('时间 (秒)')
    axs[0].set_ylabel('速度 (m/s)')
    axs[0].grid(True)
    axs[0].legend(loc='upper right')
    
    # 子图2-4: 核心轨迹1 vs 核心轨迹2-4
    for i, (core_agg, core_idx) in enumerate(zip(core_aggs, range(2, 5))):
        # 确保长度一致（取较短的那个）
        min_len = min(len(ref_agg), len(core_agg))
        ref_agg_trim = ref_agg.iloc[:min_len]
        core_agg_trim = core_agg.iloc[:min_len]
        
        # 计算相关系数
        corr = ref_agg_trim['velocity_2d_ms'].corr(core_agg_trim['velocity_2d_ms'])
        
        # 绘制速度曲线
        axs[i+1].plot(ref_agg_trim['time_s'], ref_agg_trim['velocity_2d_ms'], 'b-', label='核心轨迹1', linewidth=1.5)
        axs[i+1].plot(core_agg_trim['time_s'], core_agg_trim['velocity_2d_ms'], 'r-', label=f'核心轨迹{core_idx}', linewidth=1.5)
        
        # 设置坐标轴范围
        y_min = min(ref_agg_trim['velocity_2d_ms'].min(), core_agg_trim['velocity_2d_ms'].min()) * 0.9
        y_max = max(ref_agg_trim['velocity_2d_ms'].max(), core_agg_trim['velocity_2d_ms'].max()) * 1.1
        axs[i+1].set_ylim(y_min, y_max)
        
        axs[i+1].set_title(f'核心轨迹1 vs 核心轨迹{core_idx} (r = {corr:.3f})')
        axs[i+1].set_xlabel('时间 (秒)')
        axs[i+1].set_ylabel('速度 (m/s)')
        axs[i+1].grid(True)
        axs[i+1].legend(loc='upper right')
    
    # 调整布局
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    
    # 保存图片
    output_file = os.path.join(OUTPUT_DIR, f'trajectory_comparison_{window_size}秒.png')
    plt.savefig(output_file, bbox_inches='tight', dpi=300)
    plt.close()
    
    print(f"{window_size}秒窗口比较图生成完成")

def main():
    """主函数"""
    # 核心轨迹文件路径
    core_files = []
    
    # 查找core_trajectories目录下的所有核心轨迹文件
    for root, dirs, files in os.walk('core_trajectories'):
        for file in files:
            if file.endswith('_core.csv'):
                core_files.append(os.path.join(root, file))
    
    # 检查核心轨迹文件数量
    if len(core_files) < 4:
        print(f"错误: 需要至少4个核心轨迹文件，但只找到 {len(core_files)} 个")
        return
    
    print(f"找到 {len(core_files)} 个核心轨迹文件")
    
    # 加载核心轨迹数据
    try:
        core_dfs = [load_trajectory(file) for file in core_files[:4]]
        print("核心轨迹加载成功")
    except Exception as e:
        print(f"加载轨迹数据失败: {e}")
        return
    
    # 生成仿真轨迹（基于核心轨迹1）
    ref_df = core_dfs[0]
    sim_df = simulate_trajectory_from_reference(ref_df)
    
    # 为不同的时间窗口生成比较图
    for window_size in [1, 5, 10, 15]:
        print(f"\n处理 {window_size}秒 时间窗口...")
        create_comparison_figure(ref_df, sim_df, core_dfs[1:], window_size)
    
    print("\n所有比较图生成完成")

if __name__ == "__main__":
    main() 