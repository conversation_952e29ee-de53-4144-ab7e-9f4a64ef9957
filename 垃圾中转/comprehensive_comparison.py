#!/usr/bin/env python3
"""
全面对比分析：真实OORD轨迹 vs 原始惯性模型 vs 优化惯性模型
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveComparator:
    def __init__(self):
        self.real_data_dir = Path("core_trajectories")
        self.original_data_dir = Path("generated_trajectories")
        self.optimized_data_dir = Path("optimized_output")
        
    def load_real_trajectory(self, trajectory_id=1, max_points=5000):
        """加载真实OORD轨迹数据"""
        file_path = self.real_data_dir / f"converted_sequence_{trajectory_id}_core.csv"
        print(f"加载真实轨迹: {file_path}")
        
        df = pd.read_csv(file_path, nrows=max_points)
        df['time_s'] = (df['timestamp_ms'] - df['timestamp_ms'].iloc[0]) / 1000.0
        
        if 'velocity_2d_ms' in df.columns:
            df['speed_ms'] = df['velocity_2d_ms']
        elif 'calculated_speed_ms' in df.columns:
            df['speed_ms'] = df['calculated_speed_ms']
        else:
            df['speed_ms'] = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
        
        return df, "真实OORD轨迹"
    
    def load_original_inertial_trajectory(self, file_name="trajectory_000_0_high_mobility.csv"):
        """加载原始惯性模型轨迹"""
        file_path = self.original_data_dir / file_name
        print(f"加载原始惯性模型轨迹: {file_path}")
        
        df = pd.read_csv(file_path)
        df['time_s'] = df['timestamp_ms'] / 1000.0
        df['speed_ms'] = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
        
        return df, "原始惯性模型"
    
    def load_optimized_trajectory(self, file_name="trajectory_optimized.csv"):
        """加载优化惯性模型轨迹"""
        file_path = self.optimized_data_dir / file_name
        print(f"加载优化惯性模型轨迹: {file_path}")
        
        df = pd.read_csv(file_path)
        df['time_s'] = df['timestamp_ms'] / 1000.0
        # 优化模型已经包含speed_ms列
        
        return df, "优化惯性模型"
    
    def calculate_statistics(self, df, label):
        """计算统计指标"""
        stats_dict = {
            'label': label,
            'mean_speed': df['speed_ms'].mean(),
            'std_speed': df['speed_ms'].std(),
            'max_speed': df['speed_ms'].max(),
            'min_speed': df['speed_ms'].min(),
            'median_speed': df['speed_ms'].median(),
            'total_points': len(df),
            'speed_range': df['speed_ms'].max() - df['speed_ms'].min(),
            'cv': df['speed_ms'].std() / df['speed_ms'].mean(),  # 变异系数
        }
        
        # 计算加速度统计
        if len(df) > 1:
            acceleration = df['speed_ms'].diff() / (df['time_s'].diff() if 'time_s' in df.columns else 1.0)
            acceleration = acceleration.dropna()
            
            stats_dict.update({
                'mean_acceleration': acceleration.mean(),
                'std_acceleration': acceleration.std(),
                'max_acceleration': acceleration.max(),
                'min_acceleration': acceleration.min(),
            })
        
        return stats_dict
    
    def plot_comprehensive_comparison(self, real_df, original_df, optimized_df):
        """绘制全面对比分析图"""
        fig, axes = plt.subplots(3, 3, figsize=(20, 15))
        fig.suptitle('惯性模型全面对比分析：真实OORD vs 原始模型 vs 优化模型', 
                    fontsize=24, fontweight='bold')
        
        # 数据准备
        datasets = [
            (real_df, "真实OORD轨迹", 'blue'),
            (original_df, "原始惯性模型", 'red'),
            (optimized_df, "优化惯性模型", 'green')
        ]
        
        # 1. 速度时间序列对比
        ax1 = axes[0, 0]
        for df, label, color in datasets:
            sample_size = min(1000, len(df))
            ax1.plot(df['time_s'][:sample_size], df['speed_ms'][:sample_size], 
                    label=label, color=color, alpha=0.7, linewidth=1.5)
        ax1.set_title('速度时间序列对比', fontsize=20)
        ax1.set_xlabel('时间 (秒)', fontsize=18)
        ax1.set_ylabel('速度 (m/s)', fontsize=18)
        ax1.legend(fontsize=16)
        ax1.grid(True, alpha=0.3)
        
        # 2. 速度分布对比
        ax2 = axes[0, 1]
        for df, label, color in datasets:
            ax2.hist(df['speed_ms'], bins=40, alpha=0.5, label=label, 
                    color=color, density=True)
        ax2.set_title('速度分布对比', fontsize=20)
        ax2.set_xlabel('速度 (m/s)', fontsize=18)
        ax2.set_ylabel('密度', fontsize=18)
        ax2.legend(fontsize=16)
        ax2.grid(True, alpha=0.3)
        
        # 3. 速度箱线图
        ax3 = axes[0, 2]
        data_to_plot = [df['speed_ms'] for df, _, _ in datasets]
        labels = [label for _, label, _ in datasets]
        colors = [color for _, _, color in datasets]
        
        box_plot = ax3.boxplot(data_to_plot, labels=labels, patch_artist=True)
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.5)
        
        ax3.set_title('速度分布箱线图', fontsize=20)
        ax3.set_ylabel('速度 (m/s)', fontsize=18)
        ax3.tick_params(axis='x', labelsize=14)
        ax3.grid(True, alpha=0.3)
        
        # 4. 加速度分布对比
        ax4 = axes[1, 0]
        for df, label, color in datasets:
            try:
                acceleration = df['speed_ms'].diff() / (df['time_s'].diff() if 'time_s' in df.columns else 1.0)
                acceleration = acceleration.dropna()
                # 过滤极值
                acceleration = acceleration[abs(acceleration) < 5]
                ax4.hist(acceleration, bins=40, alpha=0.5, label=label, 
                        color=color, density=True)
            except:
                continue
        ax4.set_title('加速度分布对比', fontsize=20)
        ax4.set_xlabel('加速度 (m/s²)', fontsize=18)
        ax4.set_ylabel('密度', fontsize=18)
        ax4.legend(fontsize=16)
        ax4.grid(True, alpha=0.3)
        
        # 5. 统计指标对比表
        ax5 = axes[1, 1]
        ax5.axis('off')
        
        stats_list = []
        for df, label, _ in datasets:
            stats_list.append(self.calculate_statistics(df, label))
        
        stats_text = f"""
        统计指标对比
        
        指标                真实OORD    原始模型    优化模型
        ──────────────────────────────────────────────
        平均速度 (m/s)      {stats_list[0]['mean_speed']:.2f}        {stats_list[1]['mean_speed']:.2f}        {stats_list[2]['mean_speed']:.2f}
        标准差 (m/s)        {stats_list[0]['std_speed']:.2f}         {stats_list[1]['std_speed']:.2f}         {stats_list[2]['std_speed']:.2f}
        最大速度 (m/s)      {stats_list[0]['max_speed']:.2f}        {stats_list[1]['max_speed']:.2f}        {stats_list[2]['max_speed']:.2f}
        最小速度 (m/s)      {stats_list[0]['min_speed']:.2f}         {stats_list[1]['min_speed']:.2f}         {stats_list[2]['min_speed']:.2f}
        
        平均速度 (km/h)     {stats_list[0]['mean_speed']*3.6:.1f}         {stats_list[1]['mean_speed']*3.6:.1f}         {stats_list[2]['mean_speed']*3.6:.1f}
        变异系数           {stats_list[0]['cv']:.3f}        {stats_list[1]['cv']:.3f}        {stats_list[2]['cv']:.3f}
        """
        
        ax5.text(0.05, 0.95, stats_text, transform=ax5.transAxes, 
                fontsize=16, verticalalignment='top', fontfamily='monospace')
        
        # 6. 相关性分析
        ax6 = axes[1, 2]
        try:
            # 与真实轨迹的相关性
            min_len = min(len(real_df), len(original_df), len(optimized_df))
            
            # 原始模型 vs 真实轨迹
            corr_original = np.corrcoef(real_df['speed_ms'][:min_len], 
                                      original_df['speed_ms'][:min_len])[0, 1]
            
            # 优化模型 vs 真实轨迹
            corr_optimized = np.corrcoef(real_df['speed_ms'][:min_len], 
                                       optimized_df['speed_ms'][:min_len])[0, 1]
            
            correlations = [corr_original, corr_optimized]
            model_names = ['原始惯性模型', '优化惯性模型']
            colors = ['red', 'green']
            
            bars = ax6.bar(model_names, correlations, color=colors, alpha=0.7)
            ax6.set_title('与真实轨迹的相关性', fontsize=20)
            ax6.set_ylabel('相关系数', fontsize=18)
            ax6.set_ylim(0, 1)
            
            # 添加数值标签
            for bar, corr in zip(bars, correlations):
                height = bar.get_height()
                ax6.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{corr:.3f}', ha='center', va='bottom', fontsize=16)
            
        except Exception as e:
            ax6.text(0.5, 0.5, f'相关性分析失败:\n{str(e)}', 
                    transform=ax6.transAxes, ha='center', va='center', fontsize=16)
        ax6.grid(True, alpha=0.3)
        
        # 7. 误差分析
        ax7 = axes[2, 0]
        try:
            min_len = min(len(real_df), len(original_df), len(optimized_df))
            
            # 计算误差
            error_original = abs(real_df['speed_ms'][:min_len] - original_df['speed_ms'][:min_len])
            error_optimized = abs(real_df['speed_ms'][:min_len] - optimized_df['speed_ms'][:min_len])
            
            ax7.hist(error_original, bins=30, alpha=0.6, label='原始模型误差', color='red', density=True)
            ax7.hist(error_optimized, bins=30, alpha=0.6, label='优化模型误差', color='green', density=True)
            
            ax7.set_title('速度预测误差分布', fontsize=20)
            ax7.set_xlabel('绝对误差 (m/s)', fontsize=18)
            ax7.set_ylabel('密度', fontsize=18)
            ax7.legend(fontsize=16)
        except Exception as e:
            ax7.text(0.5, 0.5, f'误差分析失败:\n{str(e)}', 
                    transform=ax7.transAxes, ha='center', va='center', fontsize=16)
        ax7.grid(True, alpha=0.3)
        
        # 8. 模型性能评估
        ax8 = axes[2, 1]
        ax8.axis('off')
        
        try:
            # 计算性能指标
            real_mean = stats_list[0]['mean_speed']
            
            original_error = abs(stats_list[1]['mean_speed'] - real_mean) / real_mean * 100
            optimized_error = abs(stats_list[2]['mean_speed'] - real_mean) / real_mean * 100
            
            performance_text = f"""
            模型性能评估
            
            与真实轨迹的平均速度误差：
            
            原始惯性模型:
            - 平均速度误差: {original_error:.1f}%
            - RMSE: {np.sqrt(np.mean(error_original**2)):.2f} m/s
            - MAE: {np.mean(error_original):.2f} m/s
            
            优化惯性模型:
            - 平均速度误差: {optimized_error:.1f}%
            - RMSE: {np.sqrt(np.mean(error_optimized**2)):.2f} m/s
            - MAE: {np.mean(error_optimized):.2f} m/s
            
            改进效果:
            - 速度误差降低: {original_error - optimized_error:.1f}%
            """
            
            ax8.text(0.05, 0.95, performance_text, transform=ax8.transAxes, 
                    fontsize=16, verticalalignment='top', fontfamily='monospace')
        except:
            ax8.text(0.5, 0.5, '性能评估计算失败', 
                    transform=ax8.transAxes, ha='center', va='center', fontsize=16)
        
        # 9. 速度范围对比
        ax9 = axes[2, 2]
        speed_ranges = [stats['speed_range'] for stats in stats_list]
        model_names = [stats['label'] for stats in stats_list]
        colors = ['blue', 'red', 'green']
        
        bars = ax9.bar(model_names, speed_ranges, color=colors, alpha=0.7)
        ax9.set_title('速度变化范围对比', fontsize=20)
        ax9.set_ylabel('速度范围 (m/s)', fontsize=18)
        ax9.tick_params(axis='x', labelsize=14)
        
        # 添加数值标签
        for bar, range_val in zip(bars, speed_ranges):
            height = bar.get_height()
            ax9.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{range_val:.1f}', ha='center', va='bottom', fontsize=16)
        ax9.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig, stats_list
    
    def run_comprehensive_comparison(self):
        """运行全面对比分析"""
        print("=" * 80)
        print("惯性模型全面对比分析")
        print("=" * 80)
        
        # 加载数据
        real_df, real_label = self.load_real_trajectory()
        original_df, original_label = self.load_original_inertial_trajectory()
        optimized_df, optimized_label = self.load_optimized_trajectory()
        
        print(f"\n数据加载完成:")
        print(f"{real_label}: {len(real_df)} 个数据点")
        print(f"{original_label}: {len(original_df)} 个数据点")
        print(f"{optimized_label}: {len(optimized_df)} 个数据点")
        
        # 生成对比图
        fig, stats_list = self.plot_comprehensive_comparison(real_df, original_df, optimized_df)
        
        # 保存结果
        output_file = "惯性模型全面对比分析.png"
        fig.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n全面对比分析图已保存: {output_file}")
        
        # 生成详细报告
        self.generate_comprehensive_report(stats_list)
        
        return fig, stats_list
    
    def generate_comprehensive_report(self, stats_list):
        """生成全面分析报告"""
        real_stats, original_stats, optimized_stats = stats_list
        
        report = f"""
# 惯性模型全面对比分析报告

## 数据概览
- 真实OORD轨迹数据点: {real_stats['total_points']}
- 原始惯性模型数据点: {original_stats['total_points']}
- 优化惯性模型数据点: {optimized_stats['total_points']}

## 速度统计全面对比

### 真实OORD轨迹（基准）
- 平均速度: {real_stats['mean_speed']:.2f} m/s ({real_stats['mean_speed']*3.6:.1f} km/h)
- 速度标准差: {real_stats['std_speed']:.2f} m/s
- 速度范围: {real_stats['min_speed']:.2f} - {real_stats['max_speed']:.2f} m/s
- 变异系数: {real_stats['cv']:.3f}

### 原始惯性模型
- 平均速度: {original_stats['mean_speed']:.2f} m/s ({original_stats['mean_speed']*3.6:.1f} km/h)
- 速度标准差: {original_stats['std_speed']:.2f} m/s
- 速度范围: {original_stats['min_speed']:.2f} - {original_stats['max_speed']:.2f} m/s
- 变异系数: {original_stats['cv']:.3f}
- 与真实轨迹的速度误差: {abs(original_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100:.1f}%

### 优化惯性模型
- 平均速度: {optimized_stats['mean_speed']:.2f} m/s ({optimized_stats['mean_speed']*3.6:.1f} km/h)
- 速度标准差: {optimized_stats['std_speed']:.2f} m/s
- 速度范围: {optimized_stats['min_speed']:.2f} - {optimized_stats['max_speed']:.2f} m/s
- 变异系数: {optimized_stats['cv']:.3f}
- 与真实轨迹的速度误差: {abs(optimized_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100:.1f}%

## 模型改进效果分析

### 速度匹配度改进
- 原始模型速度误差: {abs(original_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100:.1f}%
- 优化模型速度误差: {abs(optimized_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100:.1f}%
- 改进幅度: {abs(original_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100 - abs(optimized_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100:.1f}个百分点

### 速度变异性对比
- 真实轨迹变异系数: {real_stats['cv']:.3f}
- 原始模型变异系数: {original_stats['cv']:.3f}
- 优化模型变异系数: {optimized_stats['cv']:.3f}

## 结论与建议

### 模型性能评价
"""
        
        # 评价优化效果
        original_error = abs(original_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100
        optimized_error = abs(optimized_stats['mean_speed'] - real_stats['mean_speed'])/real_stats['mean_speed']*100
        
        if optimized_error < original_error * 0.5:
            improvement = "显著改进"
        elif optimized_error < original_error * 0.8:
            improvement = "明显改进"
        elif optimized_error < original_error:
            improvement = "有所改进"
        else:
            improvement = "改进有限"
        
        report += f"""
1. **优化效果**: {improvement}
   - 优化模型的速度预测误差从{original_error:.1f}%降低到{optimized_error:.1f}%
   - 更好地匹配了真实OORD轨迹的速度特征

2. **模型特点对比**:
   - 真实轨迹: 平均速度较低({real_stats['mean_speed']*3.6:.1f} km/h)，变化相对平稳
   - 原始模型: 速度过高({original_stats['mean_speed']*3.6:.1f} km/h)，不符合实际情况
   - 优化模型: 速度更接近真实情况({optimized_stats['mean_speed']*3.6:.1f} km/h)

3. **建议**:
   - 优化模型在速度匹配度方面表现更好，更适合实际应用
   - 可以进一步细化环境参数以提高预测精度
   - 考虑引入更多真实轨迹数据进行模型训练
"""
        
        # 保存报告
        with open("惯性模型全面对比分析报告.md", "w", encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n全面分析报告已保存: 惯性模型全面对比分析报告.md")
        print(f"优化效果评价: {improvement}")

if __name__ == "__main__":
    comparator = ComprehensiveComparator()
    fig, stats = comparator.run_comprehensive_comparison()
    plt.show() 