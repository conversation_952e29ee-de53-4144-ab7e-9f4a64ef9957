#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy as np
import rasterio
import os

def grid_to_utm(grid_x, grid_y, transform):
    """将栅格坐标转换为UTM坐标 (栅格中心)"""
    return transform * (grid_x, grid_y)

# --- 配置 ---
path_dir = '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/high_mobility/'
env_dir = '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/environment/'
dem_file = os.path.join(env_dir, 'dem_aligned.tif')

# 选择一个代表性的起点ID (例如 099)
origin_id_for_sampling = '099' 
# --- 配置结束 ---

# 加载地理变换信息
try:
    with rasterio.open(dem_file) as dem_data:
        transform = dem_data.transform
        print(f"从 {dem_file} 加载地理变换信息成功。")
except Exception as e:
    print(f"错误：无法加载DEM文件 {dem_file}。 {e}")
    exit(1)

# 定义目标文件
goal_files = {
    0: os.path.join(path_dir, f'path_{origin_id_for_sampling}_000_high_mobility.npy'),
    1: os.path.join(path_dir, f'path_{origin_id_for_sampling}_001_high_mobility.npy'),
    2: os.path.join(path_dir, f'path_{origin_id_for_sampling}_002_high_mobility.npy'),
    3: os.path.join(path_dir, f'path_{origin_id_for_sampling}_003_high_mobility.npy')
}

# 提取并转换坐标
goal_coordinates = {}
print("\n正在处理目标点文件...")
for goal_id, file_path in goal_files.items():
    print(f"--- 处理目标 {goal_id} (文件: {os.path.basename(file_path)}) ---")
    if not os.path.exists(file_path):
        print(f"  错误: 文件不存在 {file_path}")
        continue
        
    try:
        path_points = np.load(file_path)
        if path_points is not None and path_points.shape[0] > 0:
            # 获取最后一个路径点
            last_grid_point = path_points[-1]
            # 确保是二维坐标
            if len(last_grid_point) == 2:
                grid_x, grid_y = last_grid_point[0], last_grid_point[1]
                print(f"  找到最后一个栅格点: ({grid_x}, {grid_y})")
                # 转换到UTM
                utm_x, utm_y = grid_to_utm(grid_x, grid_y, transform)
                goal_coordinates[goal_id] = (utm_x, utm_y)
                print(f"  转换后UTM坐标: ({utm_x:.2f}, {utm_y:.2f})")
            else:
                 print(f"  错误: 最后一个点格式不正确: {last_grid_point}")
        else:
            print(f"  警告: 文件 {os.path.basename(file_path)} 为空或无法加载。")
    except Exception as e:
        print(f"  处理文件 {os.path.basename(file_path)} 时发生错误: {e}")

# 打印总结
print("\n=========================================")
print("目标点 UTM 坐标总结 (EPSG:32630)")
print("=========================================")
if goal_coordinates:
    for goal_id in range(4): # 确保按顺序打印0-3
        if goal_id in goal_coordinates:
            coords = goal_coordinates[goal_id]
            print(f"  目标 {goal_id}: X = {coords[0]:.2f}, Y = {coords[1]:.2f}")
        else:
             print(f"  目标 {goal_id}: 未能成功获取坐标。")
else:
    print("未能获取任何目标点的坐标。")
print("=========================================") 