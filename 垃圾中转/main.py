#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主入口脚本 - 基于环境约束的轨迹生成系统
==================================================

功能描述:
  这是重构后的主入口脚本，提供简洁统一的接口来生成轨迹。
  整合了原有的多个脚本功能，支持不同运动模式的轨迹生成。

主要功能:
  1. 统一的命令行接口
  2. 支持多种运动模式 (high_mobility, standard, stealth_priority, mountain_special)
  3. 批量处理和单文件处理
  4. 自动创建输出目录
  5. 进度显示和错误处理
  6. 可视化结果生成

使用示例:
  # 基本用法
  python main.py
  
  # 指定参数
  python main.py --num_files 10 --target_speed 75.0 --mode high_mobility
  
  # 批量生成所有模式
  python main.py --batch --mode all
  
  # 处理单个文件
  python main.py --single_file path_000_000_high_mobility.npy --mode high_mobility
"""

import os
import sys
import argparse
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入配置和核心模块
import config
from generate_trajectories import TrajectoryGenerator, process_path_file

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOGGING['level']),
        format=config.LOGGING['format'],
        handlers=[
            logging.FileHandler(config.LOGGING['file']),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def create_directories(mode='high_mobility'):
    """创建必要的目录"""
    output_dir = config.get_output_dir(mode)
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建其他输出目录
    os.makedirs(config.RESULTS_DIR, exist_ok=True)
    os.makedirs(config.PLOTS_DIR, exist_ok=True)
    
    return output_dir

def get_path_files(mode='high_mobility'):
    """获取指定模式的路径文件列表"""
    paths_dir = config.get_paths_dir(mode)
    suffix = config.MOTION_MODES[mode]['suffix']
    
    if not os.path.exists(paths_dir):
        raise FileNotFoundError(f"路径目录不存在: {paths_dir}")
    
    path_files = [f for f in os.listdir(paths_dir) if f.endswith(f'_{suffix}.npy')]
    path_files.sort()  # 确保一致的处理顺序
    
    return paths_dir, path_files

def process_single_mode(mode, num_files, skip_files, target_speed, logger):
    """处理单个运动模式"""
    logger.info(f"开始处理模式: {mode} ({config.MOTION_MODES[mode]['description']})")
    
    # 获取路径文件
    try:
        paths_dir, path_files = get_path_files(mode)
        logger.info(f"找到 {len(path_files)} 个 {mode} 路径文件")
    except FileNotFoundError as e:
        logger.error(f"模式 {mode} 处理失败: {e}")
        return 0, 0
    
    # 创建输出目录
    output_dir = create_directories(mode)
    
    # 应用跳过和限制
    if skip_files > 0:
        path_files = path_files[skip_files:]
        logger.info(f"跳过前 {skip_files} 个文件")
    
    if num_files > 0 and num_files < len(path_files):
        path_files = path_files[:num_files]
    
    # 初始化轨迹生成器
    env_dir = config.get_env_data_dir()
    generator = TrajectoryGenerator(env_dir, target_speed)
    
    # 处理文件
    success_count = 0
    start_time = time.time()
    
    for i, file_name in enumerate(path_files):
        file_path = os.path.join(paths_dir, file_name)
        logger.info(f"[{i+1}/{len(path_files)}] 处理文件: {file_name}")
        
        try:
            if process_path_file(file_path, generator, output_dir, mode):
                success_count += 1
            
            # 显示进度
            if (i + 1) % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / (i + 1)
                remaining = avg_time * (len(path_files) - i - 1)
                logger.info(f"进度: {i+1}/{len(path_files)}, 预计剩余时间: {remaining:.1f}秒")
                
        except Exception as e:
            logger.error(f"处理文件 {file_name} 时出错: {e}")
            continue
    
    total_time = time.time() - start_time
    logger.info(f"模式 {mode} 处理完成:")
    logger.info(f"  成功: {success_count}/{len(path_files)} 个文件")
    logger.info(f"  耗时: {total_time:.1f} 秒")
    logger.info(f"  输出目录: {output_dir}")
    
    return success_count, len(path_files)

def process_single_file(file_path, mode, target_speed, logger):
    """处理单个文件"""
    logger.info(f"处理单个文件: {file_path}")
    
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return False
    
    # 创建输出目录
    output_dir = create_directories(mode)
    
    # 初始化轨迹生成器
    env_dir = config.get_env_data_dir()
    generator = TrajectoryGenerator(env_dir, target_speed)
    
    # 处理文件
    try:
        success = process_path_file(file_path, generator, output_dir, mode)
        if success:
            logger.info(f"文件处理成功，输出目录: {output_dir}")
        else:
            logger.error("文件处理失败")
        return success
    except Exception as e:
        logger.error(f"处理文件时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='基于环境约束的轨迹生成系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法 - 生成10个高机动性轨迹
  python main.py
  
  # 指定参数
  python main.py --num_files 20 --target_speed 80.0 --mode standard
  
  # 批量生成所有模式
  python main.py --batch --mode all
  
  # 处理单个文件
  python main.py --single_file path_000_000_high_mobility.npy --mode high_mobility
  
  # 跳过前50个文件，处理接下来的30个
  python main.py --skip_files 50 --num_files 30 --mode stealth_priority
        """
    )
    
    # 基本参数
    parser.add_argument('--mode', type=str, default='high_mobility',
                        choices=['high_mobility', 'standard', 'stealth_priority', 'mountain_special', 'all'],
                        help='运动模式 (默认: high_mobility)')
    parser.add_argument('--num_files', type=int, default=config.PROCESSING['default_num_files'],
                        help='处理的文件数量 (默认: 10, -1表示全部)')
    parser.add_argument('--skip_files', type=int, default=0,
                        help='跳过前N个文件 (默认: 0)')
    parser.add_argument('--target_speed', type=float, default=config.TARGET_SPEED_KMH,
                        help='目标平均速度 km/h (默认: 75.0)')
    
    # 特殊模式
    parser.add_argument('--batch', action='store_true',
                        help='批量处理模式')
    parser.add_argument('--single_file', type=str,
                        help='处理单个文件')
    
    # 其他选项
    parser.add_argument('--verbose', action='store_true',
                        help='详细输出')
    parser.add_argument('--no_plots', action='store_true',
                        help='不生成可视化图表')
    
    args = parser.parse_args()
    
    # 设置日志
    if args.verbose:
        config.LOGGING['level'] = 'DEBUG'
    
    logger = setup_logging()
    logger.info("="*60)
    logger.info("基于环境约束的轨迹生成系统")
    logger.info("="*60)
    
    # 检查环境数据
    env_dir = config.get_env_data_dir()
    if not os.path.exists(env_dir):
        logger.error(f"环境数据目录不存在: {env_dir}")
        sys.exit(1)
    
    required_files = [
        os.path.join(env_dir, 'dem_aligned.tif'),
        os.path.join(env_dir, 'slope_aligned.tif'),
        os.path.join(env_dir, 'landcover_aligned.tif'),
        os.path.join(env_dir, 'aspect_aligned.tif')
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.error(f"必需的环境数据文件不存在: {file_path}")
            sys.exit(1)
    
    logger.info("环境数据检查通过")
    
    # 处理逻辑
    start_time = time.time()
    
    try:
        if args.single_file:
            # 单文件处理
            success = process_single_file(args.single_file, args.mode, args.target_speed, logger)
            sys.exit(0 if success else 1)
            
        elif args.batch and args.mode == 'all':
            # 批量处理所有模式
            total_success = 0
            total_files = 0
            
            for mode in ['high_mobility', 'standard', 'stealth_priority', 'mountain_special']:
                try:
                    success, files = process_single_mode(mode, args.num_files, args.skip_files, args.target_speed, logger)
                    total_success += success
                    total_files += files
                except Exception as e:
                    logger.error(f"处理模式 {mode} 时出错: {e}")
                    continue
            
            logger.info(f"\n批量处理完成:")
            logger.info(f"  总成功: {total_success}/{total_files} 个文件")
            
        else:
            # 单模式处理
            success, files = process_single_mode(args.mode, args.num_files, args.skip_files, args.target_speed, logger)
            
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        sys.exit(1)
    
    total_time = time.time() - start_time
    logger.info(f"\n总处理时间: {total_time:.1f} 秒")
    logger.info("处理完成!")

if __name__ == "__main__":
    main() 