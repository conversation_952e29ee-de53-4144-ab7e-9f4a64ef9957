#!/usr/bin/env python3
"""
惯性模型与真实OORD轨迹对比分析脚本
对比惯性模型生成的轨迹与真实的OORD数据集轨迹
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TrajectoryComparator:
    def __init__(self):
        self.real_data_dir = Path("core_trajectories")
        self.inertial_data_dir = Path("generated_trajectories")
        
    def load_real_trajectory(self, trajectory_id=1):
        """加载真实OORD轨迹数据"""
        file_path = self.real_data_dir / f"converted_sequence_{trajectory_id}_core.csv"
        print(f"加载真实轨迹: {file_path}")
        
        # 由于文件较大，只读取前10000行进行分析
        df = pd.read_csv(file_path, nrows=10000)
        
        # 计算时间间隔（转换为秒）
        df['time_s'] = (df['timestamp_ms'] - df['timestamp_ms'].iloc[0]) / 1000.0
        
        # 使用已有的速度数据或计算速度
        if 'velocity_2d_ms' in df.columns:
            df['speed_ms'] = df['velocity_2d_ms']
        elif 'calculated_speed_ms' in df.columns:
            df['speed_ms'] = df['calculated_speed_ms']
        else:
            # 计算速度
            df['speed_ms'] = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
        
        return df
    
    def load_inertial_trajectory(self, trajectory_file="trajectory_000_0_high_mobility.csv"):
        """加载惯性模型生成的轨迹"""
        file_path = self.inertial_data_dir / trajectory_file
        print(f"加载惯性模型轨迹: {file_path}")
        
        df = pd.read_csv(file_path)
        
        # 计算时间序列（以毫秒为单位转换为秒）
        df['time_s'] = df['timestamp_ms'] / 1000.0
        
        # 计算速度：从velocity_north_ms和velocity_east_ms计算2D速度
        if 'velocity_north_ms' in df.columns and 'velocity_east_ms' in df.columns:
            df['speed_ms'] = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
        elif 'speed_ms' in df.columns:
            df['speed_ms'] = df['speed_ms']
        elif 'velocity_ms' in df.columns:
            df['speed_ms'] = df['velocity_ms']
        else:
            raise ValueError("无法找到速度数据列")
        
        return df
    
    def calculate_statistics(self, df, label):
        """计算轨迹统计指标"""
        stats_dict = {
            'label': label,
            'mean_speed': df['speed_ms'].mean(),
            'std_speed': df['speed_ms'].std(),
            'max_speed': df['speed_ms'].max(),
            'min_speed': df['speed_ms'].min(),
            'median_speed': df['speed_ms'].median(),
            'total_points': len(df),
        }
        
        # 计算加速度（如果数据足够）
        if len(df) > 1:
            speed_diff = df['speed_ms'].diff()
            if 'time_s' in df.columns:
                time_diff = df['time_s'].diff()
                acceleration = speed_diff / time_diff
            elif 'timestamp_ms' in df.columns:
                time_diff = df['timestamp_ms'].diff() / 1000.0
                acceleration = speed_diff / time_diff
            else:
                # 假设1秒时间间隔
                acceleration = speed_diff / 1.0
            
            stats_dict.update({
                'mean_acceleration': acceleration.mean(),
                'std_acceleration': acceleration.std(),
                'max_acceleration': acceleration.max(),
                'min_acceleration': acceleration.min(),
            })
        
        return stats_dict
    
    def plot_comparison(self, real_df, inertial_df):
        """绘制对比分析图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('惯性模型与真实OORD轨迹对比分析', fontsize=22, fontweight='bold')
        
        # 1. 速度时间序列对比
        ax1 = axes[0, 0]
        if 'time_s' in real_df.columns:
            ax1.plot(real_df['time_s'][:1000], real_df['speed_ms'][:1000], 
                    label='真实OORD轨迹', color='blue', alpha=0.7, linewidth=1.5)
        else:
            ax1.plot(real_df['speed_ms'][:1000], 
                    label='真实OORD轨迹', color='blue', alpha=0.7, linewidth=1.5)
        
        if 'time_s' in inertial_df.columns:
            ax1.plot(inertial_df['time_s'][:1000], inertial_df['speed_ms'][:1000], 
                    label='惯性模型轨迹', color='red', alpha=0.7, linewidth=1.5)
        else:
            ax1.plot(inertial_df['speed_ms'][:1000], 
                    label='惯性模型轨迹', color='red', alpha=0.7, linewidth=1.5)
        
        ax1.set_title('速度时间序列对比', fontsize=20)
        ax1.set_xlabel('时间 (秒)', fontsize=18)
        ax1.set_ylabel('速度 (m/s)', fontsize=18)
        ax1.legend(fontsize=16)
        ax1.grid(True, alpha=0.3)
        
        # 2. 速度分布对比
        ax2 = axes[0, 1]
        ax2.hist(real_df['speed_ms'], bins=50, alpha=0.6, label='真实OORD轨迹', 
                color='blue', density=True)
        ax2.hist(inertial_df['speed_ms'], bins=50, alpha=0.6, label='惯性模型轨迹', 
                color='red', density=True)
        ax2.set_title('速度分布对比', fontsize=20)
        ax2.set_xlabel('速度 (m/s)', fontsize=18)
        ax2.set_ylabel('密度', fontsize=18)
        ax2.legend(fontsize=16)
        ax2.grid(True, alpha=0.3)
        
        # 3. 速度箱线图对比
        ax3 = axes[0, 2]
        data_to_plot = [real_df['speed_ms'], inertial_df['speed_ms']]
        labels = ['真实OORD轨迹', '惯性模型轨迹']
        colors = ['lightblue', 'lightcoral']
        
        box_plot = ax3.boxplot(data_to_plot, labels=labels, patch_artist=True)
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
        
        ax3.set_title('速度分布箱线图', fontsize=20)
        ax3.set_ylabel('速度 (m/s)', fontsize=18)
        ax3.tick_params(axis='x', labelsize=16)
        ax3.grid(True, alpha=0.3)
        
        # 4. 加速度对比（如果可以计算）
        ax4 = axes[1, 0]
        try:
            # 计算真实轨迹加速度
            real_accel = real_df['speed_ms'].diff() / (real_df['time_s'].diff() if 'time_s' in real_df.columns else 1.0)
            real_accel = real_accel.dropna()
            
            # 计算惯性模型加速度
            inertial_accel = inertial_df['speed_ms'].diff() / (inertial_df['time_s'].diff() if 'time_s' in inertial_df.columns else 1.0)
            inertial_accel = inertial_accel.dropna()
            
            ax4.hist(real_accel[abs(real_accel) < 10], bins=50, alpha=0.6, 
                    label='真实OORD轨迹', color='blue', density=True)
            ax4.hist(inertial_accel[abs(inertial_accel) < 10], bins=50, alpha=0.6, 
                    label='惯性模型轨迹', color='red', density=True)
            ax4.set_title('加速度分布对比', fontsize=20)
            ax4.set_xlabel('加速度 (m/s²)', fontsize=18)
            ax4.set_ylabel('密度', fontsize=18)
            ax4.legend(fontsize=16)
        except Exception as e:
            ax4.text(0.5, 0.5, f'加速度计算失败:\n{str(e)}', 
                    transform=ax4.transAxes, ha='center', va='center', fontsize=16)
        ax4.grid(True, alpha=0.3)
        
        # 5. 统计指标对比表
        ax5 = axes[1, 1]
        ax5.axis('off')
        
        real_stats = self.calculate_statistics(real_df, '真实OORD轨迹')
        inertial_stats = self.calculate_statistics(inertial_df, '惯性模型轨迹')
        
        stats_text = f"""
        统计指标对比
        
        指标                    真实OORD轨迹    惯性模型轨迹
        ────────────────────────────────────────────
        平均速度 (m/s)         {real_stats['mean_speed']:.2f}          {inertial_stats['mean_speed']:.2f}
        标准差 (m/s)           {real_stats['std_speed']:.2f}           {inertial_stats['std_speed']:.2f}
        最大速度 (m/s)         {real_stats['max_speed']:.2f}          {inertial_stats['max_speed']:.2f}
        最小速度 (m/s)         {real_stats['min_speed']:.2f}           {inertial_stats['min_speed']:.2f}
        中位数速度 (m/s)       {real_stats['median_speed']:.2f}        {inertial_stats['median_speed']:.2f}
        数据点数               {real_stats['total_points']}            {inertial_stats['total_points']}
        
        平均速度 (km/h)        {real_stats['mean_speed']*3.6:.1f}         {inertial_stats['mean_speed']*3.6:.1f}
        """
        
        ax5.text(0.1, 0.9, stats_text, transform=ax5.transAxes, 
                fontsize=16, verticalalignment='top', fontfamily='monospace')
        
        # 6. 相关性分析
        ax6 = axes[1, 2]
        try:
            # 对齐数据长度进行相关性分析
            min_len = min(len(real_df), len(inertial_df))
            correlation = np.corrcoef(real_df['speed_ms'][:min_len], 
                                    inertial_df['speed_ms'][:min_len])[0, 1]
            
            ax6.scatter(real_df['speed_ms'][:min_len], inertial_df['speed_ms'][:min_len], 
                       alpha=0.5, s=10)
            ax6.plot([0, max(real_df['speed_ms'].max(), inertial_df['speed_ms'].max())], 
                    [0, max(real_df['speed_ms'].max(), inertial_df['speed_ms'].max())], 
                    'r--', alpha=0.8)
            ax6.set_title(f'速度相关性分析\n相关系数: {correlation:.3f}', fontsize=20)
            ax6.set_xlabel('真实OORD轨迹速度 (m/s)', fontsize=18)
            ax6.set_ylabel('惯性模型轨迹速度 (m/s)', fontsize=18)
        except Exception as e:
            ax6.text(0.5, 0.5, f'相关性分析失败:\n{str(e)}', 
                    transform=ax6.transAxes, ha='center', va='center', fontsize=16)
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig, (real_stats, inertial_stats)
    
    def run_comparison(self):
        """运行完整的对比分析"""
        print("=" * 60)
        print("惯性模型与真实OORD轨迹对比分析")
        print("=" * 60)
        
        # 加载数据
        real_df = self.load_real_trajectory(trajectory_id=1)
        inertial_df = self.load_inertial_trajectory()
        
        print(f"\n数据加载完成:")
        print(f"真实轨迹数据点: {len(real_df)}")
        print(f"惯性模型数据点: {len(inertial_df)}")
        
        # 生成对比图
        fig, stats = self.run_comparison_analysis(real_df, inertial_df)
        
        # 保存结果
        output_file = "惯性模型与真实OORD轨迹对比分析.png"
        fig.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n对比分析图已保存: {output_file}")
        
        # 生成分析报告
        self.generate_report(stats)
        
        return fig, stats
    
    def run_comparison_analysis(self, real_df, inertial_df):
        """运行对比分析并生成图表"""
        return self.plot_comparison(real_df, inertial_df)
    
    def generate_report(self, stats):
        """生成分析报告"""
        real_stats, inertial_stats = stats
        
        report = f"""
# 惯性模型与真实OORD轨迹对比分析报告

## 数据概览
- 真实OORD轨迹数据点: {real_stats['total_points']}
- 惯性模型轨迹数据点: {inertial_stats['total_points']}

## 速度统计对比

### 真实OORD轨迹
- 平均速度: {real_stats['mean_speed']:.2f} m/s ({real_stats['mean_speed']*3.6:.1f} km/h)
- 速度标准差: {real_stats['std_speed']:.2f} m/s
- 最大速度: {real_stats['max_speed']:.2f} m/s ({real_stats['max_speed']*3.6:.1f} km/h)
- 最小速度: {real_stats['min_speed']:.2f} m/s ({real_stats['min_speed']*3.6:.1f} km/h)
- 中位数速度: {real_stats['median_speed']:.2f} m/s ({real_stats['median_speed']*3.6:.1f} km/h)

### 惯性模型轨迹
- 平均速度: {inertial_stats['mean_speed']:.2f} m/s ({inertial_stats['mean_speed']*3.6:.1f} km/h)
- 速度标准差: {inertial_stats['std_speed']:.2f} m/s
- 最大速度: {inertial_stats['max_speed']:.2f} m/s ({inertial_stats['max_speed']*3.6:.1f} km/h)
- 最小速度: {inertial_stats['min_speed']:.2f} m/s ({inertial_stats['min_speed']*3.6:.1f} km/h)
- 中位数速度: {inertial_stats['median_speed']:.2f} m/s ({inertial_stats['median_speed']*3.6:.1f} km/h)

## 对比分析结果

### 速度差异分析
- 平均速度差异: {abs(real_stats['mean_speed'] - inertial_stats['mean_speed']):.2f} m/s
- 速度变异性对比: 真实轨迹标准差 {real_stats['std_speed']:.2f} vs 惯性模型 {inertial_stats['std_speed']:.2f}

### 模型性能评价
"""
        
        # 评价模型性能
        speed_diff_ratio = abs(real_stats['mean_speed'] - inertial_stats['mean_speed']) / real_stats['mean_speed']
        if speed_diff_ratio < 0.1:
            performance = "优秀"
        elif speed_diff_ratio < 0.2:
            performance = "良好"
        elif speed_diff_ratio < 0.3:
            performance = "一般"
        else:
            performance = "需要改进"
        
        report += f"- 平均速度匹配度: {performance} (差异比例: {speed_diff_ratio*100:.1f}%)\n"
        
        # 保存报告
        with open("惯性模型对比分析报告.md", "w", encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n分析报告已保存: 惯性模型对比分析报告.md")
        print(f"模型性能评价: {performance}")

if __name__ == "__main__":
    comparator = TrajectoryComparator()
    fig, stats = comparator.run_comparison()
    plt.show() 