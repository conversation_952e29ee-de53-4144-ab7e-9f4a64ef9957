import math

# 基于我们之前工作定义的区域中心点和范围，这里是计算出的近似经纬度边界
REGIONS = {
    "scottish_highlands": {"lat_min": 56.48, "lat_max": 58.48, "lon_min": -6.02, "lon_max": -2.42},
    "israel_palestine": {"lat_min": 30.77, "lat_max": 32.77, "lon_min": 34.11, "lon_max": 36.31},
    "kashmir": {"lat_min": 33.08, "lat_max": 35.08, "lon_min": 73.7, "lon_max": 75.9},
    "gulf_war_kuwait": {"lat_min": 28.38, "lat_max": 30.38, "lon_min": 46.88, "lon_max": 49.08},
    "donbas": {"lat_min": 47.02, "lat_max": 49.02, "lon_min": 36.4, "lon_max": 39.2},
}

def get_tile_name(lat_edge, lon_edge):
    """根据瓦片左上角的经纬度生成标准文件名"""
    lat_prefix = 'N' if lat_edge >= 0 else 'S'
    # 文件名中的纬度值是瓦片的上边缘
    lat_val = abs(lat_edge)

    lon_prefix = 'E' if lon_edge >= 0 else 'W'
    # 文件名中的经度值是瓦片的左边缘
    lon_val = abs(lon_edge)
    
    # 格式化文件名，例如: GLC_FCS30_2020_W010N60
    return f"GLC_FCS30_2020_{lon_prefix}{lon_val:03d}{lat_prefix}{lat_val:02d}"

def calculate_tiles_for_bbox(lat_min, lat_max, lon_min, lon_max):
    """根据经纬度边界框计算所有需要的瓦片文件名"""
    tiles = set()
    
    # 纬度逻辑: 文件名基于瓦片上边缘 (top edge). 瓦片覆盖范围是 [lat-5, lat].
    # 我们需要覆盖从 lat_min 到 lat_max 的范围.
    # 因此，需要的第一个瓦片的上边缘是大于等于 lat_min 的最小的5的倍数。
    # 需要的最后一个瓦片的上边缘是大于等于 lat_max 的最小的5的倍数。
    start_lat_edge = math.ceil(lat_min / 5.0) * 5
    end_lat_edge = math.ceil(lat_max / 5.0) * 5

    # 经度逻辑: 文件名基于瓦片左边缘 (left edge). 瓦片覆盖范围是 [lon, lon+5].
    # 我们需要覆盖从 lon_min 到 lon_max 的范围.
    # 因此，需要的第一个瓦片的左边缘是小于等于 lon_min 的最大的5的倍数。
    # 需要的最后一个瓦片的左边缘是小于等于 lon_max 的最大的5的倍数。
    start_lon_edge = math.floor(lon_min / 5.0) * 5
    end_lon_edge = math.floor(lon_max / 5.0) * 5

    # 迭代所有可能的经纬度组合来生成瓦片名
    # Python的range函数不包含stop值，所以end值需要+1（或对于5度瓦片来说+5）
    for lat in range(start_lat_edge, end_lat_edge + 5, 5):
        for lon in range(start_lon_edge, end_lon_edge + 5, 5):
            tiles.add(get_tile_name(lat, lon))
            
    return sorted(list(tiles))

def main():
    """主函数，打印出所有需要下载的文件列表"""
    print("="*80)
    print(" 全球地表覆盖数据(GLC_FCS30)下载清单")
    print("="*80)
    print("\n根据您提供的地表覆盖数据源和文件名格式，以下是您需要为每个区域下载的瓦片文件列表。")
    print("由于该网站需要登录才能下载，我无法直接为您完成下载，请您根据此清单手动下载。\n")
    
    all_tiles = set()
    for region_name, bbox in REGIONS.items():
        region_title = region_name.replace('_', ' ').title()
        print(f"--- 区域: {region_title} ---")
        tiles = calculate_tiles_for_bbox(bbox['lat_min'], bbox['lat_max'], bbox['lon_min'], bbox['lon_max'])
        if not tiles:
            print("  (该区域无需下载新的瓦片)")
        for tile in tiles:
            print(f"  - {tile}")
            all_tiles.add(tile)
        print()

    print("-" * 80)
    print(">>> 总结：总共需要下载的独立文件列表（已去重）:")
    for tile in sorted(list(all_tiles)):
        print(f"  - {tile}")
    print("-" * 80)


if __name__ == "__main__":
    main() 