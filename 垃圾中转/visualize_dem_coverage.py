import os
import glob
import re
import rasterio
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
from cartopy.feature import NaturalEarthFeature, BORDERS, COASTLINE
from matplotlib.patches import Rectangle

def get_hgt_bounds(filepath):
    """从.hgt文件名中解析出经纬度范围"""
    filename = os.path.basename(filepath)
    match = re.match(r'([NS])(\d{2})([EW])(\d{3})\.hgt', filename, re.IGNORECASE)
    if not match:
        return None

    lat_hem, lat_val, lon_hem, lon_val = match.groups()
    lat = int(lat_val)
    lon = int(lon_val)

    if lat_hem.upper() == 'S':
        lat = -lat
    if lon_hem.upper() == 'W':
        lon = -lon
    
    # .hgt文件定义了其左下角的坐标
    return (lon, lat, lon + 1, lat + 1)

def analyze_and_visualize_dem_coverage(dem_dir, output_image_path):
    """
    分析DEM文件的覆盖范围并生成可视化地图。
    """
    # 使用 glob 的 recursive=True 参数来搜索所有子目录
    hgt_files = glob.glob(os.path.join(dem_dir, '**/*.hgt'), recursive=True)
    
    if not hgt_files:
        print(f"在目录 '{dem_dir}' 及其子目录中未找到任何 .hgt 文件。")
        return

    all_bounds = [get_hgt_bounds(f) for f in hgt_files]
    all_bounds = [b for b in all_bounds if b is not None]

    if not all_bounds:
        print("无法从文件名中解析出任何有效的经纬度范围。")
        return

    min_lon = min(b[0] for b in all_bounds)
    min_lat = min(b[1] for b in all_bounds)
    max_lon = max(b[2] for b in all_bounds)
    max_lat = max(b[3] for b in all_bounds)

    print("--- DEM 数据覆盖范围 ---")
    print(f"经度范围: {min_lon}° E to {max_lon}° E")
    print(f"纬度范围: {min_lat}° N to {max_lat}° N")
    print("-------------------------")

    # --- 可视化 ---
    plt.rc('font', family='WenQuanYi Micro Hei')
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(1, 1, 1, projection=ccrs.PlateCarree())
    
    ax.set_extent([min_lon - 5, max_lon + 5, min_lat - 5, max_lat + 5], crs=ccrs.PlateCarree())

    # 添加地理特征
    ax.add_feature(COASTLINE)
    ax.add_feature(BORDERS, linestyle=':')
    ax.add_feature(NaturalEarthFeature('physical', 'ocean', '50m', edgecolor='face', facecolor='#0077BE'))
    ax.add_feature(NaturalEarthFeature('physical', 'land', '50m', edgecolor='face', facecolor='#DEB887'))

    # 绘制每个瓦片的矩形
    tile_count = 0
    for lon_min, lat_min, lon_max, lat_max in all_bounds:
        ax.add_patch(Rectangle((lon_min, lat_min), 1, 1,
                               facecolor='green',
                               alpha=0.5,
                               edgecolor='black',
                               transform=ccrs.PlateCarree()))
        tile_count += 1

    ax.gridlines(draw_labels=True, dms=True, x_inline=False, y_inline=False)
    ax.set_title(f"拥有的DEM数据瓦片地理覆盖范围 (共 {tile_count} 个)", fontsize=22)

    plt.savefig(output_image_path, dpi=300)
    print(f"\nDEM覆盖范围地图已保存到: {output_image_path}")
    plt.close()

if __name__ == "__main__":
    unzipped_dem_dir = 'data/DEM/unzipped'
    output_map_path = 'data/dem_coverage_map.png'
    os.makedirs(os.path.dirname(output_map_path), exist_ok=True)
    analyze_and_visualize_dem_coverage(unzipped_dem_dir, output_map_path) 