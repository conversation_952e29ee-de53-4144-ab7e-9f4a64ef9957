#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于物理运动学的轨迹生成脚本
严格遵守牛顿运动定律，将环境影响建模为力和加速度
"""

import os
import numpy as np
import pandas as pd
import rasterio
import math
from scipy.ndimage import gaussian_filter1d
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from collections import deque

# 物理常数
GRAVITY = 9.81  # m/s²
AIR_DENSITY = 1.225  # kg/m³

# 地表阻力系数 (基于土地覆盖类型)
SURFACE_RESISTANCE = {
    '10': 0.02,   # 耕地 - 较低阻力
    '20': 0.08,   # 林地 - 高阻力
    '30': 0.03,   # 草地 - 低阻力  
    '40': 0.06,   # 灌木地 - 中等阻力
    '50': 0.15,   # 水体 - 很高阻力
    '60': 0.01,   # 建设用地 - 很低阻力
    '80': 0.04,   # 未利用地1 - 中低阻力
    '90': 0.05,   # 未利用地2 - 中等阻力
    '255': 0.04   # 未知 - 默认阻力
}

# 目标速度 (用于计算驾驶意图)
TARGET_SPEEDS = {
    '10': 25.0,   # 耕地
    '20': 20.0,   # 林地
    '30': 27.0,   # 草地
    '40': 22.0,   # 灌木地
    '50': 12.0,   # 水体
    '60': 30.0,   # 建设用地
    '80': 24.0,   # 未利用地1
    '90': 23.0,   # 未利用地2
    '255': 22.0   # 未知
}

class VehiclePhysics:
    """车辆物理模型"""
    def __init__(self, mass=1800, max_power=150000, drag_coeff=0.35, frontal_area=2.5):
        self.mass = mass  # 质量 (kg)
        self.max_power = max_power  # 最大功率 (W)
        self.drag_coefficient = drag_coeff  # 风阻系数
        self.frontal_area = frontal_area  # 迎风面积 (m²)
        
        # 状态变量
        self.position = np.array([0.0, 0.0])  # 位置 (m)
        self.velocity = np.array([0.0, 0.0])  # 速度 (m/s)
        self.speed = 0.0  # 速度大小 (m/s)
        self.heading = 0.0  # 航向角 (度)
        
        # 历史记录
        self.speed_history = deque(maxlen=10)
        self.acceleration_history = deque(maxlen=5)

    def calculate_drag_force(self):
        """计算空气阻力"""
        if self.speed < 0.1:
            return 0.0
        return 0.5 * AIR_DENSITY * self.drag_coefficient * self.frontal_area * self.speed**2

    def calculate_rolling_resistance(self, surface_coeff):
        """计算滚动阻力"""
        normal_force = self.mass * GRAVITY
        return surface_coeff * normal_force

    def calculate_gravity_force(self, slope_angle, heading_to_slope_angle):
        """计算重力分量"""
        # 计算有效坡度（考虑运动方向）
        effective_slope = slope_angle * math.cos(math.radians(heading_to_slope_angle))
        return self.mass * GRAVITY * math.sin(math.radians(effective_slope))

    def calculate_max_traction_force(self):
        """计算最大牵引力（基于功率限制）"""
        if self.speed < 1.0:
            # 低速时受扭矩限制
            return 8000.0  # N，假设最大扭矩对应的牵引力
        else:
            # 高速时受功率限制
            return self.max_power / self.speed

    def calculate_desired_acceleration(self, target_speed, time_constant=3.0):
        """计算期望加速度（基于目标速度）"""
        speed_error = target_speed - self.speed
        # 使用一阶系统响应
        desired_accel = speed_error / time_constant
        return np.clip(desired_accel, -4.0, 3.0)  # 限制合理范围

    def update_physics(self, dt, target_speed, slope_angle, slope_aspect, surface_type):
        """基于物理定律更新车辆状态"""
        
        # 1. 计算各种阻力
        drag_force = self.calculate_drag_force()
        rolling_resistance = self.calculate_rolling_resistance(SURFACE_RESISTANCE.get(surface_type, 0.04))
        
        # 2. 计算重力分量
        heading_to_slope_angle = slope_aspect - self.heading
        gravity_force = self.calculate_gravity_force(slope_angle, heading_to_slope_angle)
        
        # 3. 计算期望加速度
        desired_accel = self.calculate_desired_acceleration(target_speed)
        
        # 4. 计算所需牵引力
        total_resistance = drag_force + rolling_resistance + gravity_force
        required_traction = self.mass * desired_accel + total_resistance
        
        # 5. 限制牵引力（物理约束）
        max_traction = self.calculate_max_traction_force()
        actual_traction = np.clip(required_traction, -max_traction*0.8, max_traction)
        
        # 6. 计算实际加速度
        net_force = actual_traction - total_resistance
        acceleration = net_force / self.mass
        
        # 7. 更新速度和位置
        self.speed = max(0, self.speed + acceleration * dt)
        
        # 更新速度矢量
        self.velocity[0] = self.speed * math.cos(math.radians(self.heading))  # 东向
        self.velocity[1] = self.speed * math.sin(math.radians(self.heading))  # 北向
        
        # 更新位置
        self.position += self.velocity * dt
        
        # 记录历史
        self.speed_history.append(self.speed)
        self.acceleration_history.append(acceleration)
        
        return {
            'speed': self.speed,
            'acceleration': acceleration,
            'traction_force': actual_traction,
            'drag_force': drag_force,
            'rolling_resistance': rolling_resistance,
            'gravity_force': gravity_force,
            'net_force': net_force
        }

class PhysicsTrajectoryGenerator:
    """基于物理的轨迹生成器"""
    
    def __init__(self, env_dir, target_speed_kmh=30.0):  # 降低默认速度到更合理的30km/h
        # 加载环境数据
        self.dem_file = os.path.join(env_dir, 'dem_aligned.tif')
        self.slope_file = os.path.join(env_dir, 'slope_aligned.tif')
        self.aspect_file = os.path.join(env_dir, 'aspect_aligned.tif')
        self.landcover_file = os.path.join(env_dir, 'landcover_aligned.tif')
        
        self.dem_data = rasterio.open(self.dem_file)
        self.slope_data = rasterio.open(self.slope_file)
        self.aspect_data = rasterio.open(self.aspect_file)
        self.landcover_data = rasterio.open(self.landcover_file)
        
        self.transform = self.dem_data.transform
        self.resolution = self.dem_data.res
        
        # 调整目标速度 - 基于真实数据校准
        self.speed_scale = (target_speed_kmh / 3.6) / 22.0  # 基于真实轨迹的平均速度校准
        
        print(f"物理轨迹生成器初始化完成")
        print(f"目标速度: {target_speed_kmh:.1f} km/h")
        print(f"速度缩放因子: {self.speed_scale:.3f}")

    def grid_to_utm(self, grid_x, grid_y):
        """栅格坐标转UTM坐标"""
        utm_x, utm_y = self.transform * (grid_x, grid_y)
        return utm_x, utm_y

    def get_env_features(self, grid_x, grid_y):
        """获取环境特征"""
        try:
            # 确保坐标在范围内
            grid_x = int(np.clip(grid_x, 0, self.dem_data.width - 1))
            grid_y = int(np.clip(grid_y, 0, self.dem_data.height - 1))
            
            # 读取数据
            dem_val = self.dem_data.read(1)[grid_y, grid_x]
            slope_val = self.slope_data.read(1)[grid_y, grid_x]
            aspect_val = self.aspect_data.read(1)[grid_y, grid_x]
            lc_val = self.landcover_data.read(1)[grid_y, grid_x]
            
            return {
                'elevation': float(dem_val),
                'slope': float(slope_val),
                'aspect': float(aspect_val),
                'landcover': str(int(lc_val))
            }
        except:
            return {
                'elevation': 0, 'slope': 0, 'aspect': 0, 'landcover': '255'
            }

    def calculate_heading(self, path_points, index, look_ahead=3):
        """计算航向角"""
        if index >= len(path_points) - look_ahead:
            if index > 0:
                dx = path_points[index][0] - path_points[index-1][0]
                dy = path_points[index][1] - path_points[index-1][1]
            else:
                return 0
        else:
            dx = path_points[index + look_ahead][0] - path_points[index][0]
            dy = path_points[index + look_ahead][1] - path_points[index][1]
        
        heading = math.degrees(math.atan2(dy, dx))
        return heading

    def interpolate_path(self, path_points, max_dist=60):
        """路径插值"""
        interpolated_points = []
        
        for i in range(len(path_points) - 1):
            p1 = path_points[i]
            p2 = path_points[i + 1]
            
            grid_dist = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            real_dist = grid_dist * self.resolution[0]
            
            interpolated_points.append(p1)
            
            if real_dist > max_dist:
                n_points = int(real_dist / max_dist)
                for j in range(1, n_points):
                    ratio = j / n_points
                    interp_x = int(p1[0] + (p2[0] - p1[0]) * ratio)
                    interp_y = int(p1[1] + (p2[1] - p1[1]) * ratio)
                    interpolated_points.append([interp_x, interp_y])
        
        interpolated_points.append(path_points[-1])
        return np.array(interpolated_points)

    def generate_trajectory(self, path_points, goal_id, output_file=None, dt=1.0):
        """生成基于物理的轨迹"""
        
        print(f"开始生成物理轨迹，共{len(path_points)}个路径点")
        
        # 插值路径
        path_points = self.interpolate_path(path_points)
        print(f"插值后路径点数量: {len(path_points)}")
        
        # 初始化车辆
        vehicle = VehiclePhysics()
        
        # 设置初始位置
        start_utm = self.grid_to_utm(path_points[0][0], path_points[0][1])
        vehicle.position = np.array(start_utm)
        
        # 轨迹数据存储
        trajectory_data = {
            'timestamp_ms': [],
            'x': [], 'y': [],
            'velocity_north_ms': [], 'velocity_east_ms': [],
            'heading_deg': [],
            'acceleration_x_ms2': [], 'acceleration_y_ms2': [],
            'goal_id': [],
            # 物理量
            'speed_ms': [],
            'acceleration_ms2': [],
            'traction_force_N': [],
            'drag_force_N': [],
            'rolling_resistance_N': [],
            'gravity_force_N': []
        }
        
        current_time = 0
        
        for i in range(len(path_points)):
            # 显示进度
            if i % 500 == 0:
                print(f"处理进度: {i}/{len(path_points)} ({i/len(path_points)*100:.1f}%)")
            
            grid_x, grid_y = path_points[i]
            
            # 获取环境特征
            env_features = self.get_env_features(grid_x, grid_y)
            
            # 计算航向
            heading = self.calculate_heading(path_points, i)
            vehicle.heading = heading
            
            # 获取目标速度
            landcover = env_features['landcover']
            target_speed = TARGET_SPEEDS.get(landcover, 22.0) * self.speed_scale
            
            # 物理更新
            physics_result = vehicle.update_physics(
                dt=dt,
                target_speed=target_speed,
                slope_angle=env_features['slope'],
                slope_aspect=env_features['aspect'],
                surface_type=landcover
            )
            
            # 更新位置到路径点
            target_utm = self.grid_to_utm(grid_x, grid_y)
            vehicle.position = np.array(target_utm)
            
            # 记录数据
            trajectory_data['timestamp_ms'].append(current_time * 1000)
            trajectory_data['x'].append(vehicle.position[0])
            trajectory_data['y'].append(vehicle.position[1])
            trajectory_data['velocity_north_ms'].append(vehicle.velocity[1])
            trajectory_data['velocity_east_ms'].append(vehicle.velocity[0])
            trajectory_data['heading_deg'].append(heading)
            
            # 计算加速度分量
            if len(vehicle.acceleration_history) >= 2:
                accel = vehicle.acceleration_history[-1]
                ax = accel * math.cos(math.radians(heading))
                ay = accel * math.sin(math.radians(heading))
            else:
                ax = ay = 0
                
            trajectory_data['acceleration_x_ms2'].append(ax)
            trajectory_data['acceleration_y_ms2'].append(ay)
            trajectory_data['goal_id'].append(goal_id)
            
            # 物理量
            trajectory_data['speed_ms'].append(physics_result['speed'])
            trajectory_data['acceleration_ms2'].append(physics_result['acceleration'])
            trajectory_data['traction_force_N'].append(physics_result['traction_force'])
            trajectory_data['drag_force_N'].append(physics_result['drag_force'])
            trajectory_data['rolling_resistance_N'].append(physics_result['rolling_resistance'])
            trajectory_data['gravity_force_N'].append(physics_result['gravity_force'])
            
            current_time += dt
        
        # 创建DataFrame
        df = pd.DataFrame(trajectory_data)
        
        # 平滑处理
        df['velocity_north_ms'] = gaussian_filter1d(df['velocity_north_ms'], sigma=0.8)
        df['velocity_east_ms'] = gaussian_filter1d(df['velocity_east_ms'], sigma=0.8)
        df['acceleration_x_ms2'] = gaussian_filter1d(df['acceleration_x_ms2'], sigma=1.2)
        df['acceleration_y_ms2'] = gaussian_filter1d(df['acceleration_y_ms2'], sigma=1.2)
        
        # 保存文件
        if output_file:
            df.to_csv(output_file, index=False)
            print(f"物理轨迹已保存: {output_file}")
            
            # 生成分析图
            self.plot_physics_analysis(df, output_file.replace('.csv', '_physics_analysis.png'))
        
        avg_speed = df['speed_ms'].mean()
        print(f"物理轨迹生成完成，平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)")
        
        return df

    def plot_physics_analysis(self, df, output_file):
        """绘制物理分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        time_sec = df['timestamp_ms'] / 1000
        
        # 1. 速度和加速度
        ax1 = axes[0, 0]
        ax1.plot(time_sec, df['speed_ms'], 'b-', linewidth=2, label='速度')
        ax1_twin = ax1.twinx()
        ax1_twin.plot(time_sec, df['acceleration_ms2'], 'r-', linewidth=1, alpha=0.7, label='加速度')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('速度 (m/s)', color='blue')
        ax1_twin.set_ylabel('加速度 (m/s²)', color='red')
        ax1.set_title('速度和加速度时间序列')
        ax1.grid(True, alpha=0.3)
        
        # 2. 力的分析
        ax2 = axes[0, 1]
        ax2.plot(time_sec, df['traction_force_N'], label='牵引力', linewidth=2)
        ax2.plot(time_sec, df['drag_force_N'], label='空气阻力', linewidth=2)
        ax2.plot(time_sec, df['rolling_resistance_N'], label='滚动阻力', linewidth=2)
        ax2.plot(time_sec, df['gravity_force_N'], label='重力分量', linewidth=2)
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('力 (N)')
        ax2.set_title('各种力的变化')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 速度分布
        ax3 = axes[1, 0]
        ax3.hist(df['speed_ms'], bins=50, alpha=0.7, edgecolor='black')
        ax3.set_xlabel('速度 (m/s)')
        ax3.set_ylabel('频次')
        ax3.set_title('速度分布')
        ax3.grid(True, alpha=0.3)
        
        # 4. 轨迹路径
        ax4 = axes[1, 1]
        scatter = ax4.scatter(df['x'], df['y'], c=df['speed_ms'], cmap='viridis', s=1)
        ax4.set_xlabel('X坐标 (m)')
        ax4.set_ylabel('Y坐标 (m)')
        ax4.set_title('轨迹路径（颜色表示速度）')
        plt.colorbar(scatter, ax=ax4, label='速度 (m/s)')
        ax4.axis('equal')
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"物理分析图已保存: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="基于物理的轨迹生成")
    parser.add_argument('--path_dir', default='gen_exp/standard', help='路径文件目录')
    parser.add_argument('--env_dir', default='trajectory_generation_module_pkg/examples/data/environment', help='环境数据目录')
    parser.add_argument('--output_dir', default='test_output_physics', help='输出目录')
    parser.add_argument('--num_files', type=int, default=1, help='处理文件数量')
    parser.add_argument('--target_speed_kmh', type=float, default=30.0, help='目标速度(km/h)')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化生成器
    generator = PhysicsTrajectoryGenerator(args.env_dir, args.target_speed_kmh)
    
    # 处理文件
    path_files = list(Path(args.path_dir).glob('*.npy'))[:args.num_files]
    
    for i, path_file in enumerate(path_files):
        print(f"\n[{i+1}/{len(path_files)}] 处理文件: {path_file.name}")
        
        try:
            # 加载路径
            path_points = np.load(path_file)
            
            # 解析文件名
            parts = path_file.stem.split('_')
            origin_id = parts[1]
            goal_id = f"{parts[2]}_physics"
            
            # 生成轨迹
            output_csv = os.path.join(args.output_dir, f"trajectory_{origin_id}_{goal_id}.csv")
            generator.generate_trajectory(path_points, goal_id, output_csv)
            
        except Exception as e:
            print(f"处理失败: {e}")

if __name__ == '__main__':
    main()
