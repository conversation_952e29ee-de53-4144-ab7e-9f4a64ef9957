#!/usr/bin/env python3
"""
轨迹仿真与真实轨迹对比分析
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
import logging
from typing import Dict, List, Tuple

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from smart_trajectory_simulator import AccelerationDrivenSimulator
from environment_speed_model import EnvironmentSpeedModel
from config import Config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrajectoryComparisonAnalyzer:
    """轨迹对比分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.config = Config()
        self.simulator = AccelerationDrivenSimulator()
        
        # 创建结果目录
        os.makedirs("results/trajectory_comparison", exist_ok=True)
    
    def load_real_trajectory(self, trajectory_file: str) -> pd.DataFrame:
        """加载真实轨迹数据"""
        logger.info(f"加载真实轨迹: {trajectory_file}")
        
        try:
            df = pd.read_csv(trajectory_file)
            # 确保必要的列存在
            required_columns = ['timestamp_ms', 'latitude', 'longitude', 'velocity_2d_ms', 'heading_deg']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"轨迹文件缺少必要的列: {missing_columns}")
                return None
            
            # 重命名列以便统一处理
            if 'latitude' in df.columns and 'longitude' in df.columns:
                df.rename(columns={'latitude': 'utm_y', 'longitude': 'utm_x'}, inplace=True)
            
            # 计算时间差
            df['timestamp_s'] = df['timestamp_ms'] / 1000.0
            df['time_diff'] = df['timestamp_s'] - df['timestamp_s'].iloc[0]
            
            # 计算累计距离
            dx = df['utm_x'].diff()
            dy = df['utm_y'].diff()
            df['segment_distance'] = np.sqrt(dx**2 + dy**2)
            df['cumulative_distance'] = df['segment_distance'].cumsum().fillna(0)
            
            logger.info(f"成功加载轨迹，共 {len(df)} 个点，总距离 {df['cumulative_distance'].iloc[-1]:.2f} 米")
            return df
            
        except Exception as e:
            logger.error(f"加载轨迹文件失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def simulate_trajectory(self, real_trajectory: pd.DataFrame) -> List[Dict]:
        """基于真实轨迹的初始状态和路径进行仿真"""
        logger.info("开始轨迹仿真...")
        
        # 获取真实轨迹的初始状态
        initial_state = real_trajectory.iloc[0]
        
        # 设置仿真器的初始状态
        self.simulator.set_initial_state(
            utm_x=initial_state['utm_x'],
            utm_y=initial_state['utm_y'],
            speed_2d=initial_state['velocity_2d_ms'],
            heading_deg=initial_state['heading_deg'],
            timestamp=initial_state['timestamp_ms'] / 1000.0
        )
        
        # 将真实轨迹保存为临时文件，用于提取路径
        temp_trajectory_file = "results/trajectory_comparison/temp_real_trajectory.csv"
        real_trajectory.to_csv(temp_trajectory_file, index=False)
        
        # 从真实轨迹提取路径
        self.simulator.set_path_from_real_trajectory(temp_trajectory_file)
        
        # 运行仿真
        max_steps = min(len(real_trajectory), 1000)  # 限制步数，避免过长
        simulated_trajectory = self.simulator.run_simulation(max_steps=max_steps)
        
        # 删除临时文件
        if os.path.exists(temp_trajectory_file):
            os.remove(temp_trajectory_file)
        
        logger.info(f"仿真完成，生成 {len(simulated_trajectory)} 个轨迹点")
        return simulated_trajectory
    
    def compare_trajectories(self, real_trajectory: pd.DataFrame, simulated_trajectory: List[Dict], 
                             output_prefix: str = "trajectory_comparison"):
        """比较真实轨迹和仿真轨迹"""
        logger.info("开始轨迹对比分析...")
        
        # 将仿真轨迹转换为DataFrame
        sim_df = pd.DataFrame(simulated_trajectory)
        
        # 重采样真实轨迹以匹配仿真轨迹的时间步长
        real_df = real_trajectory.copy()
        real_df['timestamp'] = real_df['timestamp_ms'] / 1000.0
        
        # 确保两个轨迹的时间起点相同
        sim_start_time = sim_df['timestamp'].iloc[0]
        real_start_time = real_df['timestamp'].iloc[0]
        sim_df['time_diff'] = sim_df['timestamp'] - sim_start_time
        real_df['time_diff'] = real_df['timestamp'] - real_start_time
        
        # 限制分析的最大时间范围
        max_time = min(sim_df['time_diff'].max(), real_df['time_diff'].max())
        sim_df = sim_df[sim_df['time_diff'] <= max_time]
        real_df = real_df[real_df['time_diff'] <= max_time]
        
        # 生成轨迹对比图
        self._plot_trajectory_comparison(real_df, sim_df, output_prefix)
        
        # 生成速度对比图
        self._plot_speed_comparison(real_df, sim_df, output_prefix)
        
        # 计算统计指标
        self._calculate_comparison_metrics(real_df, sim_df, output_prefix)
    
    def _plot_trajectory_comparison(self, real_df: pd.DataFrame, sim_df: pd.DataFrame, output_prefix: str):
        """生成轨迹对比图"""
        plt.figure(figsize=(12, 10))
        
        # 主轨迹图
        plt.subplot(2, 1, 1)
        plt.plot(real_df['utm_x'], real_df['utm_y'], 'b-', linewidth=2, label='真实轨迹')
        plt.plot(sim_df['utm_x'], sim_df['utm_y'], 'r-', linewidth=2, label='仿真轨迹')
        plt.scatter(real_df['utm_x'].iloc[0], real_df['utm_y'].iloc[0], c='g', s=100, marker='o', label='起点')
        plt.scatter(real_df['utm_x'].iloc[-1], real_df['utm_y'].iloc[-1], c='m', s=100, marker='x', label='终点')
        plt.xlabel('UTM X (m)')
        plt.ylabel('UTM Y (m)')
        plt.title('轨迹对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        
        # 轨迹偏差图
        plt.subplot(2, 1, 2)
        
        # 计算每个时间点的最近点偏差
        deviations = []
        sample_points = np.linspace(0, len(sim_df)-1, min(100, len(sim_df))).astype(int)
        
        for i in sample_points:
            sim_point = sim_df.iloc[i]
            sim_time = sim_point['time_diff']
            
            # 找到真实轨迹中最接近的时间点
            real_idx = (real_df['time_diff'] - sim_time).abs().argmin()
            real_point = real_df.iloc[real_idx]
            
            # 计算距离偏差
            deviation = np.sqrt((sim_point['utm_x'] - real_point['utm_x'])**2 + 
                               (sim_point['utm_y'] - real_point['utm_y'])**2)
            deviations.append((sim_time, deviation))
        
        deviations = np.array(deviations)
        plt.plot(deviations[:, 0], deviations[:, 1], 'g-', linewidth=2)
        plt.xlabel('时间 (秒)')
        plt.ylabel('轨迹偏差 (米)')
        plt.title('轨迹偏差随时间变化')
        plt.grid(True, alpha=0.3)
        
        # 保存图表
        plt.tight_layout()
        plt.savefig(f'results/trajectory_comparison/{output_prefix}_path.png', dpi=300, bbox_inches='tight')
        logger.info(f"轨迹对比图已保存: results/trajectory_comparison/{output_prefix}_path.png")
        plt.close()
    
    def _plot_speed_comparison(self, real_df: pd.DataFrame, sim_df: pd.DataFrame, output_prefix: str):
        """生成速度对比图"""
        plt.figure(figsize=(12, 10))
        
        # 速度对比图
        plt.subplot(2, 1, 1)
        plt.plot(real_df['time_diff'], real_df['velocity_2d_ms'], 'b-', linewidth=2, label='真实速度')
        plt.plot(sim_df['time_diff'], sim_df['speed_2d'], 'r-', linewidth=2, label='仿真速度')
        plt.xlabel('时间 (秒)')
        plt.ylabel('速度 (m/s)')
        plt.title('速度对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 速度差异图
        plt.subplot(2, 1, 2)
        
        # 在相同时间点比较速度差异
        speed_diffs = []
        sample_points = np.linspace(0, len(sim_df)-1, min(100, len(sim_df))).astype(int)
        
        for i in sample_points:
            sim_point = sim_df.iloc[i]
            sim_time = sim_point['time_diff']
            
            # 找到真实轨迹中最接近的时间点
            real_idx = (real_df['time_diff'] - sim_time).abs().argmin()
            real_point = real_df.iloc[real_idx]
            
            # 计算速度差异
            speed_diff = sim_point['speed_2d'] - real_point['velocity_2d_ms']
            speed_diffs.append((sim_time, speed_diff))
        
        speed_diffs = np.array(speed_diffs)
        plt.plot(speed_diffs[:, 0], speed_diffs[:, 1], 'g-', linewidth=2)
        plt.axhline(y=0, color='k', linestyle='--')
        plt.xlabel('时间 (秒)')
        plt.ylabel('速度差异 (m/s)')
        plt.title('速度差异随时间变化')
        plt.grid(True, alpha=0.3)
        
        # 保存图表
        plt.tight_layout()
        plt.savefig(f'results/trajectory_comparison/{output_prefix}_speed.png', dpi=300, bbox_inches='tight')
        logger.info(f"速度对比图已保存: results/trajectory_comparison/{output_prefix}_speed.png")
        plt.close()
    
    def _calculate_comparison_metrics(self, real_df: pd.DataFrame, sim_df: pd.DataFrame, output_prefix: str):
        """计算对比指标"""
        # 轨迹偏差统计
        deviations = []
        sample_points = np.linspace(0, len(sim_df)-1, min(500, len(sim_df))).astype(int)
        
        for i in sample_points:
            sim_point = sim_df.iloc[i]
            sim_time = sim_point['time_diff']
            
            # 找到真实轨迹中最接近的时间点
            real_idx = (real_df['time_diff'] - sim_time).abs().argmin()
            real_point = real_df.iloc[real_idx]
            
            # 计算距离偏差
            deviation = np.sqrt((sim_point['utm_x'] - real_point['utm_x'])**2 + 
                               (sim_point['utm_y'] - real_point['utm_y'])**2)
            deviations.append(deviation)
        
        # 速度差异统计
        speed_diffs = []
        for i in sample_points:
            sim_point = sim_df.iloc[i]
            sim_time = sim_point['time_diff']
            
            # 找到真实轨迹中最接近的时间点
            real_idx = (real_df['time_diff'] - sim_time).abs().argmin()
            real_point = real_df.iloc[real_idx]
            
            # 计算速度差异
            speed_diff = abs(sim_point['speed_2d'] - real_point['velocity_2d_ms'])
            speed_diffs.append(speed_diff)
        
        # 计算统计指标
        metrics = {
            "轨迹总长度(米)": real_df['cumulative_distance'].iloc[-1],
            "轨迹持续时间(秒)": real_df['time_diff'].iloc[-1],
            "平均轨迹偏差(米)": np.mean(deviations),
            "最大轨迹偏差(米)": np.max(deviations),
            "轨迹偏差标准差(米)": np.std(deviations),
            "平均速度差异(m/s)": np.mean(speed_diffs),
            "最大速度差异(m/s)": np.max(speed_diffs),
            "速度差异标准差(m/s)": np.std(speed_diffs),
            "真实轨迹平均速度(m/s)": real_df['velocity_2d_ms'].mean(),
            "仿真轨迹平均速度(m/s)": sim_df['speed_2d'].mean()
        }
        
        # 保存指标到文件
        with open(f'results/trajectory_comparison/{output_prefix}_metrics.txt', 'w') as f:
            f.write("轨迹对比分析指标\n")
            f.write("=" * 50 + "\n\n")
            for key, value in metrics.items():
                f.write(f"{key}: {value:.4f}\n")
        
        logger.info(f"对比指标已保存: results/trajectory_comparison/{output_prefix}_metrics.txt")
        
        # 打印主要指标
        logger.info(f"平均轨迹偏差: {metrics['平均轨迹偏差(米)']:.2f} 米")
        logger.info(f"平均速度差异: {metrics['平均速度差异(m/s)']:.2f} m/s")
    
    def run_analysis_for_trajectory(self, trajectory_file: str):
        """对单个轨迹文件运行完整分析"""
        # 提取轨迹编号
        trajectory_name = os.path.basename(trajectory_file).replace('.csv', '')
        logger.info(f"开始分析轨迹: {trajectory_name}")
        
        # 加载真实轨迹
        real_trajectory = self.load_real_trajectory(trajectory_file)
        if real_trajectory is None:
            logger.error("轨迹加载失败，跳过分析")
            return
        
        # 运行仿真
        simulated_trajectory = self.simulate_trajectory(real_trajectory)
        if not simulated_trajectory:
            logger.error("轨迹仿真失败，跳过分析")
            return
        
        # 比较轨迹
        self.compare_trajectories(real_trajectory, simulated_trajectory, trajectory_name)
        
        logger.info(f"轨迹 {trajectory_name} 分析完成")
    
    def run_batch_analysis(self, trajectory_dir: str = "../trajectories"):
        """批量分析目录中的所有轨迹"""
        logger.info(f"开始批量分析轨迹目录: {trajectory_dir}")
        
        # 获取目录中的所有CSV文件
        trajectory_files = []
        for file in os.listdir(trajectory_dir):
            if file.endswith('.csv') and 'trajectory' in file.lower():
                trajectory_files.append(os.path.join(trajectory_dir, file))
        
        if not trajectory_files:
            logger.error(f"在 {trajectory_dir} 中未找到轨迹文件")
            return
        
        logger.info(f"找到 {len(trajectory_files)} 个轨迹文件")
        
        # 分析每个轨迹
        for i, trajectory_file in enumerate(trajectory_files):
            logger.info(f"处理轨迹 {i+1}/{len(trajectory_files)}: {os.path.basename(trajectory_file)}")
            self.run_analysis_for_trajectory(trajectory_file)
        
        logger.info("批量分析完成")
        
        # 生成汇总报告
        self._generate_summary_report(trajectory_files)
    
    def _generate_summary_report(self, trajectory_files: List[str]):
        """生成汇总报告"""
        logger.info("生成汇总报告...")
        
        # 收集所有轨迹的指标
        all_metrics = []
        for trajectory_file in trajectory_files:
            trajectory_name = os.path.basename(trajectory_file).replace('.csv', '')
            metrics_file = f'results/trajectory_comparison/{trajectory_name}_metrics.txt'
            
            if not os.path.exists(metrics_file):
                continue
            
            metrics = {}
            with open(metrics_file, 'r') as f:
                for line in f:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        try:
                            metrics[key.strip()] = float(value.strip())
                        except:
                            pass
            
            if metrics:
                metrics['轨迹名称'] = trajectory_name
                all_metrics.append(metrics)
        
        if not all_metrics:
            logger.warning("未找到任何指标文件，无法生成汇总报告")
            return
        
        # 创建汇总表格
        metrics_df = pd.DataFrame(all_metrics)
        
        # 保存为CSV
        metrics_df.to_csv('results/trajectory_comparison/summary_metrics.csv', index=False)
        
        # 生成汇总图表
        self._plot_summary_charts(metrics_df)
        
        logger.info("汇总报告已生成: results/trajectory_comparison/summary_metrics.csv")
    
    def _plot_summary_charts(self, metrics_df: pd.DataFrame):
        """生成汇总图表"""
        plt.figure(figsize=(15, 10))
        
        gs = GridSpec(2, 2, figure=plt.gcf())
        
        # 轨迹偏差对比
        ax1 = plt.subplot(gs[0, 0])
        metrics_df.plot(x='轨迹名称', y='平均轨迹偏差(米)', kind='bar', ax=ax1, color='blue')
        ax1.set_title('各轨迹平均偏差对比')
        ax1.set_ylabel('平均偏差 (米)')
        ax1.grid(True, alpha=0.3)
        
        # 速度差异对比
        ax2 = plt.subplot(gs[0, 1])
        metrics_df.plot(x='轨迹名称', y='平均速度差异(m/s)', kind='bar', ax=ax2, color='green')
        ax2.set_title('各轨迹平均速度差异对比')
        ax2.set_ylabel('平均速度差异 (m/s)')
        ax2.grid(True, alpha=0.3)
        
        # 真实vs仿真速度对比
        ax3 = plt.subplot(gs[1, :])
        width = 0.35
        x = np.arange(len(metrics_df))
        ax3.bar(x - width/2, metrics_df['真实轨迹平均速度(m/s)'], width, label='真实轨迹')
        ax3.bar(x + width/2, metrics_df['仿真轨迹平均速度(m/s)'], width, label='仿真轨迹')
        ax3.set_title('真实vs仿真平均速度对比')
        ax3.set_ylabel('平均速度 (m/s)')
        ax3.set_xticks(x)
        ax3.set_xticklabels(metrics_df['轨迹名称'])
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('results/trajectory_comparison/summary_charts.png', dpi=300, bbox_inches='tight')
        logger.info("汇总图表已保存: results/trajectory_comparison/summary_charts.png")
        plt.close()

def main():
    """主函数"""
    logger.info("开始轨迹仿真与对比分析")
    
    analyzer = TrajectoryComparisonAnalyzer()
    
    # 检查轨迹目录
    trajectory_dir = "../trajectories"
    if not os.path.exists(trajectory_dir):
        trajectory_dir = "trajectories"
        if not os.path.exists(trajectory_dir):
            logger.error("未找到轨迹目录，请确保轨迹文件位于 trajectories 目录中")
            return
    
    # 运行批量分析
    analyzer.run_batch_analysis(trajectory_dir)
    
    logger.info("轨迹仿真与对比分析完成")

if __name__ == "__main__":
    main() 