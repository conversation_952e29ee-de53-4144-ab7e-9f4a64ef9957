"""
项目配置文件
集中管理所有参数设置
"""

import os

# ==================== 路径配置 ====================
# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 数据目录
DATA_DIR = os.path.join(PROJECT_ROOT, 'data')
ENV_DATA_DIR = os.path.join(DATA_DIR, 'environment')
PATHS_DIR = os.path.join(DATA_DIR, 'paths')
TRAJECTORIES_DIR = os.path.join(DATA_DIR, 'trajectories')

# 输出目录
OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'output')
RESULTS_DIR = os.path.join(OUTPUT_DIR, 'results')
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')

# 环境数据文件
DEM_FILE = os.path.join(ENV_DATA_DIR, 'dem_aligned.tif')
SLOPE_FILE = os.path.join(ENV_DATA_DIR, 'slope_aligned.tif')
ASPECT_FILE = os.path.join(ENV_DATA_DIR, 'aspect_aligned.tif')
LANDCOVER_FILE = os.path.join(ENV_DATA_DIR, 'landcover_aligned.tif')

# ==================== 轨迹生成参数 ====================
# 目标速度设置
TARGET_SPEED_KMH = 75.0  # 目标平均速度 (km/h)
REFERENCE_SPEED_MS = 20.8  # 基准速度 (m/s)

# 时间设置
TIME_STEP_MS = 1000  # 时间步长 (毫秒)
SIMULATION_DT = 0.25  # 仿真时间步长 (秒)

# 速度限制
MIN_SPEED = 1.0  # 最小速度 (m/s)
MAX_SPEED = 35.0  # 最大速度 (m/s)
MAX_ACCELERATION = 3.0  # 最大加速度 (m/s²)

# ==================== 地表类型速度模型 ====================
SPEED_MODELS = {
    '10': {  # 耕地
        'base_speed': 26.0,  # m/s
        'max_speed': 32.0,
        'min_speed': 20.0,
        'transition_weight': 0.3
    },
    '20': {  # 林地
        'base_speed': 23.0,
        'max_speed': 28.0,
        'min_speed': 18.0,
        'transition_weight': 0.2
    },
    '30': {  # 草地
        'base_speed': 27.0,
        'max_speed': 33.0,
        'min_speed': 21.0,
        'transition_weight': 0.3
    },
    '40': {  # 灌木地
        'base_speed': 21.0,
        'max_speed': 26.0,
        'min_speed': 17.0,
        'transition_weight': 0.2
    },
    '50': {  # 水体
        'base_speed': 12.0,
        'max_speed': 16.0,
        'min_speed': 8.0,
        'transition_weight': 0.3
    },
    '60': {  # 建设用地
        'base_speed': 28.0,
        'max_speed': 35.0,
        'min_speed': 22.0,
        'transition_weight': 0.3
    },
    '80': {  # 未利用地1
        'base_speed': 25.0,
        'max_speed': 30.0,
        'min_speed': 19.0,
        'transition_weight': 0.2
    },
    '90': {  # 未利用地2
        'base_speed': 24.0,
        'max_speed': 29.0,
        'min_speed': 18.0,
        'transition_weight': 0.2
    },
    '255': {  # 未知类型
        'base_speed': 22.0,
        'max_speed': 27.0,
        'min_speed': 17.0,
        'transition_weight': 0.2
    }
}

# ==================== 坡度影响参数 ====================
SLOPE_EFFECTS = {
    'uphill': {
        'threshold': 6.0,  # 上坡阈值
        'base_factor': 0.85,  # 基础影响因子
        'steep_factor': 0.75  # 陡坡影响因子
    },
    'downhill': {
        'threshold': -10.0,  # 下坡阈值
        'base_factor': 1.18,  # 基础影响因子
        'steep_factor': 1.12  # 陡坡影响因子
    }
}

# ==================== 曲率影响参数 ====================
CURVATURE_PARAMS = {
    'factor': 0.65,  # 曲率影响因子
    'threshold': 4.0,  # 曲率阈值
    'power': 1.8,  # 曲率影响幂次
    'min_factor': 0.4  # 最小影响因子
}

# ==================== 轨迹生成控制参数 ====================
GENERATION_PARAMS = {
    'global_speed_multiplier': 0.8,
    'max_turn_rate': 25.0,
    'turn_p_gain': 0.6,
    'waypoint_arrival_threshold': 5.0,
    'curvature_factor': 0.65,
    'use_original_speeds': True
}

# ==================== 运动模式配置 ====================
MOTION_MODES = {
    'standard': {
        'suffix': 'standard',
        'description': '标准运动模式',
        'speed_multiplier': 1.0
    },
    'high_mobility': {
        'suffix': 'high_mobility', 
        'description': '高机动性模式',
        'speed_multiplier': 1.2
    },
    'stealth_priority': {
        'suffix': 'stealth_priority',
        'description': '隐蔽性优先模式',
        'speed_multiplier': 0.8
    },
    'mountain_special': {
        'suffix': 'mountain_special',
        'description': '山地特殊模式',
        'speed_multiplier': 0.9
    }
}

# ==================== 土地覆盖类型映射 ====================
LANDCOVER_MAPPING = {
    10: '水域',
    20: '湿地', 
    30: '草地',
    40: '灌木地',
    50: '建筑用地',
    60: '农田',
    80: '森林',
    90: '荒地',
    255: '未分类'
}

# ==================== 可视化配置 ====================
VISUALIZATION = {
    'enable': True,
    'save_plots': True,
    'plot_dpi': 300,
    'figure_size': (12, 8),
    'font_size': 14,
    'colors': {
        'trajectory': '#1f77b4',
        'path': '#ff7f0e', 
        'speed': '#2ca02c',
        'acceleration': '#d62728'
    }
}

# ==================== 日志配置 ====================
LOGGING = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'trajectory_generation.log'
}

# ==================== 处理配置 ====================
PROCESSING = {
    'default_num_files': 10,
    'max_path_points': 10000,  # 最大路径点数
    'interpolation_max_dist': 60,  # 插值最大距离
    'smoothing_sigma': 1.0,  # 平滑参数
    'batch_size': 100  # 批处理大小
}

# ==================== 验证配置 ====================
VALIDATION = {
    'speed_correlation_threshold': 0.9,
    'hausdorff_distance_threshold': 1000,
    'rmse_threshold': 2.0,
    'mae_threshold': 1.5
}

def get_paths_dir(mode='high_mobility'):
    """获取指定模式的路径目录"""
    if mode == 'high_mobility':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/high_mobility'
    elif mode == 'standard':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/standard'
    elif mode == 'stealth_priority':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/stealth_priority'
    elif mode == 'mountain_special':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/mountain_special'
    else:
        return PATHS_DIR

def get_env_data_dir():
    """获取环境数据目录"""
    return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/environment'

def get_output_dir(mode='high_mobility'):
    """获取输出目录"""
    if mode == 'high_mobility':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories'
    elif mode == 'standard':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories_standard'
    elif mode == 'stealth_priority':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories_stealth_priority'
    elif mode == 'mountain_special':
        return '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories_mountain_special'
    else:
        return OUTPUT_DIR 