#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
混合轨迹生成器：环境映射 + 初始条件 + 加速度约束
保持环境映射的速度曲线特征，同时考虑初始速度和物理约束
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import rasterio
import argparse
from pathlib import Path
import math
from scipy.ndimage import gaussian_filter1d
from collections import deque

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 地类速度映射 (基于真实数据校准)
TARGET_SPEEDS = {
    '10': 8.0,   # 水域 - 较慢
    '20': 12.0,  # 湿地 - 中等
    '30': 15.0,  # 草地 - 较快
    '40': 10.0,  # 灌木地 - 较慢  
    '50': 18.0,  # 建设用地 - 最快
    '60': 14.0,  # 农田 - 中等
    '80': 8.0,   # 林地 - 较慢
    '90': 12.0,  # 荒地 - 中等
    '255': 10.0  # 未分类 - 默认
}

class HybridTrajectoryGenerator:
    """混合轨迹生成器"""
    
    def __init__(self, env_dir, speed_scale=0.4):
        # 加载环境数据
        self.dem_file = os.path.join(env_dir, 'dem_aligned.tif')
        self.slope_file = os.path.join(env_dir, 'slope_aligned.tif')
        self.aspect_file = os.path.join(env_dir, 'aspect_aligned.tif')
        self.landcover_file = os.path.join(env_dir, 'landcover_aligned.tif')
        
        self.dem_data = rasterio.open(self.dem_file)
        self.slope_data = rasterio.open(self.slope_file)
        self.aspect_data = rasterio.open(self.aspect_file)
        self.landcover_data = rasterio.open(self.landcover_file)
        
        self.transform = self.dem_data.transform
        self.resolution = self.dem_data.res
        
        # 速度缩放因子
        self.speed_scale = speed_scale
        
        # 物理约束参数
        self.max_acceleration = 2.5  # m/s²
        self.max_deceleration = 4.0  # m/s²
        
        print(f"混合轨迹生成器初始化完成，速度缩放因子: {self.speed_scale}")

    def grid_to_utm(self, grid_x, grid_y):
        """栅格坐标转UTM坐标"""
        utm_x, utm_y = self.transform * (grid_x, grid_y)
        return utm_x, utm_y

    def get_env_features(self, grid_x, grid_y):
        """获取环境特征"""
        try:
            grid_x = int(np.clip(grid_x, 0, self.dem_data.width - 1))
            grid_y = int(np.clip(grid_y, 0, self.dem_data.height - 1))
            
            dem_val = self.dem_data.read(1)[grid_y, grid_x]
            slope_val = self.slope_data.read(1)[grid_y, grid_x]
            aspect_val = self.aspect_data.read(1)[grid_y, grid_x]
            lc_val = self.landcover_data.read(1)[grid_y, grid_x]
            
            return {
                'elevation': float(dem_val),
                'slope': float(slope_val),
                'aspect': float(aspect_val),
                'landcover': str(int(lc_val))
            }
        except:
            return {
                'elevation': 0, 'slope': 0, 'aspect': 0, 'landcover': '255'
            }

    def calculate_heading(self, path_points, index, look_ahead=3):
        """计算航向角"""
        if index >= len(path_points) - look_ahead:
            if index > 0:
                dx = path_points[index][0] - path_points[index-1][0]
                dy = path_points[index][1] - path_points[index-1][1]
            else:
                return 0
        else:
            dx = path_points[index + look_ahead][0] - path_points[index][0]
            dy = path_points[index + look_ahead][1] - path_points[index][1]
        
        heading = math.degrees(math.atan2(dy, dx))
        return heading

    def interpolate_path(self, path_points, max_dist=60):
        """路径插值"""
        interpolated_points = []
        
        for i in range(len(path_points) - 1):
            p1 = path_points[i]
            p2 = path_points[i + 1]
            
            grid_dist = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            real_dist = grid_dist * self.resolution[0]
            
            interpolated_points.append(p1)
            
            if real_dist > max_dist:
                n_points = int(real_dist / max_dist)
                for j in range(1, n_points):
                    ratio = j / n_points
                    interp_x = int(p1[0] + (p2[0] - p1[0]) * ratio)
                    interp_y = int(p1[1] + (p2[1] - p1[1]) * ratio)
                    interpolated_points.append([interp_x, interp_y])
        
        interpolated_points.append(path_points[-1])
        return np.array(interpolated_points)

    def calculate_env_speed(self, env_features):
        """基于环境计算目标速度"""
        landcover = env_features['landcover']
        slope = env_features['slope']
        
        # 基础速度
        base_speed = TARGET_SPEEDS.get(landcover, 10.0) * self.speed_scale
        
        # 坡度影响
        slope_factor = 1.0
        if slope > 0:  # 上坡
            slope_factor = max(0.6, 1.0 - slope * 0.02)
        elif slope < 0:  # 下坡
            slope_factor = min(1.4, 1.0 + abs(slope) * 0.01)
        
        target_speed = base_speed * slope_factor
        
        # 添加随机扰动保持自然变化
        noise = np.random.normal(0, 0.15)
        target_speed *= (1 + noise)
        
        return max(1.0, target_speed)

    def apply_acceleration_constraints(self, current_speed, target_speed, dt=1.0):
        """应用加速度约束"""
        speed_diff = target_speed - current_speed
        
        if speed_diff > 0:  # 加速
            max_change = self.max_acceleration * dt
        else:  # 减速
            max_change = self.max_deceleration * dt
        
        constrained_change = np.clip(speed_diff, -max_change, max_change)
        new_speed = current_speed + constrained_change
        
        return max(0.5, new_speed)

    def generate_trajectory(self, path_points, goal_id, output_file=None, dt=1.0, initial_speed=5.0):
        """生成混合轨迹"""
        
        print(f"开始生成混合轨迹，共{len(path_points)}个路径点")
        
        # 插值路径
        path_points = self.interpolate_path(path_points)
        print(f"插值后路径点数量: {len(path_points)}")
        
        # 轨迹数据存储
        trajectory_data = {
            'timestamp_ms': [],
            'x': [], 'y': [],
            'velocity_north_ms': [], 'velocity_east_ms': [],
            'heading_deg': [],
            'acceleration_x_ms2': [], 'acceleration_y_ms2': [],
            'goal_id': [],
            'speed_ms': [],
            'env_target_speed': [],
            'acceleration_ms2': [],
            'landcover': []
        }
        
        current_time = 0
        current_speed = initial_speed
        
        for i in range(len(path_points)):
            if i % 500 == 0:
                print(f"处理进度: {i}/{len(path_points)} ({i/len(path_points)*100:.1f}%)")
            
            grid_x, grid_y = path_points[i]
            
            # 获取环境特征
            env_features = self.get_env_features(grid_x, grid_y)
            
            # 计算环境目标速度
            env_target_speed = self.calculate_env_speed(env_features)
            
            # 应用加速度约束
            new_speed = self.apply_acceleration_constraints(current_speed, env_target_speed, dt)
            
            # 计算加速度
            acceleration = (new_speed - current_speed) / dt
            
            # 计算航向
            heading = self.calculate_heading(path_points, i)
            
            # 更新位置
            utm_x, utm_y = self.grid_to_utm(grid_x, grid_y)
            
            # 计算速度分量
            velocity_east = new_speed * math.cos(math.radians(heading))
            velocity_north = new_speed * math.sin(math.radians(heading))
            
            # 计算加速度分量
            accel_east = acceleration * math.cos(math.radians(heading))
            accel_north = acceleration * math.sin(math.radians(heading))
            
            # 记录数据
            trajectory_data['timestamp_ms'].append(current_time * 1000)
            trajectory_data['x'].append(utm_x)
            trajectory_data['y'].append(utm_y)
            trajectory_data['velocity_north_ms'].append(velocity_north)
            trajectory_data['velocity_east_ms'].append(velocity_east)
            trajectory_data['heading_deg'].append(heading)
            trajectory_data['acceleration_x_ms2'].append(accel_east)
            trajectory_data['acceleration_y_ms2'].append(accel_north)
            trajectory_data['goal_id'].append(goal_id)
            trajectory_data['speed_ms'].append(new_speed)
            trajectory_data['env_target_speed'].append(env_target_speed)
            trajectory_data['acceleration_ms2'].append(acceleration)
            trajectory_data['landcover'].append(env_features['landcover'])
            
            # 更新状态
            current_speed = new_speed
            current_time += dt
        
        # 创建DataFrame
        df = pd.DataFrame(trajectory_data)
        
        # 轻微平滑处理
        df['velocity_north_ms'] = gaussian_filter1d(df['velocity_north_ms'], sigma=0.5)
        df['velocity_east_ms'] = gaussian_filter1d(df['velocity_east_ms'], sigma=0.5)
        df['speed_ms'] = gaussian_filter1d(df['speed_ms'], sigma=0.5)
        
        # 保存文件
        if output_file:
            df.to_csv(output_file, index=False)
            print(f"混合轨迹已保存: {output_file}")
            
            # 生成分析图
            self.plot_hybrid_analysis(df, output_file.replace('.csv', '_hybrid_analysis.png'))
        
        avg_speed = df['speed_ms'].mean()
        print(f"混合轨迹生成完成，平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)")
        
        return df

    def plot_hybrid_analysis(self, df, output_file):
        """绘制混合模型分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        time_sec = df['timestamp_ms'] / 1000
        
        # 1. 速度对比
        ax1 = axes[0, 0]
        ax1.plot(time_sec, df['env_target_speed'], 'r--', linewidth=1, alpha=0.7, label='环境目标速度')
        ax1.plot(time_sec, df['speed_ms'], 'b-', linewidth=2, label='实际速度')
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('速度 (m/s)')
        ax1.set_title('环境映射 vs 实际速度')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 加速度分析
        ax2 = axes[0, 1]
        ax2.plot(time_sec, df['acceleration_ms2'], 'g-', linewidth=1, alpha=0.8)
        ax2.axhline(y=2.5, color='r', linestyle='--', alpha=0.5, label='最大加速度')
        ax2.axhline(y=-4.0, color='r', linestyle='--', alpha=0.5, label='最大减速度')
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('加速度 (m/s²)')
        ax2.set_title('加速度约束效果')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 速度分布
        ax3 = axes[1, 0]
        ax3.hist(df['speed_ms'], bins=50, alpha=0.7, edgecolor='black', color='skyblue')
        ax3.axvline(df['speed_ms'].mean(), color='red', linestyle='--', 
                    linewidth=2, label=f'平均值: {df["speed_ms"].mean():.2f} m/s')
        ax3.set_xlabel('速度 (m/s)')
        ax3.set_ylabel('频次')
        ax3.set_title('速度分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 轨迹路径
        ax4 = axes[1, 1]
        scatter = ax4.scatter(df['x'], df['y'], c=df['speed_ms'], cmap='viridis', s=1)
        ax4.set_xlabel('X坐标 (m)')
        ax4.set_ylabel('Y坐标 (m)')
        ax4.set_title('轨迹路径（颜色表示速度）')
        plt.colorbar(scatter, ax=ax4, label='速度 (m/s)')
        ax4.axis('equal')
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"混合模型分析图已保存: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="混合轨迹生成：环境映射+初始条件+加速度约束")
    parser.add_argument('--path_dir', default='gen_exp/standard', help='路径文件目录')
    parser.add_argument('--env_dir', default='trajectory_generation_module_pkg/examples/data/environment', help='环境数据目录')
    parser.add_argument('--output_dir', default='hybrid_output', help='输出目录')
    parser.add_argument('--num_files', type=int, default=1, help='处理文件数量')
    parser.add_argument('--speed_scale', type=float, default=0.4, help='速度缩放因子')
    parser.add_argument('--initial_speed', type=float, default=5.0, help='初始速度(m/s)')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化生成器
    generator = HybridTrajectoryGenerator(args.env_dir, args.speed_scale)
    
    # 处理文件
    path_files = list(Path(args.path_dir).glob('*.npy'))[:args.num_files]
    
    for i, path_file in enumerate(path_files):
        print(f"\n[{i+1}/{len(path_files)}] 处理文件: {path_file.name}")
        
        try:
            # 加载路径
            path_points = np.load(path_file)
            
            # 解析文件名
            parts = path_file.stem.split('_')
            origin_id = parts[1]
            goal_id = f"{parts[2]}_hybrid"
            
            # 生成轨迹
            output_csv = os.path.join(args.output_dir, f"trajectory_{origin_id}_{goal_id}.csv")
            generator.generate_trajectory(path_points, goal_id, output_csv, 
                                        initial_speed=args.initial_speed)
            
        except Exception as e:
            print(f"处理失败: {e}")

if __name__ == '__main__':
    main() 