#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目清理脚本
删除冗余文件，保留核心功能
"""

import os
import shutil
import glob

def backup_important_files():
    """备份重要文件"""
    backup_dir = "backup_before_cleanup"
    os.makedirs(backup_dir, exist_ok=True)
    
    important_files = [
        "generate_trajectories.py",
        "trajectory_generation_module_pkg/src/generator.py",
        "trajectory_generation_module_pkg/src/environment.py",
        "simulate_trajectory.py",
        "validate_best_model.py"
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            shutil.copy2(file_path, backup_dir)
            print(f"已备份: {file_path}")

def remove_redundant_files():
    """删除冗余文件"""
    # 要删除的文件列表
    files_to_remove = [
        # 重复的轨迹生成脚本
        "根据路径生成轨迹/generate_trajectories.py",
        
        # 旧的批处理脚本 (已整合到main.py)
        "batch_generate.sh",
        "batch_generate_standard.sh", 
        "batch_generate_stealth.sh",
        "batch_generate_mountain.sh",
        
        # 测试和调试脚本
        "test_visualization.py",
        "test_output/",
        "simple_vis/",
        
        # 可视化脚本 (功能重复)
        "visualize_simple.py",
        "visualize_trajectory.py.bak",
        "visualize_dem.py",
        "visualize_environment.py",
        "visualize_generated_trajectories.py",
        "visualize_trajectory_speeds.py",
        
        # 分析脚本 (可选保留)
        "analyze_r2_distribution.py",
        "analyze_residuals.py", 
        "analyze_real_environment.py",
        "analyze_speed_environment.py",
        "analyze_trajectory_correlations.py",
        "compare_simulated_trajectories.py",
        
        # 其他工具脚本
        "align_all_tifs.py",
        "create_script.py",
        "get_goal_coords.py",
        
        # 日志和临时文件
        "speed_analysis_summary.txt",
        "speed_analysis_analysis_report.txt", 
        "gis_data_quality_report.txt",
        "trajectory_correlation_summary.md",
        "speed_distribution_model.joblib",
        
        # 大的可视化文件
        "gis_data_comparison.png",
        
        # 重复的目录
        "根据路径生成轨迹/",
        "之前的分析/",
        "visualization_output/",
        "__pycache__/"
    ]
    
    removed_count = 0
    for item in files_to_remove:
        if os.path.exists(item):
            try:
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"已删除目录: {item}")
                else:
                    os.remove(item)
                    print(f"已删除文件: {item}")
                removed_count += 1
            except Exception as e:
                print(f"删除失败 {item}: {e}")
    
    print(f"\n总共删除了 {removed_count} 个文件/目录")

def create_clean_structure():
    """创建清洁的项目结构"""
    # 创建新的目录结构
    directories = [
        "src",
        "data/environment", 
        "data/paths",
        "data/trajectories",
        "output/results",
        "output/plots", 
        "examples",
        "tests",
        "docs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")

def move_core_files():
    """移动核心文件到新结构"""
    moves = [
        # 移动核心生成器到src目录
        ("trajectory_generation_module_pkg/src/generator.py", "src/generator.py"),
        ("trajectory_generation_module_pkg/src/environment.py", "src/environment.py"),
        
        # 移动示例文件
        ("trajectory_generation_module_pkg/examples/run_example.py", "examples/run_example.py"),
        
        # 保留一些重要的分析脚本
        ("simulate_trajectory.py", "src/simulate_trajectory.py"),
        ("validate_best_model.py", "src/validate_best_model.py"),
        
        # 移动可视化脚本
        ("visualize_trajectory.py", "src/visualize_trajectory.py"),
    ]
    
    for src, dst in moves:
        if os.path.exists(src):
            try:
                # 确保目标目录存在
                os.makedirs(os.path.dirname(dst), exist_ok=True)
                shutil.move(src, dst)
                print(f"移动: {src} -> {dst}")
            except Exception as e:
                print(f"移动失败 {src} -> {dst}: {e}")

def create_init_files():
    """创建__init__.py文件"""
    init_files = [
        "src/__init__.py",
        "examples/__init__.py", 
        "tests/__init__.py"
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            with open(init_file, 'w') as f:
                f.write('"""模块初始化文件"""\n')
            print(f"创建: {init_file}")

def main():
    """主函数"""
    print("开始项目清理...")
    print("="*50)
    
    # 1. 备份重要文件
    print("\n1. 备份重要文件...")
    backup_important_files()
    
    # 2. 创建新的目录结构
    print("\n2. 创建清洁的项目结构...")
    create_clean_structure()
    
    # 3. 移动核心文件
    print("\n3. 移动核心文件...")
    move_core_files()
    
    # 4. 创建__init__.py文件
    print("\n4. 创建初始化文件...")
    create_init_files()
    
    # 5. 删除冗余文件 (最后执行)
    print("\n5. 删除冗余文件...")
    remove_redundant_files()
    
    print("\n" + "="*50)
    print("项目清理完成!")
    print("\n清理后的项目结构:")
    print("├── README.md")
    print("├── requirements.txt") 
    print("├── config.py")
    print("├── main.py")
    print("├── src/")
    print("│   ├── generator.py")
    print("│   ├── environment.py")
    print("│   ├── simulate_trajectory.py")
    print("│   ├── validate_best_model.py")
    print("│   └── visualize_trajectory.py")
    print("├── data/")
    print("│   ├── environment/")
    print("│   ├── paths/")
    print("│   └── trajectories/")
    print("├── output/")
    print("│   ├── results/")
    print("│   └── plots/")
    print("├── examples/")
    print("│   └── run_example.py")
    print("└── tests/")
    print("\n使用方法:")
    print("python main.py --help")

if __name__ == "__main__":
    # 确认操作
    response = input("这将删除大量文件，确定要继续吗？(yes/no): ")
    if response.lower() in ['yes', 'y']:
        main()
    else:
        print("操作已取消") 