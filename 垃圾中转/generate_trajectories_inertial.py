"""
脚本名称: generate_trajectories.py
==================================

功能描述:
  该脚本的核心功能是将输入的路径点序列（通常是栅格坐标）转换为一个包含物理运动学
  信息的完整轨迹。它利用环境数据（地形高程、坡度、坡向、土地覆盖类型）和预定义的
  速度模型，为路径上的每个点计算出合理的速度、航向角和加速度，模拟车辆或实体
  在特定环境下的运动行为。脚本支持根据目标平均速度动态调整基础速度模型。

主要功能:
  1. 加载路径点数据 (Numpy .npy 文件，包含栅格坐标)。
  2. 加载环境栅格数据 (DEM, 坡度, 坡向, 土地覆盖类型 - GeoTIFF 格式)。
  3. 定义基于土地覆盖类型的基础速度模型 (`BASE_SPEED_MODELS`) 和坡度影响因子
     (`BASE_SLOPE_EFFECT`)。
  4. 支持通过命令行参数 `--target_speed_kmh` 指定目标平均速度，并据此缩放基础
     速度模型参数。
  5. 将路径的栅格坐标转换为 UTM 坐标。
  6. 为路径上的点提取相应的环境特征值 (高程, 坡度, 坡向, 土地覆盖代码)。
  7. 根据当前点的土地覆盖类型、坡度、坡向以及前一个点的速度，计算预测速度。
     - 应用基于土地覆盖的速度上下限。
     - 应用基于坡度的速度调整因子。
     - 实现速度变化的平滑处理和加速度限制。
  8. 对原始路径点进行插值 (`interpolate_path`)，确保点之间的距离不会过大，
     以满足模拟的时间步长要求。
  9. 计算每个点的航向角 (`calculate_heading`)。
 10. 将计算出的速度分解为北向和东向速度分量。
 11. 计算北向和东向的加速度分量。
 12. 使用高斯滤波器 (`gaussian_filter1d`) 对最终的速度和加速度进行平滑处理。
 13. 将生成的轨迹数据 (时间戳, UTM坐标, 速度分量, 航向角, 加速度分量, 目标ID)
     保存为 CSV 文件。
 14. 为每个生成的轨迹绘制并保存速度分析图 (`plot_speed_analysis`)。
 15. 支持通过命令行参数控制处理文件的数量和跳过文件。

输入:
  - 路径文件: 存储在 `--path_dir` 指定目录下的 .npy 文件，每个文件包含一个
    N x 2 的 Numpy 数组，代表路径的栅格坐标 [[grid_x1, grid_y1], ...]。
    文件名应符合 `path_{origin_id}_{goal_id}_high_mobility.npy` 的格式。
  - 环境数据栅格文件: 位于 `--env_dir` 指定目录下，包括:
    - dem_aligned.tif (数字高程模型)
    - slope_aligned.tif (坡度)
    - aspect_aligned.tif (坡向)
    - landcover_aligned.tif (土地覆盖类型)
    (注意：栅格数据的坐标系需要与脚本内部处理逻辑兼容，通常为UTM)
  - 命令行参数:
    - --path_dir: 路径文件目录。
    - --env_dir: 环境数据目录。
    - --output_dir: 生成轨迹的输出目录。
    - --num_files: 处理的文件数量 (-1 表示处理所有)。
    - --skip_files: 跳过前 N 个文件。
    - --target_speed_kmh: 目标平均速度 (km/h)。
    - --path_suffix: 路径文件后缀，如high_mobility、mountain_special等。
  - 内部模型参数: `BASE_SPEED_MODELS`, `BASE_SLOPE_EFFECT`, `REFERENCE_AVG_SPEED_MS`。

输出:
  - 轨迹文件: 保存到 `--output_dir` 指定目录下，CSV 格式，文件名格式为
    `trajectory_{origin_id}_{goal_id}_high_mobility.csv`。包含列:
    timestamp_ms, x, y, velocity_north_ms, velocity_east_ms, heading_deg,
    acceleration_x_ms2, acceleration_y_ms2, goal_id。
  - 速度分析图: 保存到 `--output_dir` 指定目录下，PNG 格式，文件名格式为
    `trajectory_{origin_id}_{goal_id}_high_mobility_speed_analysis.png`。
    显示轨迹的速度随时间变化曲线和平均速度。
  - 控制台输出: 显示加载信息、处理进度、每个文件的平均生成速度、错误信息以及最终总结。

使用示例:
  # 使用默认参数生成轨迹 (目标速度75km/h, 处理前10个文件)
  python generate_trajectories.py

  # 指定不同目标速度并处理所有文件
  python generate_trajectories.py --target_speed_kmh 80.0 --num_files -1

  # 指定输入输出目录
  python generate_trajectories.py --path_dir /path/to/paths --env_dir /path/to/env --output_dir /path/to/output

"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轨迹生成脚本
将路径点转换为包含速度、航向角和加速度的完整轨迹
"""

import os
import numpy as np
import pandas as pd
import rasterio
from rasterio.transform import rowcol
import math
from scipy.ndimage import gaussian_filter1d
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from datetime import datetime
from collections import deque

# 配置参数 - 适配更高速度 (75km/h)
BASE_SPEED_MODELS = {
    '10': {  # 耕地
        'base_speed': 26.0,  # m/s (93.6 km/h)
        'max_speed': 32.0,   # m/s (115.2 km/h)
        'min_speed': 20.0,   # m/s (72 km/h)
        'transition_weight': 0.3  # 进一步降低过渡权重
    },
    '20': {  # 林地
        'base_speed': 23.0,  # m/s (82.8 km/h)
        'max_speed': 28.0,   # m/s (100.8 km/h)
        'min_speed': 18.0,   # m/s (64.8 km/h)
        'transition_weight': 0.2  # 进一步降低过渡权重
    },
    '30': {  # 草地
        'base_speed': 27.0,  # m/s (97.2 km/h)
        'max_speed': 33.0,   # m/s (118.8 km/h)
        'min_speed': 21.0,   # m/s (75.6 km/h)
        'transition_weight': 0.3  # 进一步降低过渡权重
    },
    '40': {  # 灌木地
        'base_speed': 21.0,  # m/s (75.6 km/h)
        'max_speed': 26.0,   # m/s (93.6 km/h)
        'min_speed': 17.0,   # m/s (61.2 km/h)
        'transition_weight': 0.2  # 进一步降低过渡权重
    },
    '50': {  # 水体
        'base_speed': 12.0,   # m/s (43.2 km/h)
        'max_speed': 16.0,   # m/s (57.6 km/h)
        'min_speed': 8.0,    # m/s (28.8 km/h)
        'transition_weight': 0.3  # 进一步降低过渡权重
    },
    '60': {  # 建设用地
        'base_speed': 28.0,  # m/s (100.8 km/h)
        'max_speed': 35.0,   # m/s (126 km/h)
        'min_speed': 22.0,   # m/s (79.2 km/h)
        'transition_weight': 0.3  # 进一步降低过渡权重
    },
    '80': {  # 未利用地 1
        'base_speed': 25.0,  # m/s (90 km/h)
        'max_speed': 30.0,   # m/s (108 km/h)
        'min_speed': 19.0,   # m/s (68.4 km/h)
        'transition_weight': 0.2  # 进一步降低过渡权重
    },
    '90': {  # 未利用地 2
        'base_speed': 24.0,  # m/s (86.4 km/h)
        'max_speed': 29.0,   # m/s (104.4 km/h)
        'min_speed': 18.0,   # m/s (64.8 km/h)
        'transition_weight': 0.2  # 进一步降低过渡权重
    },
    '255': {  # 未知类型
        'base_speed': 22.0,  # m/s (79.2 km/h)
        'max_speed': 27.0,   # m/s (97.2 km/h)
        'min_speed': 17.0,   # m/s (61.2 km/h)
        'transition_weight': 0.2  # 进一步降低过渡权重
    }
}

# 坡度影响系数 - 进一步优化
BASE_SLOPE_EFFECT = {
    'uphill': {
        'threshold': 6.0,  # 进一步降低上坡阈值
        'base_factor': 0.85,  # 进一步增加小上坡惩罚
        'steep_factor': 0.75  # 进一步增加大上坡惩罚
    },
    'downhill': {
        'threshold': -10.0,  # 进一步降低下坡阈值
        'base_factor': 1.18,  # 增加小下坡奖励
        'steep_factor': 1.12  # 增加大下坡奖励
    }
}

REFERENCE_AVG_SPEED_MS = 20.8 # 75 km/h 的基准速度

class VehicleState:
    """管理车辆的动态状态和物理属性"""
    def __init__(self, mass=1800, power=150000, drag_coeff=0.35):
        self.mass = mass  # 车辆质量 (kg)
        self.max_power = power  # 最大功率 (W)
        self.drag_coefficient = drag_coeff # 风阻系数
        
        self.speed_history = deque(maxlen=10) # 速度历史，用于计算动量
        self.current_speed = 0.0

    def update_speed(self, speed):
        """更新当前速度并记录到历史"""
        self.current_speed = speed
        self.speed_history.append(speed)

    def get_momentum_speed(self):
        """计算动量速度（基于历史速度的趋势）"""
        if len(self.speed_history) < 3:
            return self.current_speed
        
        # 使用简单的线性趋势预测动量
        recent_speeds = list(self.speed_history)[-5:]
        if len(recent_speeds) > 1:
            trend = np.mean(np.diff(recent_speeds))
            # 预测下一步的速度，但施加衰减
            momentum = self.current_speed + trend * 0.8 
        else:
            momentum = self.current_speed
            
        return max(0, momentum) # 速度不能为负

class TrajectoryGenerator:
    def __init__(self, env_dir, target_speed_kmh=75.0):
        """初始化轨迹生成器
        
        Args:
            env_dir: 环境数据目录
            target_speed_kmh: 目标平均速度 (km/h)
        """
        self.dem_file = os.path.join(env_dir, 'dem_aligned.tif')
        self.slope_file = os.path.join(env_dir, 'slope_aligned.tif')
        self.aspect_file = os.path.join(env_dir, 'aspect_aligned.tif')
        self.landcover_file = os.path.join(env_dir, 'landcover_aligned.tif')
        
        self.dem_data = rasterio.open(self.dem_file)
        self.slope_data = rasterio.open(self.slope_file)
        self.aspect_data = rasterio.open(self.aspect_file)
        self.landcover_data = rasterio.open(self.landcover_file)
        
        self.transform = self.dem_data.transform
        self.resolution = self.dem_data.res
        self.crs = self.dem_data.crs
        
        print(f"环境数据加载完成，分辨率: {self.resolution}")
        
        # --- 动态调整速度参数 ---
        self.target_speed_ms = target_speed_kmh / 3.6
        self.scale_factor = self.target_speed_ms / REFERENCE_AVG_SPEED_MS
        
        print(f"目标平均速度: {target_speed_kmh:.1f} km/h ({self.target_speed_ms:.2f} m/s)")
        print(f"速度缩放因子: {self.scale_factor:.3f} (基于 {REFERENCE_AVG_SPEED_MS*3.6:.1f} km/h 基准)")
        
        self.speed_models = self._scale_speed_models(BASE_SPEED_MODELS, self.scale_factor)
        self.slope_effect = BASE_SLOPE_EFFECT # 坡度影响暂不缩放
        
        # 初始化车辆状态
        self.vehicle = VehicleState()
        
        print("动态速度参数已应用:")
        # print(self.speed_models) # 可以取消注释来查看具体参数
        # --- 调整结束 ---

    def _scale_speed_models(self, base_models, scale_factor):
        """根据缩放因子调整速度模型参数"""
        scaled_models = {}
        for landcover, params in base_models.items():
            scaled_params = params.copy()
            scaled_params['base_speed'] = params['base_speed'] * scale_factor
            scaled_params['max_speed'] = params['max_speed'] * scale_factor
            # 对最小速度应用较小的缩放，避免变为负数或过小
            min_scale_factor = 1 + (scale_factor - 1) * 0.5 
            scaled_params['min_speed'] = params['min_speed'] * max(0.1, min_scale_factor)
            scaled_models[landcover] = scaled_params
        return scaled_models

    def grid_to_utm(self, grid_x, grid_y):
        """将栅格坐标转换为UTM坐标
        
        Args:
            grid_x: 栅格X坐标
            grid_y: 栅格Y坐标
            
        Returns:
            utm_x, utm_y: UTM坐标
        """
        utm_x, utm_y = self.transform * (grid_x, grid_y)
        return utm_x, utm_y
    
    def get_env_features(self, grid_x, grid_y):
        """获取给定栅格点的环境特征
        
        Args:
            grid_x: 栅格X坐标
            grid_y: 栅格Y坐标
            
        Returns:
            features: 环境特征字典
        """
        try:
            # 防止越界
            if grid_y >= self.dem_data.height or grid_x >= self.dem_data.width or grid_y < 0 or grid_x < 0:
                return {
                    'elevation': 0,
                    'slope': 0,
                    'aspect': 0,
                    'landcover': '40'  # 默认为灌木地
                }
            
            # 读取环境数据
            elevation = self.dem_data.read(1, window=((grid_y, grid_y+1), (grid_x, grid_x+1)))[0][0]
            slope = self.slope_data.read(1, window=((grid_y, grid_y+1), (grid_x, grid_x+1)))[0][0]
            aspect = self.aspect_data.read(1, window=((grid_y, grid_y+1), (grid_x, grid_x+1)))[0][0]
            landcover = str(self.landcover_data.read(1, window=((grid_y, grid_y+1), (grid_x, grid_x+1)))[0][0])
            
            return {
                'elevation': elevation,
                'slope': slope,
                'aspect': aspect,
                'landcover': landcover
            }
        except Exception as e:
            print(f"获取环境特征出错: {e}, 位置: ({grid_x}, {grid_y})")
            return {
                'elevation': 0,
                'slope': 0,
                'aspect': 0,
                'landcover': '40'  # 默认为灌木地
            }
    
    def calculate_speed(self, landcover, slope, aspect, prev_speed, heading):
        """
        使用动态惯性模型计算速度
        - env_speed: 完全由环境决定的速度
        - momentum_speed: 基于历史速度趋势的惯性速度
        - 权重根据当前速度与环境速度的差异动态调整
        """
        if landcover not in self.speed_models:
            landcover = '255'
        
        model = self.speed_models[landcover]
        
        # 1. 计算纯环境驱动的速度 (env_speed)
        env_speed = model['base_speed']
        
        # 应用坡度影响
        effective_slope = slope * math.cos(math.radians(aspect - heading))
        
        if effective_slope > self.slope_effect['uphill']['threshold']:
            factor = self.slope_effect['uphill']['steep_factor']
        elif effective_slope > 0:
            factor = self.slope_effect['uphill']['base_factor']
        elif effective_slope < self.slope_effect['downhill']['threshold']:
            factor = self.slope_effect['downhill']['steep_factor']
        else:
            factor = self.slope_effect['downhill']['base_factor']
        env_speed *= factor

        # 2. 获取动量速度
        momentum_speed = self.vehicle.get_momentum_speed()
        
        # 3. 动态计算权重
        speed_diff = abs(prev_speed - env_speed)
        
        # 当速度远低于环境速度时，环境权重更高，加速更快
        # 当速度接近或超过环境速度时，惯性权重更高，保持速度
        # 使用 sigmoid 函数进行平滑过渡
        inertia_weight = 1 / (1 + math.exp(-0.2 * (speed_diff - 5)))
        env_weight = 1.0 - inertia_weight
        
        # 4. 计算目标速度
        target_speed = (env_speed * env_weight) + (momentum_speed * inertia_weight)

        # 5. 应用加速度/减速度限制 (基于简化的物理模型)
        # 功率 = (牵引力 - 阻力) * 速度
        # 简化：用最大加速度限制变化率
        max_accel = 2.5 # m/s^2
        max_decel = 4.0 # m/s^2

        if target_speed > prev_speed:
            final_speed = min(target_speed, prev_speed + max_accel)
        else:
            final_speed = max(target_speed, prev_speed - max_decel)
            
        # 6. 应用地类速度硬限制
        final_speed = max(model['min_speed'], min(model['max_speed'], final_speed))
        
        return final_speed
    
    def generate_trajectory(self, path_points, goal_id, output_file=None, time_step=1000):
        """生成轨迹数据
        
        Args:
            path_points: 路径点数组，格式为 [[grid_x, grid_y], ...]
            goal_id: 目标点ID
            output_file: 输出文件路径
            time_step: 时间步长(毫秒)
            
        Returns:
            trajectory_df: 轨迹数据DataFrame
        """
        print(f"开始生成轨迹，共{len(path_points)}个路径点, 目标点ID: {goal_id}, 时间步长: {time_step}ms")
        
        # 准备轨迹数据结构
        trajectory_data = {
            'timestamp_ms': [],
            'x': [],
            'y': [],
            'velocity_north_ms': [],
            'velocity_east_ms': [],
            'heading_deg': [],
            'acceleration_x_ms2': [],
            'acceleration_y_ms2': [],
            'goal_id': []
        }
        
        # 计算初始参数
        current_time = 0
        prev_speed = 0
        prev_vx, prev_vy = 0, 0
        
        # 对路径进行插值，确保点的间距合理
        path_points = self.interpolate_path(path_points)
        
        # 重置车辆状态
        self.vehicle = VehicleState()

        # 生成轨迹
        for i in range(len(path_points)):
            grid_x, grid_y = path_points[i]
            
            # 转换为UTM坐标
            utm_x, utm_y = self.grid_to_utm(grid_x, grid_y)
            
            # 获取环境特征
            env_features = self.get_env_features(grid_x, grid_y)
            
            # 计算航向
            heading = self.calculate_heading(path_points, i)
            
            # 计算速度
            current_speed = self.calculate_speed(
                env_features['landcover'],
                env_features['slope'],
                env_features['aspect'],
                prev_speed,
                heading
            )
            self.vehicle.update_speed(current_speed)
            
            # 计算速度分量
            vx = current_speed * math.sin(math.radians(heading))
            vy = current_speed * math.cos(math.radians(heading))
            
            # 计算加速度
            if i > 0:
                ax = (vx - prev_vx) / (time_step / 1000)
                ay = (vy - prev_vy) / (time_step / 1000)
            else:
                ax, ay = 0, 0
            
            # 限制加速度
            max_accel = 3.0  # m/s^2
            ax = max(min(ax, max_accel), -max_accel)
            ay = max(min(ay, max_accel), -max_accel)
            
            # 更新轨迹数据
            trajectory_data['timestamp_ms'].append(current_time)
            trajectory_data['x'].append(utm_x)
            trajectory_data['y'].append(utm_y)
            trajectory_data['velocity_north_ms'].append(vy)
            trajectory_data['velocity_east_ms'].append(vx)
            trajectory_data['heading_deg'].append(heading)
            trajectory_data['acceleration_x_ms2'].append(ax)
            trajectory_data['acceleration_y_ms2'].append(ay)
            trajectory_data['goal_id'].append(goal_id)
            
            # 更新状态
            current_time += time_step
            prev_speed = current_speed
            prev_vx, prev_vy = vx, vy
        
        # 创建DataFrame
        trajectory_df = pd.DataFrame(trajectory_data)
        
        # 对速度和加速度进行平滑（进一步减小sigma值）
        trajectory_df['velocity_north_ms'] = gaussian_filter1d(trajectory_df['velocity_north_ms'], sigma=0.6)
        trajectory_df['velocity_east_ms'] = gaussian_filter1d(trajectory_df['velocity_east_ms'], sigma=0.6)
        trajectory_df['acceleration_x_ms2'] = gaussian_filter1d(trajectory_df['acceleration_x_ms2'], sigma=1.0)
        trajectory_df['acceleration_y_ms2'] = gaussian_filter1d(trajectory_df['acceleration_y_ms2'], sigma=1.0)
        
        # 保存到文件
        if output_file:
            trajectory_df.to_csv(output_file, index=False)
            print(f"轨迹数据已保存到: {output_file}")
            
            # 额外保存速度分析图
            self.plot_speed_analysis(trajectory_df, output_file.replace('.csv', '_speed_analysis.png'))
        
        # 打印统计信息
        avg_speed = np.sqrt(trajectory_df['velocity_north_ms']**2 + trajectory_df['velocity_east_ms']**2).mean()
        print(f"轨迹生成完成，平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)")
        
        return trajectory_df
    
    def interpolate_path(self, path_points, max_dist=60):
        """对路径点进行插值，确保点的间距合理
        
        Args:
            path_points: 路径点数组
            max_dist: 最大间距(米)
            
        Returns:
            interpolated_points: 插值后的路径点
        """
        interpolated_points = []
        
        for i in range(len(path_points) - 1):
            p1 = path_points[i]
            p2 = path_points[i + 1]
            
            # 计算栅格距离
            grid_dist = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            
            # 转换为实际距离
            real_dist = grid_dist * self.resolution[0]
            
            # 加入第一个点
            interpolated_points.append(p1)
            
            # 如果距离过大，进行插值
            if real_dist > max_dist:
                # 计算需要插入的点数
                n_points = int(real_dist / max_dist)
                
                for j in range(1, n_points):
                    # 线性插值
                    ratio = j / n_points
                    interp_x = int(p1[0] + (p2[0] - p1[0]) * ratio)
                    interp_y = int(p1[1] + (p2[1] - p1[1]) * ratio)
                    interpolated_points.append([interp_x, interp_y])
        
        # 加入最后一个点
        interpolated_points.append(path_points[-1])
        
        return np.array(interpolated_points)
    
    def calculate_heading(self, path_points, index, look_ahead=3):
        """计算路径的航向角
        
        Args:
            path_points: 路径点数组
            index: 当前点索引
            look_ahead: 向前看的点数
            
        Returns:
            heading: 航向角(度)
        """
        # 如果是最后几个点，使用前一段的方向
        if index >= len(path_points) - look_ahead:
            if index > 0:
                dx = path_points[index][0] - path_points[index-1][0]
                dy = path_points[index][1] - path_points[index-1][1]
            else:
                return 0
        else:
            dx = path_points[index + look_ahead][0] - path_points[index][0]
            dy = path_points[index + look_ahead][1] - path_points[index][1]
        
        # 计算航向角 (0度为北，顺时针增加)
        heading = math.degrees(math.atan2(dx, dy))
        if heading < 0:
            heading += 360
        
        return heading
    
    def plot_speed_analysis(self, trajectory_df, output_file):
        """绘制速度分析图
        
        Args:
            trajectory_df: 轨迹数据
            output_file: 输出文件
        """
        # 计算实际速度
        speeds = np.sqrt(trajectory_df['velocity_north_ms']**2 + trajectory_df['velocity_east_ms']**2)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制速度曲线
        ax.plot(trajectory_df['timestamp_ms'] / 1000, speeds, 'b-', label='速度')
        
        # 添加平均速度线
        avg_speed = speeds.mean()
        ax.axhline(y=avg_speed, color='r', linestyle='--', label=f'平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)')
        
        # 添加标题和标签
        ax.set_title('轨迹速度分析')
        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('速度 (m/s)')
        ax.grid(True)
        ax.legend()
        
        # 保存图形
        plt.tight_layout()
        plt.savefig(output_file)
        plt.close()
        
        print(f"速度分析图已保存到: {output_file}")

def process_path_file(file_path, generator, output_dir, path_suffix='high_mobility'):
    """处理单个路径文件
    
    Args:
        file_path: 路径文件
        generator: 轨迹生成器
        output_dir: 输出目录
        path_suffix: 路径文件后缀，如'high_mobility'、'mountain_special'等
    
    Returns:
        success: 是否成功
    """
    try:
        # 从文件名解析信息
        file_name = os.path.basename(file_path)
        parts = file_name.split('_')
        origin_id = parts[1]
        goal_id = int(parts[2])
        
        # 加载路径点
        path_points = np.load(file_path)
        
        # 输出文件名
        output_file = os.path.join(output_dir, f"trajectory_{origin_id}_{goal_id}_{path_suffix}.csv")
        
        # 生成轨迹
        generator.generate_trajectory(path_points, goal_id, output_file)
        
        return True
    except Exception as e:
        print(f"处理文件{file_path}时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成轨迹数据')
    parser.add_argument('--path_dir', type=str, default='/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/high_mobility',
                        help='路径数据目录')
    parser.add_argument('--env_dir', type=str, default='/home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/environment',
                        help='环境数据目录')
    parser.add_argument('--output_dir', type=str, default='/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories',
                        help='输出目录')
    parser.add_argument('--num_files', type=int, default=10,
                        help='处理的文件数量')
    parser.add_argument('--skip_files', type=int, default=0,
                        help='跳过前N个文件')
    parser.add_argument('--target_speed_kmh', type=float, default=75.0,
                        help='目标平均速度 (km/h)')
    parser.add_argument('--path_suffix', type=str, default='high_mobility',
                        help='路径文件后缀，如high_mobility、mountain_special等')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化轨迹生成器, 传入目标速度
    generator = TrajectoryGenerator(args.env_dir, args.target_speed_kmh)
    
    # 获取所有路径文件
    path_files = [f for f in os.listdir(args.path_dir) if f.endswith(f'_{args.path_suffix}.npy')]
    print(f"找到{len(path_files)}个{args.path_suffix}路径文件")
    
    # 排序文件以确保一致的处理顺序
    path_files.sort()
    
    # 应用跳过和限制
    if args.skip_files > 0:
        path_files = path_files[args.skip_files:]
        print(f"跳过前{args.skip_files}个文件")
    
    # 限制处理的文件数量
    if args.num_files > 0 and args.num_files < len(path_files):
        path_files = path_files[:args.num_files]
    
    # 处理每个文件
    success_count = 0
    for i, file_name in enumerate(path_files):
        file_path = os.path.join(args.path_dir, file_name)
        print(f"[{i+1}/{len(path_files)}] 处理文件: {file_name}")
        
        if process_path_file(file_path, generator, args.output_dir, args.path_suffix):
            success_count += 1
    
    print(f"处理完成，成功生成{success_count}/{len(path_files)}个轨迹文件")
    print(f"输出目录: {args.output_dir}")

if __name__ == "__main__":
    main() 