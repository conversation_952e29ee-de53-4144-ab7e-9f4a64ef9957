import os
import zipfile
import glob

def unzip_all_in_directory(source_dir, dest_dir):
    """
    解压指定目录下的所有zip文件到目标目录。

    :param source_dir: 包含zip文件的源目录。
    :param dest_dir: 解压文件的目标目录。
    """
    os.makedirs(dest_dir, exist_ok=True)
    
    zip_files = glob.glob(os.path.join(source_dir, '*.zip'))
    
    if not zip_files:
        print(f"在目录 '{source_dir}' 中未找到任何 .zip 文件。")
        return

    print(f"找到 {len(zip_files)} 个 .zip 文件，开始解压...")

    for zip_path in zip_files:
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(dest_dir)
                print(f"成功解压: {os.path.basename(zip_path)}")
        except zipfile.BadZipFile:
            print(f"错误：文件 {os.path.basename(zip_path)} 不是一个有效的zip文件或已损坏。")
        except Exception as e:
            print(f"解压 {os.path.basename(zip_path)} 时发生未知错误: {e}")

if __name__ == "__main__":
    dem_zip_dir = 'data/DEM'
    unzip_target_dir = 'data/DEM/unzipped'
    unzip_all_in_directory(dem_zip_dir, unzip_target_dir)
    print("\n所有 .zip 文件解压完成。") 