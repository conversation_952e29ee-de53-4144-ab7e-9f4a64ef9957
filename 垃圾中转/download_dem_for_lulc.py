import requests
import os
from tqdm import tqdm

# Automatically calculated list of required files
REQUIRED_FILES = sorted(list({
    'N50.zip', 'N51.zip', 'N52.zip', 'N53.zip', 'N54.zip', 'N25.zip', 'N26.zip', 
    'N27.zip', 'N28.zip', 'N29.zip', 'N40.zip', 'N41.zip', 'N42.zip', 'N43.zip', 'N44.zip'
}))

BASE_URL = "https://www.viewfinderpanoramas.org/dem3"
OUTPUT_DIR = "data/DEM"

def download_files():
    print(f"将开始下载 {len(REQUIRED_FILES)} 个DEM文件到: {OUTPUT_DIR}")
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    success_count = 0
    error_count = 0
    skipped_count = 0

    with tqdm(total=len(REQUIRED_FILES), desc="总下载进度") as pbar:
        for filename in REQUIRED_FILES:
            pbar.set_postfix_str(filename)
            file_url = f"{BASE_URL}/{filename}"
            output_path = os.path.join(OUTPUT_DIR, filename)

            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                # print(f"\n文件 {filename} 已存在，跳过。")
                pbar.update(1)
                skipped_count += 1
                success_count += 1
                continue

            try:
                response = requests.get(file_url, stream=True, timeout=300)
                response.raise_for_status()
                total_size = int(response.headers.get('content-length', 0))
                
                with open(output_path, 'wb') as f, tqdm(
                    total=total_size, unit='iB', unit_scale=True,
                    desc=f"  -> {filename}", leave=False
                ) as file_pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        file_pbar.update(len(chunk))
                success_count += 1
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404:
                    tqdm.write(f"错误: {filename} 在服务器上不存在 (404 Not Found)")
                else:
                    tqdm.write(f"下载 {filename} 时发生HTTP错误: {e}")
                error_count += 1
            except Exception as e:
                tqdm.write(f"下载 {filename} 时发生未知错误: {e}")
                error_count += 1
            
            pbar.update(1)

    print("\n--- 下载总结 ---")
    print(f"成功: {success_count}, 失败: {error_count}, 已跳过: {skipped_count}")
    if error_count > 0:
        print("警告: 部分文件下载失败，对应区域的DEM数据将不完整。")

if __name__ == "__main__":
    download_files()
