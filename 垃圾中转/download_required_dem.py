import os
import math
import glob
import requests
import rasterio
from rasterio.warp import transform_bounds
from tqdm import tqdm

def get_required_dem_tiles(lulc_files):
    """
    根据LULC文件的范围，计算需要哪些DEM瓦片。
    返回一个需要下载的 NXX.zip 文件名集合。
    """
    required_dem_zips = set()
    print("正在分析LULC文件以确定所需的DEM瓦片...")

    for file_path in lulc_files:
        try:
            with rasterio.open(file_path) as src:
                src_crs = src.crs
                bounds = src.bounds
                
                # 转换为经纬度
                lonlat_bounds = transform_bounds(src_crs, 'EPSG:4326', *bounds)
                
                # 从纬度范围计算所需的 NXX 文件
                min_lat = math.floor(lonlat_bounds[1])
                max_lat = math.ceil(lonlat_bounds[3])

                for lat in range(min_lat, max_lat):
                    if lat >= 0: # 只处理北半球
                        zip_name = f"N{lat}.zip"
                        required_dem_zips.add(zip_name)

        except Exception as e:
            print(f"处理文件 {os.path.basename(file_path)} 时出错: {e}")
            
    return required_dem_zips

def download_file(url, dest_folder, filename):
    """下载单个文件并显示进度条。"""
    dest_path = os.path.join(dest_folder, filename)
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status() # 检查HTTP请求是否成功

        total_size = int(response.headers.get('content-length', 0))
        
        with open(dest_path, 'wb') as f, tqdm(
            desc=filename,
            total=total_size,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
        ) as bar:
            for chunk in response.iter_content(chunk_size=8192):
                size = f.write(chunk)
                bar.update(size)
        
        print(f"成功下载: {filename}")
        return True

    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            print(f"下载失败: {filename} (文件未在服务器上找到 - 404)")
        else:
            print(f"下载失败: {filename} (HTTP错误: {e.response.status_code})")
        return False
    except Exception as e:
        print(f"下载 {filename} 时发生未知错误: {e}")
        return False

if __name__ == "__main__":
    lulc_directory_glob = "./data/N*_2010LC030/*.tif"
    dem_storage_dir = "data/DEM"
    base_url = "http://viewfinderpanoramas.org/dem3/"

    # 1. 获取所有LULC文件的路径
    lulc_files = glob.glob(lulc_directory_glob)
    if not lulc_files:
        print("未找到LULC文件，请检查路径。")
        exit()

    # 2. 确定需要哪些DEM文件
    required_zips = get_required_dem_tiles(lulc_files)
    print(f"\n根据LULC数据，共需要 {len(required_zips)} 个DEM瓦片文件: {sorted(list(required_zips))}")

    # 3. 检查已有的DEM文件
    os.makedirs(dem_storage_dir, exist_ok=True)
    existing_zips = {os.path.basename(p) for p in glob.glob(os.path.join(dem_storage_dir, "*.zip"))}
    print(f"已存在 {len(existing_zips)} 个DEM文件: {sorted(list(existing_zips))}")

    # 4. 确定需要下载的文件
    to_download = required_zips - existing_zips
    
    if not to_download:
        print("\n所有必需的DEM文件均已存在，无需下载。")
    else:
        print(f"\n准备下载 {len(to_download)} 个缺失的DEM文件: {sorted(list(to_download))}")
        for zip_name in sorted(list(to_download)):
            file_url = f"{base_url}{zip_name}"
            download_file(file_url, dem_storage_dir, zip_name)

    print("\nDEM数据准备完成。") 