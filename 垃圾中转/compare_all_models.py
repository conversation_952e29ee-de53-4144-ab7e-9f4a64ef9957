#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
综合模型对比分析脚本
对比原始轨迹、惯性模型和物理模型的差异
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ModelComparator:
    """模型对比分析器"""
    
    def __init__(self):
        self.models = {}
        self.comparison_results = {}
    
    def load_trajectory_data(self, file_path, model_name):
        """加载轨迹数据"""
        try:
            df = pd.read_csv(file_path)
            
            # 标准化坐标列名
            if 'latitude' in df.columns and 'longitude' in df.columns:
                df['x'] = df['longitude']
                df['y'] = df['latitude']
            elif 'x' not in df.columns or 'y' not in df.columns:
                print(f"警告：{model_name} 缺少坐标信息")
                return False
            
            # 计算速度（如果没有speed_ms列）
            if 'speed_ms' not in df.columns:
                if 'velocity_2d_ms' in df.columns:
                    df['speed_ms'] = df['velocity_2d_ms']
                elif 'calculated_speed_ms' in df.columns:
                    df['speed_ms'] = df['calculated_speed_ms']
                else:
                    df['speed_ms'] = np.sqrt(df['velocity_north_ms']**2 + df['velocity_east_ms']**2)
            
            # 计算加速度（如果没有acceleration_ms2列）
            if 'acceleration_ms2' not in df.columns:
                if 'horizontal_acceleration_ms2' in df.columns:
                    df['acceleration_ms2'] = df['horizontal_acceleration_ms2']
                elif 'acceleration_magnitude_ms2' in df.columns:
                    df['acceleration_ms2'] = df['acceleration_magnitude_ms2']
                else:
                    df['acceleration_ms2'] = np.sqrt(df['acceleration_x_ms2']**2 + df['acceleration_y_ms2']**2)
            
            self.models[model_name] = df
            print(f"已加载 {model_name} 数据：{len(df)} 个点")
            return True
            
        except Exception as e:
            print(f"加载 {model_name} 数据失败: {e}")
            return False
    
    def calculate_statistics(self):
        """计算各模型的统计指标"""
        stats_data = []
        
        for model_name, df in self.models.items():
            stats_data.append({
                '模型': model_name,
                '平均速度 (m/s)': df['speed_ms'].mean(),
                '速度标准差 (m/s)': df['speed_ms'].std(),
                '最大速度 (m/s)': df['speed_ms'].max(),
                '最小速度 (m/s)': df['speed_ms'].min(),
                '平均加速度 (m/s²)': df['acceleration_ms2'].mean(),
                '加速度标准差 (m/s²)': df['acceleration_ms2'].std(),
                '最大加速度 (m/s²)': df['acceleration_ms2'].max(),
                '轨迹长度 (点数)': len(df),
                '总时间 (秒)': df['timestamp_ms'].max() / 1000 if 'timestamp_ms' in df.columns else len(df)
            })
        
        return pd.DataFrame(stats_data)
    
    def calculate_correlations(self):
        """计算模型间的相关性"""
        if len(self.models) < 2:
            return None
        
        model_names = list(self.models.keys())
        correlations = {}
        
        # 找到最短的轨迹长度
        min_length = min(len(df) for df in self.models.values())
        
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names):
                if i < j:  # 避免重复计算
                    # 截取相同长度
                    speed1 = self.models[model1]['speed_ms'][:min_length]
                    speed2 = self.models[model2]['speed_ms'][:min_length]
                    
                    # 计算相关系数
                    corr_coeff, p_value = stats.pearsonr(speed1, speed2)
                    correlations[f"{model1} vs {model2}"] = {
                        '相关系数': corr_coeff,
                        'p值': p_value,
                        '显著性': 'significant' if p_value < 0.05 else 'not significant'
                    }
        
        return correlations
    
    def plot_comprehensive_comparison(self, output_file):
        """绘制综合对比图"""
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 速度时间序列对比 (2x2的第1个)
        ax1 = plt.subplot(3, 3, 1)
        for model_name, df in self.models.items():
            time_sec = np.arange(len(df))
            ax1.plot(time_sec, df['speed_ms'], label=model_name, linewidth=2, alpha=0.8)
        ax1.set_xlabel('时间 (秒)', fontsize=14)
        ax1.set_ylabel('速度 (m/s)', fontsize=14)
        ax1.set_title('速度时间序列对比', fontsize=16)
        ax1.legend(fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 2. 速度分布对比
        ax2 = plt.subplot(3, 3, 2)
        for model_name, df in self.models.items():
            ax2.hist(df['speed_ms'], bins=50, alpha=0.6, label=model_name, density=True)
        ax2.set_xlabel('速度 (m/s)', fontsize=14)
        ax2.set_ylabel('密度', fontsize=14)
        ax2.set_title('速度分布对比', fontsize=16)
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 3. 加速度时间序列对比
        ax3 = plt.subplot(3, 3, 3)
        for model_name, df in self.models.items():
            time_sec = np.arange(len(df))
            ax3.plot(time_sec, df['acceleration_ms2'], label=model_name, linewidth=2, alpha=0.8)
        ax3.set_xlabel('时间 (秒)', fontsize=14)
        ax3.set_ylabel('加速度 (m/s²)', fontsize=14)
        ax3.set_title('加速度时间序列对比', fontsize=16)
        ax3.legend(fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 4. 轨迹路径对比
        ax4 = plt.subplot(3, 3, 4)
        colors = ['blue', 'red', 'green', 'orange', 'purple']
        for i, (model_name, df) in enumerate(self.models.items()):
            ax4.plot(df['x'], df['y'], label=model_name, linewidth=2, 
                    color=colors[i % len(colors)], alpha=0.7)
        ax4.set_xlabel('X坐标 (m)', fontsize=14)
        ax4.set_ylabel('Y坐标 (m)', fontsize=14)
        ax4.set_title('轨迹路径对比', fontsize=16)
        ax4.legend(fontsize=12)
        ax4.axis('equal')
        ax4.grid(True, alpha=0.3)
        
        # 5. 速度变化率对比
        ax5 = plt.subplot(3, 3, 5)
        for model_name, df in self.models.items():
            speed_diff = np.abs(np.diff(df['speed_ms']))
            time_sec = np.arange(len(speed_diff))
            ax5.plot(time_sec, speed_diff, label=model_name, linewidth=2, alpha=0.8)
        ax5.set_xlabel('时间 (秒)', fontsize=14)
        ax5.set_ylabel('速度变化率 (m/s)', fontsize=14)
        ax5.set_title('速度变化率对比', fontsize=16)
        ax5.legend(fontsize=12)
        ax5.grid(True, alpha=0.3)
        
        # 6. 统计指标对比（箱线图）
        ax6 = plt.subplot(3, 3, 6)
        speed_data = []
        labels = []
        for model_name, df in self.models.items():
            speed_data.append(df['speed_ms'])
            labels.append(model_name)
        ax6.boxplot(speed_data, labels=labels)
        ax6.set_ylabel('速度 (m/s)', fontsize=14)
        ax6.set_title('速度分布箱线图', fontsize=16)
        ax6.grid(True, alpha=0.3)
        plt.setp(ax6.get_xticklabels(), rotation=45)
        
        # 7. 加速度分布对比
        ax7 = plt.subplot(3, 3, 7)
        for model_name, df in self.models.items():
            ax7.hist(df['acceleration_ms2'], bins=50, alpha=0.6, label=model_name, density=True)
        ax7.set_xlabel('加速度 (m/s²)', fontsize=14)
        ax7.set_ylabel('密度', fontsize=14)
        ax7.set_title('加速度分布对比', fontsize=16)
        ax7.legend(fontsize=12)
        ax7.grid(True, alpha=0.3)
        
        # 8. 速度-加速度散点图
        ax8 = plt.subplot(3, 3, 8)
        for i, (model_name, df) in enumerate(self.models.items()):
            ax8.scatter(df['speed_ms'], df['acceleration_ms2'], 
                       label=model_name, alpha=0.6, s=1, color=colors[i % len(colors)])
        ax8.set_xlabel('速度 (m/s)', fontsize=14)
        ax8.set_ylabel('加速度 (m/s²)', fontsize=14)
        ax8.set_title('速度-加速度关系', fontsize=16)
        ax8.legend(fontsize=12)
        ax8.grid(True, alpha=0.3)
        
        # 9. 统计摘要表格
        ax9 = plt.subplot(3, 3, 9)
        ax9.axis('tight')
        ax9.axis('off')
        
        stats_df = self.calculate_statistics()
        table = ax9.table(cellText=stats_df.round(2).values,
                         colLabels=stats_df.columns,
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        ax9.set_title('统计摘要', fontsize=16, pad=20)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"综合对比图已保存: {output_file}")
        
        return fig
    
    def generate_comparison_report(self, output_file):
        """生成对比分析报告"""
        report = []
        report.append("# 轨迹生成模型综合对比分析报告\n")
        report.append(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append(f"对比模型数量: {len(self.models)}\n\n")
        
        # 1. 基本统计
        report.append("## 1. 基本统计指标\n")
        stats_df = self.calculate_statistics()
        report.append(stats_df.to_string(index=False))
        report.append("\n\n")
        
        # 2. 模型特征分析
        report.append("## 2. 模型特征分析\n")
        for model_name, df in self.models.items():
            report.append(f"### {model_name}\n")
            report.append(f"- 平均速度: {df['speed_ms'].mean():.2f} m/s ({df['speed_ms'].mean()*3.6:.2f} km/h)\n")
            report.append(f"- 速度变异系数: {df['speed_ms'].std()/df['speed_ms'].mean():.3f}\n")
            report.append(f"- 加速度范围: {df['acceleration_ms2'].min():.2f} ~ {df['acceleration_ms2'].max():.2f} m/s²\n")
            
            # 速度变化平滑性
            speed_changes = np.abs(np.diff(df['speed_ms']))
            smoothness = 1 / (1 + speed_changes.mean())
            report.append(f"- 速度变化平滑性: {smoothness:.3f} (越接近1越平滑)\n\n")
        
        # 3. 相关性分析
        correlations = self.calculate_correlations()
        if correlations:
            report.append("## 3. 模型间相关性分析\n")
            for pair, corr_data in correlations.items():
                report.append(f"### {pair}\n")
                report.append(f"- 相关系数: {corr_data['相关系数']:.4f}\n")
                report.append(f"- 显著性: {corr_data['显著性']}\n")
                
                # 相关性解释
                corr_val = abs(corr_data['相关系数'])
                if corr_val > 0.8:
                    interpretation = "强相关"
                elif corr_val > 0.6:
                    interpretation = "中等相关"
                elif corr_val > 0.3:
                    interpretation = "弱相关"
                else:
                    interpretation = "几乎无相关"
                report.append(f"- 相关性程度: {interpretation}\n\n")
        
        # 4. 物理合理性评估
        report.append("## 4. 物理合理性评估\n")
        for model_name, df in self.models.items():
            report.append(f"### {model_name}\n")
            
            # 速度合理性
            max_speed_kmh = df['speed_ms'].max() * 3.6
            if max_speed_kmh > 120:
                speed_assessment = "过高，可能不现实"
            elif max_speed_kmh > 80:
                speed_assessment = "较高，需要验证"
            else:
                speed_assessment = "合理"
            report.append(f"- 最大速度评估: {max_speed_kmh:.1f} km/h - {speed_assessment}\n")
            
            # 加速度合理性
            max_accel = df['acceleration_ms2'].max()
            if max_accel > 5:
                accel_assessment = "过高，超出车辆物理极限"
            elif max_accel > 3:
                accel_assessment = "较高，接近极限"
            else:
                accel_assessment = "合理"
            report.append(f"- 最大加速度评估: {max_accel:.2f} m/s² - {accel_assessment}\n\n")
        
        # 5. 推荐结论
        report.append("## 5. 推荐结论\n")
        
        # 找出最平滑的模型
        smoothness_scores = {}
        for model_name, df in self.models.items():
            speed_changes = np.abs(np.diff(df['speed_ms']))
            smoothness_scores[model_name] = 1 / (1 + speed_changes.mean())
        
        smoothest_model = max(smoothness_scores, key=smoothness_scores.get)
        report.append(f"- 最平滑的模型: {smoothest_model} (平滑性得分: {smoothness_scores[smoothest_model]:.3f})\n")
        
        # 找出最具物理合理性的模型
        physics_scores = {}
        for model_name, df in self.models.items():
            max_speed_penalty = max(0, (df['speed_ms'].max() * 3.6 - 80) / 40)  # 80km/h以上开始扣分
            max_accel_penalty = max(0, (df['acceleration_ms2'].max() - 3) / 2)  # 3m/s²以上开始扣分
            physics_scores[model_name] = 1 - (max_speed_penalty + max_accel_penalty) / 2
        
        most_physical_model = max(physics_scores, key=physics_scores.get)
        report.append(f"- 最符合物理规律的模型: {most_physical_model} (物理合理性得分: {physics_scores[most_physical_model]:.3f})\n")
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(''.join(report))
        
        print(f"对比分析报告已保存: {output_file}")
        return ''.join(report)

def main():
    parser = argparse.ArgumentParser(description="综合模型对比分析")
    parser.add_argument('--original_file', help='原始轨迹文件路径')
    parser.add_argument('--inertial_file', help='惯性模型轨迹文件路径')
    parser.add_argument('--physics_file', help='物理模型轨迹文件路径')
    parser.add_argument('--output_dir', default='model_comparison_results', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化对比器
    comparator = ModelComparator()
    
    # 加载数据
    models_loaded = 0
    if args.original_file and os.path.exists(args.original_file):
        if comparator.load_trajectory_data(args.original_file, "原始轨迹"):
            models_loaded += 1
    
    if args.inertial_file and os.path.exists(args.inertial_file):
        if comparator.load_trajectory_data(args.inertial_file, "惯性模型"):
            models_loaded += 1
    
    if args.physics_file and os.path.exists(args.physics_file):
        if comparator.load_trajectory_data(args.physics_file, "物理模型"):
            models_loaded += 1
    
    if models_loaded < 2:
        print("至少需要两个模型的数据才能进行对比分析")
        return
    
    print(f"成功加载 {models_loaded} 个模型的数据")
    
    # 生成对比图
    plot_file = os.path.join(args.output_dir, "comprehensive_model_comparison.png")
    comparator.plot_comprehensive_comparison(plot_file)
    
    # 生成分析报告
    report_file = os.path.join(args.output_dir, "model_comparison_report.md")
    comparator.generate_comparison_report(report_file)
    
    # 输出统计摘要
    print("\n=== 统计摘要 ===")
    stats_df = comparator.calculate_statistics()
    print(stats_df.to_string(index=False))

if __name__ == '__main__':
    main() 