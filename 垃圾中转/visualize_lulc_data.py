import os
import rasterio
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap, BoundaryNorm
import cartopy.crs as ccrs
from cartopy.feature import BORDERS, COASTLINE
from rasterio.warp import transform_bounds

def get_lulc_colormap_and_labels():
    """
    根据项目规范定义地表覆盖类型颜色和标签。
    返回一个有序的列表，确保颜色、分类号和标签一一对应。
    """
    # 规范中定义的原始数据
    spec = {
        10: ('水域', '#0077BE'),
        20: ('湿地', '#80CCFF'),
        30: ('草地', '#90EE90'),
        40: ('灌木地', '#228B22'),
        50: ('建筑用地', '#CD5C5C'),
        60: ('农田', '#FFD700'),
        80: ('森林', '#006400'),
        90: ('荒地', '#DEB887'),
        255: ('未分类', '#808080')
    }
    
    # 按分类号排序，确保顺序永远正确
    sorted_classes = sorted(spec.keys())
    
    # 创建有序的列表
    classes = sorted_classes
    labels = [spec[key][0] for key in sorted_classes]
    colors = [spec[key][1] for key in sorted_classes]
    
    # 创建matplotlib所需的colormap和norm
    cmap = ListedColormap(colors)
    # 边界需要比颜色多一个，这里我们用一个简单的方法来定义边界
    # 让每个分类号都落在两个边界值之间
    bounds = [c - 0.5 for c in classes] + [255.5]
    norm = BoundaryNorm(bounds, cmap.N)
    
    return cmap, norm, classes, labels

def visualize_lulc_file(tiff_path, output_dir):
    """读取LULC文件，打印信息并可视化。"""
    if not os.path.exists(tiff_path):
        print(f"错误: 文件不存在 {tiff_path}")
        return
        
    plt.rc('font', family='WenQuanYi Micro Hei')

    try:
        with rasterio.open(tiff_path) as src:
            src_crs = src.crs
            src_bounds = src.bounds
            
            # 转换为WGS84经纬度
            lonlat_bounds = transform_bounds(src_crs, 'EPSG:4326', *src_bounds)
            
            print(f"--- 文件: {os.path.basename(tiff_path)} ---")
            print(f"原始投影坐标系: {src_crs.to_string()}")
            print(f"转换后坐标范围 (经度): {lonlat_bounds[0]:.2f}° to {lonlat_bounds[2]:.2f}°")
            print(f"转换后坐标范围 (纬度): {lonlat_bounds[1]:.2f}° to {lonlat_bounds[3]:.2f}°")
            print("-" * (len(os.path.basename(tiff_path)) + 10))

            data = src.read(1)
            
            fig = plt.figure(figsize=(12, 12))
            ax = fig.add_subplot(1, 1, 1, projection=ccrs.PlateCarree())
            ax.set_extent([lonlat_bounds[0], lonlat_bounds[2], lonlat_bounds[1], lonlat_bounds[3]], crs=ccrs.PlateCarree())

            cmap, norm, ticks, tick_labels = get_lulc_colormap_and_labels()

            im = ax.imshow(data, origin='upper', extent=[lonlat_bounds[0], lonlat_bounds[2], lonlat_bounds[1], lonlat_bounds[3]], 
                           transform=ccrs.PlateCarree(), cmap=cmap, norm=norm)

            ax.add_feature(COASTLINE, linewidth=0.8)
            ax.add_feature(BORDERS, linestyle=':', linewidth=0.6)
            ax.gridlines(draw_labels=True, dms=True, x_inline=False, y_inline=False)
            
            title = f"地表覆盖类型: {os.path.splitext(os.path.basename(tiff_path))[0]}"
            ax.set_title(title, fontsize=22)
            
            # 创建图例，使用自动生成的ticks和labels确保对应关系正确
            cbar = plt.colorbar(im, ax=ax, orientation='vertical', fraction=0.03, pad=0.04, ticks=ticks)
            cbar.ax.set_yticklabels(tick_labels)
            cbar.ax.tick_params(labelsize=10)

            output_filename = os.path.join(output_dir, f"{os.path.splitext(os.path.basename(tiff_path))[0]}.png")
            plt.savefig(output_filename, dpi=300, bbox_inches='tight')
            print(f"地图已保存到: {output_filename}\n")
            plt.close()

    except Exception as e:
        print(f"处理文件 {tiff_path} 时发生错误: {e}")

if __name__ == "__main__":
    lulc_files_paths = [
        "./data/N30_55_2010LC030/n30_55_2010lc030.tif",
        "./data/N36_30_2010LC030/n36_30_2010lc030.tif",
        "./data/N36_45_2010LC030/n36_45_2010lc030.tif",
        "./data/N38_30_2010LC030/n38_30_2010lc030.tif",
        "./data/N43_30_2010LC030/n43_30_2010lc030.tif"
    ]
    output_visualization_dir = "data/LULC_maps"
    os.makedirs(output_visualization_dir, exist_ok=True)

    for lulc_file in lulc_files_paths:
        visualize_lulc_file(lulc_file, output_visualization_dir)

    print("所有LULC文件处理完毕。") 