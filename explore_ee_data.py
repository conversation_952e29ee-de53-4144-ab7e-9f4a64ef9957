import ee
import os
import numpy as np

# 设置Google Cloud服务账户密钥文件路径
service_account_key_path = "just-landing-468209-u0-d7f8ef6de834.json"

# 使用服务账户进行Earth Engine身份验证
if os.path.exists(service_account_key_path):
    print(f"使用服务账户密钥文件: {service_account_key_path}")
    credentials = ee.ServiceAccountCredentials(
        "<EMAIL>",
        service_account_key_path
    )
    ee.Initialize(credentials)
    print("Earth Engine 初始化成功！")
else:
    print(f"错误：找不到服务账户密钥文件 {service_account_key_path}")
    exit(1)

# 探索可用的Sentinel-2数据集
print("\n=== 探索可用的Sentinel-2数据集 ===")

# 列出所有包含S2的数据集
try:
    datasets = ee.data.listAssets('COPERNICUS')
    print("COPERNICUS下的数据集:")
    for dataset in datasets:
        print(f"  - {dataset}")
except Exception as e:
    print(f"无法列出数据集: {e}")

# 测试不同的数据集
test_datasets = [
    'COPERNICUS/S2_SR_HARMONIZED',
    'COPERNICUS/S2',
    'COPERNICUS/S2_SR',
    'COPERNICUS/S2_HARMONIZED'
]

# 顿巴斯地区的边界框
center_lon, center_lat = 37.8, 48.0
size_km = 50
delta_lat = (size_km / 2) / 111.0
delta_lon = (size_km / 2) / (111.0 * np.cos(np.radians(center_lat)))
min_lon = center_lon - delta_lon
max_lon = center_lon + delta_lon
min_lat = center_lat - delta_lat
max_lat = center_lat + delta_lat

geometry = ee.Geometry.Rectangle([min_lon, min_lat, max_lon, max_lat])

print(f"\n=== 测试顿巴斯地区 ({min_lon:.4f}, {min_lat:.4f}) 到 ({max_lon:.4f}, {max_lat:.4f}) ===")

for dataset_id in test_datasets:
    try:
        print(f"\n测试数据集: {dataset_id}")
        collection = ee.ImageCollection(dataset_id) \
            .filterDate('2023-01-01', '2023-12-31') \
            .filterBounds(geometry)
        
        size = collection.size().getInfo()
        print(f"  找到 {size} 个影像")
        
        if size > 0:
            # 获取第一个影像的信息
            first_img = collection.first()
            print(f"  第一个影像ID: {first_img.get('system:index').getInfo()}")
            
            # 检查云量信息
            try:
                cloud_cover = first_img.get('CLOUD_COVERAGE_MEAN').getInfo()
                print(f"  云量: {cloud_cover}%")
            except:
                print(f"  无法获取云量信息")
                
    except Exception as e:
        print(f"  错误: {e}")

print("\n=== 探索完成 ===") 