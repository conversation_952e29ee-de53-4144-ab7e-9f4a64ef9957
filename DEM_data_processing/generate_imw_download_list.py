 
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import math

from DEM_data_processing.imw import CIM

def get_imw_tile(lat, lon):
    try:
        cim = CIM(lat, lon, scale=1000000)
        code = cim.get_code()
        # The website uses a slightly different format, so we need to adjust the code.
        # Example: NN-30 -> N30
        # Let's re-evaluate this assumption.
        print(f"Original CIM code: {code}")
        parts = code.split('-')
        hemisphere_letter = parts[0][0]
        letter = parts[0][1]
        number = parts[1]

        # This logic is likely still wrong.
        # Let's try a more direct approach based on observation of the website.
        # The website seems to use the letter and the number.
        # For N-hemisphere, it's just the letter. For S-hemisphere, it's S + letter.
        if hemisphere_letter == 'N':
            folder = f"{letter}{number}"
        else:
            folder = f"S{letter}{number}"

        print(f"Calculated folder: {folder}")
        return folder
    except Exception as e:
        print(f"Error calculating IMW tile for {lat}, {lon}: {e}")
        return None


def generate_download_list(regions, output_file):
    base_url = "viewfinderpanoramas.org/dem3"
    with open(output_file, 'w') as f:
        for region_name, (min_lat, max_lat, min_lon, max_lon) in regions.items():
            f.write(f"# {region_name}\n")
            for lat in range(int(min_lat), int(max_lat) + 1):
                for lon in range(int(min_lon), int(max_lon) + 1):
                    imw_folder = get_imw_tile(lat, lon)
                    if imw_folder:
                        lat_str = f"N{lat:02d}" if lat >= 0 else f"S{-lat:02d}"
                        lon_str = f"E{lon:03d}" if lon >= 0 else f"W{-lon:03d}"
                        filename = f"{lat_str}{lon_str}.hgt"
                        # 根据网站结构，IMW文件夹名称可能需要进一步处理
                        # 这是一个简化版本
                        url = f"https://{base_url}/{imw_folder}/{filename}.zip\n"
                        f.write(url)
            f.write("\n")

if __name__ == "__main__":
    regions = {
        "Scottish_Highlands": (55, 59, -6, -3),
        "Israel_Palestine": (29, 34, 34, 36),
        "Kashmir": (32, 37, 72, 80),
        "Taiwan": (21, 26, 119, 122),
        "Korean_Peninsula": (33, 44, 124, 131),
    }
    output_file = "DEM_data_processing/dem_download_list.txt"
    generate_download_list(regions, output_file)
    print(f"Download list generated at {output_file}") 