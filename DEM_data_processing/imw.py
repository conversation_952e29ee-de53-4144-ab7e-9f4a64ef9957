# !/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pymil - Open-source Carte internationale du Monde au Millionième nomenclature encoder
"""

__author__ = "<PERSON>"
__email__ = "<EMAIL>"
__version__ = "0.2"


class CIM(object):
    """
    CIM - Carte Internationale du Monde au millionième is a class for finding the string code that represents a map
    in the international map of the world.
    """
    def __init__(self, lat, lon, scale=25000):
        super(CIM, self).__init__()
        self.latitude = float(lat)
        self.longitude = float(lon)
        self.scale = int(scale)

    @property
    def __geo_interface__(self):
        import json
        return json.loads(self.to_geojson())

    def get_code(self):
        return self._build_code()

    def to_geojson(self):
        """
        Returns a geojson representation of the CIM
        :return: string
        """
        import json
        code = self._build_code()

        return json.dumps({
            'type': 'Feature',
            'bbox': self._get_bbox(),
            'geometry': self._get_geojson_geometry(),
            'properties': {
                'code': code,
                'scale': self.scale,
                'latitude': self.latitude,
                'longitude': self.longitude,
                'parent': self._get_parent_code()
            }
        })

    def _get_parent_code(self):
        return self._build_code(parent=True)

    def _get_geojson_geometry(self):
        """
        Returns a geojson geometry:
        http://geojson.org/geojson-spec.html#geometry-objects
        :return: dict
        """
        return {
            'type': 'Polygon',
            'coordinates': [self._get_polygon_coordinates()]
        }

    def _get_polygon_coordinates(self):
        """
        Returns a list with the polygon coordinates
        :return: list
        """
        min_lon, min_lat, max_lon, max_lat = self._get_bbox()
        return [
            [min_lon, min_lat],
            [max_lon, min_lat],
            [max_lon, max_lat],
            [min_lon, max_lat],
            [min_lon, min_lat]
        ]

    def _get_bbox(self):
        """
        Returns a list with the bounding box of the CIM
        :return: list
        """
        min_lon, min_lat, max_lon, max_lat = self._get_milion()
        if self.scale == 1000000:
            return [min_lon, min_lat, max_lon, max_lat]
        min_lon, min_lat, max_lon, max_lat = self._get_500k(min_lon, min_lat)
        if self.scale == 500000:
            return [min_lon, min_lat, max_lon, max_lat]
        min_lon, min_lat, max_lon, max_lat = self._get_250k(min_lon, min_lat)
        if self.scale == 250000:
            return [min_lon, min_lat, max_lon, max_lat]
        min_lon, min_lat, max_lon, max_lat = self._get_100k(min_lon, min_lat)
        if self.scale == 100000:
            return [min_lon, min_lat, max_lon, max_lat]
        min_lon, min_lat, max_lon, max_lat = self._get_50k(min_lon, min_lat)
        if self.scale == 50000:
            return [min_lon, min_lat, max_lon, max_lat]
        min_lon, min_lat, max_lon, max_lat = self._get_25k(min_lon, min_lat)
        if self.scale == 25000:
            return [min_lon, min_lat, max_lon, max_lat]

    def _build_code(self, parent=False):
        """
        Returns the code for the CIM
        :return: string
        """

        # first part of the code, for the 1:1,000,000 scale
        million_code = self._get_milion_code()
        if self.scale == 1000000:
            return million_code
        elif parent and self.scale < 1000000:
            return million_code

        # second part of the code, for the 1:500,000 scale
        code_500 = self._get_500k_code()
        if self.scale == 500000:
            return "%s-%s" % (million_code, code_500)
        elif parent and self.scale < 500000:
            return "%s-%s" % (million_code, code_500)

        # third part of the code, for the 1:250,000 scale
        code_250 = self._get_250k_code()
        if self.scale == 250000:
            return "%s-%s" % (million_code, code_250)
        elif parent and self.scale < 250000:
            return "%s-%s" % (million_code, code_250)

        # fourth part of the code, for the 1:100,000 scale
        code_100 = self._get_100k_code()
        if self.scale == 100000:
            return "%s-%s-%s" % (million_code, code_250, code_100)
        elif parent and self.scale < 100000:
            return "%s-%s-%s" % (million_code, code_250, code_100)

        # fifth part of the code, for the 1:50,000 scale
        code_50 = self._get_50k_code()
        if self.scale == 50000:
            return "%s-%s-%s-%s" % (million_code, code_250, code_100, code_50)
        elif parent and self.scale < 50000:
            return "%s-%s-%s-%s" % (million_code, code_250, code_100, code_50)

        # sixth part of the code, for the 1:25,000 scale
        code_25 = self._get_25k_code()
        if self.scale == 25000:
            return "%s-%s-%s-%s-%s" % (million_code, code_250, code_100, code_50, code_25)

    def _get_milion_code(self):
        """
        Returns the code for the 1:1,000,000 scale
        :return: string
        """
        # defining the hemisphere for the code calculation
        if self.latitude >= 0:
            hemisphere = "N"
        else:
            hemisphere = "S"

        # getting the letter part of the code
        # from A to V, excluding J
        if self.latitude >= 88 or self.latitude <= -80:
            letter = "Z"
        else:
            if hemisphere == "N":
                # from 0 to 88
                start = 0
            else:
                # from -80 to 0
                start = -80
            letters = "ABCDEFGHIKLMNOPQRSTUV"
            letter = letters[int((self.latitude - start) / 4)]
        # the Z letter is used for areas with latitude > 88 or < -80

        # getting the number part of the code
        # from 1 to 60
        number = int((self.longitude + 180) / 6) + 1

        return "%s%s-%02d" % (hemisphere, letter, number)

    def _get_milion(self):
        """
        Calculates the bounding box of a 1:1,000,000 map
        :return: list
        """
        # defining the hemisphere for the code calculation
        if self.latitude >= 0:
            hemisphere = "N"
        else:
            hemisphere = "S"
        # getting the number part of the code
        # from 1 to 60
        number = int((self.longitude + 180) / 6) + 1
        # getting the letter part of the code
        # from A to V, excluding J
        if self.latitude >= 88 or self.latitude <= -80:
            letter = "Z"
        else:
            if hemisphere == "N":
                # from 0 to 88
                start = 0
            else:
                # from -80 to 0
                start = -80
            letters = "ABCDEFGHIKLMNOPQRSTUV"
            letter = letters[int((self.latitude - start) / 4)]

        max_lat = (letters.find(letter) + 1) * 4
        if hemisphere == "S":
            max_lat = -80 + max_lat
        min_lat = max_lat - 4

        min_lon = (number - 1) * 6 - 180
        max_lon = min_lon + 6
        return [min_lon, min_lat, max_lon, max_lat]

    def _get_500k_code(self):
        """
        Returns the code for the 1:500,000 scale
        :return: string
        """
        min_lon, min_lat, max_lon, max_lat = self._get_milion()

        # the letters are organized in a 2x2 grid (rows x columns)
        # B C
        # A D
        # so we need to know in which quarter of the 1:1,000,000 map the coordinate is
        if self.latitude < (min_lat + 2):
            if self.longitude < (min_lon + 3):
                return "A"
            else:
                return "D"
        else:
            if self.longitude < (min_lon + 3):
                return "B"
            else:
                return "C"

    def _get_500k(self, min_lon, min_lat):
        """
        Calculates the bounding box of a 1:500,000 map
        :param min_lon: float
        :param min_lat: float
        :return: list
        """
        code = self._get_500k_code()
        if code == "A":
            max_lat = min_lat + 2
            max_lon = min_lon + 3
        elif code == "B":
            min_lat += 2
            max_lat = min_lat + 2
            max_lon = min_lon + 3
        elif code == "C":
            min_lat += 2
            max_lat = min_lat + 2
            min_lon += 3
            max_lon = min_lon + 3
        elif code == "D":
            max_lat = min_lat + 2
            min_lon += 3
            max_lon = min_lon + 3
        return [min_lon, min_lat, max_lon, max_lat]

    def _get_250k_code(self):
        """
        Returns the code for the 1:250,000 scale
        :return: string
        """
        min_lon, min_lat, max_lon, max_lat = self._get_milion()

        # the letters are organized in a 4x4 grid (rows x columns)
        # M N O P
        # I J L K
        # E F G H
        # A B C D
        # so we need to know in which 1/16 of the 1:1,000,000 map the coordinate is
        letters = [
            ["A", "B", "C", "D"],
            ["E", "F", "G", "H"],
            ["I", "J", "L", "K"],
            ["M", "N", "O", "P"]
        ]
        # each letter represents a 1x1.5 degree rectangle
        row = int((self.latitude - min_lat))
        col = int((self.longitude - min_lon) / 1.5)
        return letters[row][col]

    def _get_250k(self, min_lon, min_lat):
        """
        Calculates the bounding box of a 1:250,000 map
        :param min_lon: float
        :param min_lat: float
        :return: list
        """
        code = self._get_250k_code()
        letters = [
            ["A", "B", "C", "D"],
            ["E", "F", "G", "H"],
            ["I", "J", "L", "K"],
            ["M", "N", "O", "P"]
        ]
        for i in range(len(letters)):
            if code in letters[i]:
                row = i
                col = letters[i].index(code)
                break
        min_lat += row
        max_lat = min_lat + 1
        min_lon += col * 1.5
        max_lon = min_lon + 1.5
        return [min_lon, min_lat, max_lon, max_lat]

    def _get_100k_code(self):
        """
        Returns the code for the 1:100,000 scale
        :return: string
        """
        min_lon, min_lat, max_lon, max_lat = self._get_250k(*self._get_milion())

        # the numbers are organized in a 2x3 grid (rows x columns)
        # IV V VI
        # I II III
        # so we need to know in which 1/6 of the 1:250,000 map the coordinate is
        numbers = [
            ["I", "II", "III"],
            ["IV", "V", "VI"]
        ]
        # each number represents a 30'x30' rectangle
        row = int((self.latitude - min_lat) * 2)
        col = int((self.longitude - min_lon) * 2)
        return numbers[row][col]

    def _get_100k(self, min_lon, min_lat):
        """
        Calculates the bounding box of a 1:100,000 map
        :param min_lon: float
        :param min_lat: float
        :return: list
        """
        code = self._get_100k_code()
        numbers = [
            ["I", "II", "III"],
            ["IV", "V", "VI"]
        ]
        for i in range(len(numbers)):
            if code in numbers[i]:
                row = i
                col = numbers[i].index(code)
                break
        min_lat += row * 0.5
        max_lat = min_lat + 0.5
        min_lon += col * 0.5
        max_lon = min_lon + 0.5
        return [min_lon, min_lat, max_lon, max_lat]

    def _get_50k_code(self):
        """
        Returns the code for the 1:50,000 scale
        :return: string
        """
        min_lon, min_lat, max_lon, max_lat = self._get_100k(*self._get_250k(*self._get_milion()))

        # the numbers are organized in a 2x2 grid (rows x columns)
        # 2 4
        # 1 3
        # so we need to know in which 1/4 of the 1:100,000 map the coordinate is
        if self.latitude < (min_lat + 0.25):
            if self.longitude < (min_lon + 0.25):
                return "1"
            else:
                return "3"
        else:
            if self.longitude < (min_lon + 0.25):
                return "2"
            else:
                return "4"

    def _get_50k(self, min_lon, min_lat):
        """
        Calculates the bounding box of a 1:50,000 map
        :param min_lon: float
        :param min_lat: float
        :return: list
        """
        code = self._get_50k_code()
        if code == "1":
            max_lat = min_lat + 0.25
            max_lon = min_lon + 0.25
        elif code == "2":
            min_lat += 0.25
            max_lat = min_lat + 0.25
            max_lon = min_lon + 0.25
        elif code == "3":
            max_lat = min_lat + 0.25
            min_lon += 0.25
            max_lon = min_lon + 0.25
        elif code == "4":
            min_lat += 0.25
            max_lat = min_lat + 0.25
            min_lon += 0.25
            max_lon = min_lon + 0.25
        return [min_lon, min_lat, max_lon, max_lat]

    def _get_25k_code(self):
        """
        Returns the code for the 1:25,000 scale
        :return: string
        """
        min_lon, min_lat, max_lon, max_lat = self._get_50k(*self._get_100k(*self._get_250k(*self._get_milion())))

        # the letters are organized in a 2x2 grid (rows x columns)
        # NW NE
        # SW SE
        # so we need to know in which 1/4 of the 1:50,000 map the coordinate is
        if self.latitude < (min_lat + 0.125):
            if self.longitude < (min_lon + 0.125):
                return "SW"
            else:
                return "SE"
        else:
            if self.longitude < (min_lon + 0.125):
                return "NW"
            else:
                return "NE"

    def _get_25k(self, min_lon, min_lat):
        """
        Calculates the bounding box of a 1:25,000 map
        :param min_lon: float
        :param min_lat: float
        :return: list
        """
        code = self._get_25k_code()
        if code == "SW":
            max_lat = min_lat + 0.125
            max_lon = min_lon + 0.125
        elif code == "NW":
            min_lat += 0.125
            max_lat = min_lat + 0.125
            max_lon = min_lon + 0.125
        elif code == "SE":
            max_lat = min_lat + 0.125
            min_lon += 0.125
            max_lon = min_lon + 0.125
        elif code == "NE":
            min_lat += 0.125
            max_lat = min_lat + 0.125
            min_lon += 0.125
            max_lon = min_lon + 0.125
        return [min_lon, min_lat, max_lon, max_lat]


def main():
    import argparse
    parser = argparse.ArgumentParser(description='calculates CIM code for given coordinate pair.')
    parser.add_argument('latitude')
    parser.add_argument('longitude')
    parser.add_argument('--geojson', action='store_true', help='prints geoJSON of the map area instead of the code.')
    parser.add_argument('--scale', type=int, default=25000, help='scale to be encoded, defaults to 1:25.000.')
    args = parser.parse_args()

    cim = CIM(args.latitude, args.longitude, args.scale)
    if args.geojson:
        print(cim.to_geojson())
    else:
        print(cim.get_code())


if __name__ == '__main__':
    main() 