#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级轨迹生成系统 - 快速开始示例

这个脚本演示了如何使用新的控制-运动学-环境仿真生成模型
"""

import os
import sys
from advanced_trajectory_system import AdvancedTrajectorySystem

def quick_demo():
    """快速演示"""
    print("🚀 高级轨迹生成系统 - 快速演示")
    print("=" * 50)
    
    # 初始化系统
    system = AdvancedTrajectorySystem()
    
    # 检查环境数据是否存在
    env_dir = "trajectory_generation_module_pkg/examples/data/environment"
    if not os.path.exists(env_dir):
        print(f"❌ 环境数据目录不存在: {env_dir}")
        print("请确保环境数据文件存在：")
        print("  - dem_aligned.tif")
        print("  - slope_aligned.tif") 
        print("  - aspect_aligned.tif")
        print("  - landcover_aligned.tif")
        return False
        
    # 检查轨迹数据是否存在
    traj_dir = "core_trajectories"
    if not os.path.exists(traj_dir):
        print(f"❌ 轨迹数据目录不存在: {traj_dir}")
        print("请确保存在真实轨迹数据用于训练")
        return False
    
    # 定义起点和终点（请根据实际环境数据范围调整）
    try:
        # 简单测试：只初始化环境地图来获取边界
        system.initialize_components()
        bounds = system.env_maps.bounds
        
        # 在地图范围内选择起点和终点
        start_point = (bounds.left + 1000, bounds.bottom + 1000)
        end_point = (bounds.right - 1000, bounds.top - 1000)
        
        print(f"地图边界: {bounds}")
        print(f"起点: {start_point}")
        print(f"终点: {end_point}")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    # 运行完整流水线
    try:
        print("\n🌟 开始运行完整流水线...")
        
        results = system.run_full_pipeline(
            start_point=start_point,
            end_point=end_point,
            vehicle_type="standard",
            scenario="standard",
            force_retrain=False  # 如果已有模型就不重新训练
        )
        
        print("\n✅ 演示完成！")
        print("生成的文件：")
        output_dir = system.output_dir
        for file in os.listdir(output_dir):
            print(f"  - {file}")
            
        return True
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_different_scenarios():
    """演示不同场景的轨迹生成"""
    print("\n🎭 演示不同场景的轨迹生成")
    print("=" * 50)
    
    system = AdvancedTrajectorySystem()
    system.initialize_components()
    
    # 获取地图边界
    bounds = system.env_maps.bounds
    start_point = (bounds.left + 2000, bounds.bottom + 2000)
    end_point = (bounds.right - 2000, bounds.top - 2000)
    
    # 不同的车辆类型和场景组合
    scenarios = [
        ("standard", "standard"),
        ("high_mobility", "high_mobility"), 
        ("stealth", "stealth_priority"),
        ("mountain", "mountain_special")
    ]
    
    for vehicle_type, scenario in scenarios:
        print(f"\n🚗 生成 {vehicle_type} 车辆在 {scenario} 场景下的轨迹...")
        
        try:
            results = system.generate_trajectories(
                start_point=start_point,
                end_point=end_point,
                vehicle_type=vehicle_type,
                scenario=scenario,
                num_samples=2,  # 每种情况生成2条轨迹
                visualize=True
            )
            
            print(f"✅ {vehicle_type} - {scenario} 轨迹生成完成")
            
        except Exception as e:
            print(f"❌ {vehicle_type} - {scenario} 生成失败: {e}")

def demonstrate_key_features():
    """演示系统的关键特性"""
    print("\n🔬 系统关键特性演示")
    print("=" * 50)
    
    print("本系统的核心创新：")
    print()
    print("1. 🧠 深度学习控制预测器")
    print("   - 基于Transformer的序列建模")
    print("   - 多模态输出（MDN）支持轨迹多样性")
    print("   - 融合历史运动学、环境特征、路径引导")
    print()
    
    print("2. 🏗️ 分层系统架构") 
    print("   - 阶段1：高分辨率环境数据预处理")
    print("   - 阶段2：分层A*路径规划")
    print("   - 阶段3：深度学习控制策略预测")
    print("   - 阶段4：物理约束轨迹仿真")
    print()
    
    print("3. 🎯 真实感提升")
    print("   - 初始条件对轨迹的持续影响")
    print("   - 车辆类型差异化建模")
    print("   - 环境物理约束严格执行")
    print("   - 控制-运动学-环境耦合仿真")
    print()
    
    print("4. 🔄 多样性保证")
    print("   - 概率分布采样而非确定性输出")
    print("   - 不同初始状态产生不同轨迹")
    print("   - 车辆类型和场景的组合效应")

if __name__ == "__main__":
    print("🌟 高级轨迹生成系统 - 快速开始")
    print("基于深度学习的控制-运动学-环境仿真模型")
    print("=" * 60)
    
    # 检查Python版本和依赖
    print(f"Python版本: {sys.version}")
    
    # 检查关键依赖
    try:
        import torch
        import numpy as np
        import pandas as pd
        import rasterio
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ 其他依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装requirements.txt中的依赖")
        sys.exit(1)
    
    print("\n选择演示模式：")
    print("1. 快速演示（推荐）")
    print("2. 多场景对比演示")
    print("3. 系统特性说明")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            success = quick_demo()
            if success:
                print("\n🎉 快速演示成功完成！")
                print("您可以在 'advanced_trajectory_output' 目录中查看生成的轨迹和可视化结果")
            else:
                print("\n❌ 演示失败，请检查数据文件和环境配置")
                
        elif choice == "2":
            demo_different_scenarios()
            print("\n🎉 多场景演示完成！")
            
        elif choice == "3":
            demonstrate_key_features()
            
        elif choice == "4":
            print("👋 再见！")
            
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc() 