#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证和清理脚本
逐个检查文件，确认哪些可以正常使用，删除损坏的文件
"""

import os
import shutil
import rasterio
import json
import numpy as np
from rasterio.errors import RasterioIOError
import warnings
warnings.filterwarnings('ignore')

def test_raster_file(file_path, file_type):
    """测试栅格文件是否可以正常读取"""
    print(f"\n--- 测试 {file_type} 文件: {file_path} ---")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在")
        return False, "文件不存在"
    
    try:
        with rasterio.open(file_path) as src:
            print(f"✅ 文件可以打开")
            print(f"  尺寸: {src.width} x {src.height}")
            print(f"  CRS: {src.crs}")
            print(f"  边界: {src.bounds}")
            
            # 尝试读取一小块数据
            try:
                # 读取中心区域的一小块数据
                center_row = src.height // 2
                center_col = src.width // 2
                window_size = min(50, src.height // 8, src.width // 8)
                
                window = rasterio.windows.Window(
                    center_col - window_size // 2,
                    center_row - window_size // 2,
                    window_size,
                    window_size
                )
                
                data = src.read(1, window=window)
                print(f"✅ 数据读取成功")
                print(f"  读取数据形状: {data.shape}")
                print(f"  数据范围: {np.min(data)} 到 {np.max(data)}")
                
                # 检查地理范围是否合理
                bounds = src.bounds
                lon_range = bounds.right - bounds.left
                lat_range = bounds.top - bounds.bottom
                
                if lon_range > 180 or lat_range > 90:
                    print(f"⚠️  地理范围异常: 经度{lon_range:.2f}度, 纬度{lat_range:.2f}度")
                    return False, "地理范围异常"
                elif lon_range < 0.001 or lat_range < 0.001:
                    print(f"⚠️  地理范围过小: 经度{lon_range:.6f}度, 纬度{lat_range:.6f}度")
                    return False, "地理范围过小"
                else:
                    print(f"✅ 地理范围合理: 经度{lon_range:.2f}度, 纬度{lat_range:.2f}度")
                
                return True, "正常"
                
            except Exception as e:
                print(f"❌ 数据读取失败: {e}")
                return False, f"数据读取失败: {e}"
                
    except RasterioIOError as e:
        print(f"❌ RasterioIOError: {e}")
        return False, f"RasterioIOError: {e}"
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False, f"其他错误: {e}"

def test_json_file(file_path):
    """测试JSON文件是否可以正常读取"""
    print(f"\n--- 测试JSON文件: {file_path} ---")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在")
        return False, "文件不存在"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ JSON文件可以正常读取")
        print(f"  数据类型: {type(data)}")
        if isinstance(data, dict):
            print(f"  键数量: {len(data)}")
        elif isinstance(data, list):
            print(f"  列表长度: {len(data)}")
        return True, "正常"
    except Exception as e:
        print(f"❌ JSON读取失败: {e}")
        return False, f"JSON读取失败: {e}"

def backup_and_delete_file(file_path, reason):
    """备份并删除文件"""
    backup_dir = "data/backup_corrupted_files"
    os.makedirs(backup_dir, exist_ok=True)
    
    # 创建备份路径
    relative_path = os.path.relpath(file_path, "data")
    backup_path = os.path.join(backup_dir, relative_path)
    
    # 确保备份目录存在
    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
    
    try:
        # 移动文件到备份目录
        shutil.move(file_path, backup_path)
        print(f"✅ 已备份并删除: {file_path}")
        print(f"  备份位置: {backup_path}")
        print(f"  原因: {reason}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 数据验证和清理 ===")
    
    # 要检查的文件列表
    files_to_check = [
        # TIF文件
        ("data/以色列-巴勒斯坦地区/lulc.tif", "LULC"),
        ("data/以色列-巴勒斯坦地区/temp_dem_mosaic.tif", "DEM"),
        ("data/克什米尔地区/lulc.tif", "LULC"),
        ("data/克什米尔地区/temp_dem_mosaic.tif", "DEM"),
        ("data/苏格兰高地/lulc.tif", "LULC"),
        ("data/苏格兰高地/temp_dem_mosaic.tif", "DEM"),
        
        # JSON文件
        ("data/road_networks/以色列-巴勒斯坦地区_roads.json", "JSON"),
        ("data/road_networks/克什米尔地区_roads.json", "JSON"),
        ("data/road_networks/苏格兰高地_roads.json", "JSON"),
        
        # 卫星云图JSON文件
        ("data/satellite_imagery/以色列-巴勒斯坦地区_cloud_cover_2025-08-05.json", "JSON"),
        ("data/satellite_imagery/克什米尔地区_cloud_cover_2025-08-05.json", "JSON"),
        ("data/satellite_imagery/苏格兰高地_cloud_cover_2025-08-05.json", "JSON"),
    ]
    
    results = {}
    files_to_delete = []
    
    print(f"\n开始检查 {len(files_to_check)} 个文件...")
    
    for file_path, file_type in files_to_check:
        if file_type in ["LULC", "DEM"]:
            is_valid, reason = test_raster_file(file_path, file_type)
        elif file_type == "JSON":
            is_valid, reason = test_json_file(file_path)
        else:
            is_valid, reason = False, "未知文件类型"
        
        results[file_path] = {
            'valid': is_valid,
            'reason': reason,
            'type': file_type
        }
        
        if not is_valid:
            files_to_delete.append((file_path, reason))
    
    # 显示结果统计
    print(f"\n{'='*60}")
    print("检查结果统计")
    print(f"{'='*60}")
    
    valid_count = sum(1 for r in results.values() if r['valid'])
    total_count = len(results)
    
    print(f"总文件数: {total_count}")
    print(f"有效文件数: {valid_count}")
    print(f"损坏文件数: {total_count - valid_count}")
    
    # 显示有效文件
    print(f"\n✅ 有效文件:")
    for file_path, result in results.items():
        if result['valid']:
            print(f"  {file_path} ({result['type']})")
    
    # 显示损坏文件
    if files_to_delete:
        print(f"\n❌ 损坏文件:")
        for file_path, reason in files_to_delete:
            print(f"  {file_path}")
            print(f"    原因: {reason}")
    
    # 询问是否删除损坏文件
    if files_to_delete:
        print(f"\n{'='*60}")
        print("是否要备份并删除损坏的文件？")
        print(f"将删除 {len(files_to_delete)} 个损坏文件")
        print("文件将被移动到 data/backup_corrupted_files/ 目录")
        
        # 自动执行删除（因为用户已经确认要清理）
        print(f"\n开始清理损坏文件...")
        
        deleted_count = 0
        for file_path, reason in files_to_delete:
            if backup_and_delete_file(file_path, reason):
                deleted_count += 1
        
        print(f"\n清理完成！成功删除 {deleted_count}/{len(files_to_delete)} 个损坏文件")
    else:
        print(f"\n✅ 所有文件都正常，无需清理")
    
    # 生成清理报告
    report = {
        "检查时间": "2025-08-07",
        "总文件数": total_count,
        "有效文件数": valid_count,
        "损坏文件数": total_count - valid_count,
        "有效文件": [f for f, r in results.items() if r['valid']],
        "损坏文件": [{"file": f, "reason": r} for f, r in files_to_delete]
    }
    
    with open("data/data_cleanup_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n清理报告已保存到: data/data_cleanup_report.json")

if __name__ == "__main__":
    main() 