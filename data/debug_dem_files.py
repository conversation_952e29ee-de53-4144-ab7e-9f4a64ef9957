#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试DEM文件脚本
检查DEM文件的实际内容和问题
"""

import rasterio
import numpy as np
import matplotlib.pyplot as plt
from rasterio.plot import show
import warnings
warnings.filterwarnings('ignore')

def inspect_dem_file(file_path, region_name):
    """详细检查DEM文件"""
    print(f"\n=== 检查 {region_name} DEM文件 ===")
    print(f"文件路径: {file_path}")
    
    try:
        with rasterio.open(file_path) as src:
            print(f"文件大小: {src.width} x {src.height}")
            print(f"坐标参考系统: {src.crs}")
            print(f"变换参数: {src.transform}")
            print(f"边界: {src.bounds}")
            print(f"数据类型: {src.dtypes}")
            print(f"波段数: {src.count}")
            
            # 读取数据
            data = src.read(1)  # 读取第一个波段
            print(f"数据形状: {data.shape}")
            print(f"数据范围: {np.min(data)} 到 {np.max(data)}")
            print(f"有效数据数量: {np.sum(~np.isnan(data))}")
            print(f"无效数据数量: {np.sum(np.isnan(data))}")
            
            # 检查是否有异常值
            valid_data = data[~np.isnan(data)]
            if len(valid_data) > 0:
                print(f"有效数据统计:")
                print(f"  平均值: {np.mean(valid_data):.2f}")
                print(f"  标准差: {np.std(valid_data):.2f}")
                print(f"  中位数: {np.median(valid_data):.2f}")
                print(f"  最小值: {np.min(valid_data):.2f}")
                print(f"  最大值: {np.max(valid_data):.2f}")
            
            # 检查地理范围是否合理
            bounds = src.bounds
            lon_range = bounds.right - bounds.left
            lat_range = bounds.top - bounds.bottom
            
            print(f"\n地理范围分析:")
            print(f"经度范围: {bounds.left:.6f} 到 {bounds.right:.6f} (跨度: {lon_range:.6f}度)")
            print(f"纬度范围: {bounds.bottom:.6f} 到 {bounds.top:.6f} (跨度: {lat_range:.6f}度)")
            
            # 判断是否合理
            if lon_range > 180 or lat_range > 90:
                print("❌ 警告: 地理范围异常，可能覆盖了全球或更大范围")
            elif lon_range > 10 or lat_range > 10:
                print("⚠️  注意: 地理范围较大，可能不是预期的区域数据")
            else:
                print("✅ 地理范围看起来合理")
            
            return {
                'bounds': bounds,
                'data': data,
                'transform': src.transform,
                'crs': src.crs,
                'width': src.width,
                'height': src.height
            }
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def compare_dem_files():
    """比较所有DEM文件"""
    regions = ['以色列-巴勒斯坦地区', '克什米尔地区', '苏格兰高地']
    
    results = {}
    
    for region in regions:
        dem_path = f"data/{region}/temp_dem_mosaic.tif"
        result = inspect_dem_file(dem_path, region)
        results[region] = result
    
    # 比较结果
    print("\n" + "="*60)
    print("DEM文件比较结果")
    print("="*60)
    
    for region, result in results.items():
        if result:
            bounds = result['bounds']
            print(f"\n{region}:")
            print(f"  经度: {bounds.left:.6f} 到 {bounds.right:.6f}")
            print(f"  纬度: {bounds.bottom:.6f} 到 {bounds.top:.6f}")
            print(f"  大小: {result['width']} x {result['height']}")
    
    # 检查是否所有文件都相同
    if len(results) > 1:
        first_bounds = None
        all_same = True
        
        for region, result in results.items():
            if result:
                if first_bounds is None:
                    first_bounds = result['bounds']
                elif (abs(result['bounds'].left - first_bounds.left) > 1e-6 or
                      abs(result['bounds'].bottom - first_bounds.bottom) > 1e-6 or
                      abs(result['bounds'].right - first_bounds.right) > 1e-6 or
                      abs(result['bounds'].top - first_bounds.top) > 1e-6):
                    all_same = False
                    break
        
        if all_same:
            print("\n❌ 严重问题: 所有DEM文件都有相同的地理范围！")
            print("这表明DEM文件可能有问题，需要重新处理。")

if __name__ == "__main__":
    compare_dem_files() 