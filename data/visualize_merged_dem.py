import os
import rasterio
from rasterio.merge import merge
import matplotlib.pyplot as plt
import numpy as np

def visualize_dem(input_dir, output_path):
    """
    Merges DEM tiles from a directory and visualizes them.

    Args:
        input_dir (str): Directory containing DEM .tif files.
        output_path (str): Path to save the output visualization PNG.
    """
    dem_files = [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f.endswith('.tif')]

    if not dem_files:
        print(f"No DEM files found in {input_dir}")
        return

    src_files_to_mosaic = []
    for fp in dem_files:
        src = rasterio.open(fp)
        src_files_to_mosaic.append(src)

    mosaic, out_trans = merge(src_files_to_mosaic)

    # Close the file handles
    for src in src_files_to_mosaic:
        src.close()

    # Create figure
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # Use 'terrain' colormap
    # Handle potential nodata values by masking them
    masked_data = np.ma.masked_where(mosaic[0] <= -32767, mosaic[0]) # Common nodata value for DEMs
    
    im = ax.imshow(masked_data, cmap='terrain')

    ax.set_title('苏格兰高地 数字高程模型(DEM)', fontsize=22)
    ax.set_xlabel('经度方向像素')
    ax.set_ylabel('纬度方向像素')
    
    # Add colorbar
    cbar = fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
    cbar.set_label('高程 (米)')

    plt.tight_layout()
    plt.savefig(output_path, dpi=300)
    plt.close()
    print(f"Visualization saved to {output_path}")

if __name__ == '__main__':
    dem_input_directory = 'data/DEM/Scottish_Highlands'
    output_visualization_path = 'data/Scottish_Highlands_DEM.png'
    visualize_dem(dem_input_directory, output_visualization_path) 