#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据对齐检查脚本
检查DEM、LULC和路网数据的地理覆盖范围是否一致
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import rasterio
from rasterio.plot import show
import geopandas as gpd
from shapely.geometry import box
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def get_raster_bounds(file_path):
    """获取栅格数据的边界信息"""
    try:
        with rasterio.open(file_path) as src:
            bounds = src.bounds
            crs = src.crs
            return {
                'left': bounds.left,
                'bottom': bounds.bottom,
                'right': bounds.right,
                'top': bounds.top,
                'crs': str(crs),
                'width': src.width,
                'height': src.height,
                'res': src.res
            }
    except Exception as e:
        print(f"读取栅格文件失败 {file_path}: {e}")
        return None

def get_road_network_bounds(file_path):
    """获取路网数据的边界信息"""
    try:
        gdf = gpd.read_file(file_path)
        bounds = gdf.total_bounds
        return {
            'left': bounds[0],
            'bottom': bounds[1],
            'right': bounds[2],
            'top': bounds[3],
            'crs': str(gdf.crs),
            'feature_count': len(gdf)
        }
    except Exception as e:
        print(f"读取路网文件失败 {file_path}: {e}")
        return None

def check_data_alignment():
    """检查数据对齐情况"""
    regions = ['以色列-巴勒斯坦地区', '克什米尔地区', '苏格兰高地']
    
    results = {}
    
    for region in regions:
        print(f"\n=== 检查 {region} 数据 ===")
        
        # 文件路径
        dem_path = f"data/{region}/temp_dem_mosaic.tif"
        lulc_path = f"data/{region}/lulc.tif"
        road_path = f"data/road_networks/{region}_roads.json"
        
        # 检查文件是否存在
        files_exist = {
            'DEM': os.path.exists(dem_path),
            'LULC': os.path.exists(lulc_path),
            '路网': os.path.exists(road_path)
        }
        
        print(f"文件存在性: {files_exist}")
        
        if not all(files_exist.values()):
            print(f"⚠️  {region} 缺少必要文件")
            continue
        
        # 获取边界信息
        dem_bounds = get_raster_bounds(dem_path)
        lulc_bounds = get_raster_bounds(lulc_path)
        road_bounds = get_road_network_bounds(road_path)
        
        results[region] = {
            'files_exist': files_exist,
            'dem_bounds': dem_bounds,
            'lulc_bounds': lulc_bounds,
            'road_bounds': road_bounds
        }
        
        if dem_bounds and lulc_bounds:
            # 检查DEM和LULC是否对齐
            dem_lulc_aligned = (
                abs(dem_bounds['left'] - lulc_bounds['left']) < 1e-6 and
                abs(dem_bounds['bottom'] - lulc_bounds['bottom']) < 1e-6 and
                abs(dem_bounds['right'] - lulc_bounds['right']) < 1e-6 and
                abs(dem_bounds['top'] - lulc_bounds['top']) < 1e-6
            )
            
            print(f"DEM-LULC对齐: {'✅' if dem_lulc_aligned else '❌'}")
            if not dem_lulc_aligned:
                print(f"  DEM边界: {dem_bounds}")
                print(f"  LULC边界: {lulc_bounds}")
        
        if road_bounds:
            print(f"路网特征数: {road_bounds['feature_count']}")
            print(f"路网边界: {road_bounds}")
    
    return results

def visualize_data_alignment(results):
    """可视化数据对齐情况"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('数据覆盖范围检查', fontsize=22, fontweight='bold')
    
    regions = list(results.keys())
    
    for i, region in enumerate(regions):
        ax = axes[i]
        ax.set_title(f'{region}', fontsize=20)
        
        data = results[region]
        
        # 绘制DEM和LULC边界（应该相同）
        if data['dem_bounds'] and data['lulc_bounds']:
            dem_bounds = data['dem_bounds']
            lulc_bounds = data['lulc_bounds']
            
            # DEM边界（蓝色）
            dem_rect = patches.Rectangle(
                (dem_bounds['left'], dem_bounds['bottom']),
                dem_bounds['right'] - dem_bounds['left'],
                dem_bounds['top'] - dem_bounds['bottom'],
                linewidth=2, edgecolor='blue', facecolor='none', 
                label='DEM边界', alpha=0.8
            )
            ax.add_patch(dem_rect)
            
            # LULC边界（红色）
            lulc_rect = patches.Rectangle(
                (lulc_bounds['left'], lulc_bounds['bottom']),
                lulc_bounds['right'] - lulc_bounds['left'],
                lulc_bounds['top'] - lulc_bounds['bottom'],
                linewidth=2, edgecolor='red', facecolor='none', 
                label='LULC边界', alpha=0.8
            )
            ax.add_patch(lulc_rect)
            
            # 路网边界（绿色）
            if data['road_bounds']:
                road_bounds = data['road_bounds']
                road_rect = patches.Rectangle(
                    (road_bounds['left'], road_bounds['bottom']),
                    road_bounds['right'] - road_bounds['left'],
                    road_bounds['top'] - road_bounds['bottom'],
                    linewidth=2, edgecolor='green', facecolor='none', 
                    label='路网边界', alpha=0.8
                )
                ax.add_patch(road_rect)
            
            # 设置坐标轴范围
            all_bounds = [dem_bounds, lulc_bounds]
            if data['road_bounds']:
                all_bounds.append(data['road_bounds'])
            
            min_x = min(b['left'] for b in all_bounds)
            max_x = max(b['right'] for b in all_bounds)
            min_y = min(b['bottom'] for b in all_bounds)
            max_y = max(b['top'] for b in all_bounds)
            
            ax.set_xlim(min_x - 0.01, max_x + 0.01)
            ax.set_ylim(min_y - 0.01, max_y + 0.01)
            
            ax.set_xlabel('经度', fontsize=16)
            ax.set_ylabel('纬度', fontsize=16)
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=14)
            
            # 添加对齐状态信息
            dem_lulc_aligned = (
                abs(dem_bounds['left'] - lulc_bounds['left']) < 1e-6 and
                abs(dem_bounds['bottom'] - lulc_bounds['bottom']) < 1e-6 and
                abs(dem_bounds['right'] - lulc_bounds['right']) < 1e-6 and
                abs(dem_bounds['top'] - lulc_bounds['top']) < 1e-6
            )
            
            status_text = f"DEM-LULC对齐: {'✅' if dem_lulc_aligned else '❌'}"
            ax.text(0.02, 0.98, status_text, transform=ax.transAxes, 
                   fontsize=14, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('data/data_alignment_check.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_alignment_report(results):
    """生成对齐检查报告"""
    report = {
        "检查时间": "2025-08-07",
        "检查结果": {}
    }
    
    for region, data in results.items():
        region_report = {
            "文件完整性": data['files_exist'],
            "数据对齐状态": {}
        }
        
        if data['dem_bounds'] and data['lulc_bounds']:
            dem_bounds = data['dem_bounds']
            lulc_bounds = data['lulc_bounds']
            
            # 检查DEM和LULC对齐
            dem_lulc_aligned = (
                abs(dem_bounds['left'] - lulc_bounds['left']) < 1e-6 and
                abs(dem_bounds['bottom'] - lulc_bounds['bottom']) < 1e-6 and
                abs(dem_bounds['right'] - lulc_bounds['right']) < 1e-6 and
                abs(dem_bounds['top'] - lulc_bounds['top']) < 1e-6
            )
            
            region_report["数据对齐状态"]["DEM-LULC对齐"] = dem_lulc_aligned
            
            if dem_lulc_aligned:
                region_report["数据对齐状态"]["覆盖范围"] = {
                    "经度范围": [dem_bounds['left'], dem_bounds['right']],
                    "纬度范围": [dem_bounds['bottom'], dem_bounds['top']],
                    "分辨率": dem_bounds['res'],
                    "网格大小": [dem_bounds['width'], dem_bounds['height']]
                }
        
        if data['road_bounds']:
            region_report["数据对齐状态"]["路网信息"] = {
                "特征数量": data['road_bounds']['feature_count'],
                "覆盖范围": {
                    "经度范围": [data['road_bounds']['left'], data['road_bounds']['right']],
                    "纬度范围": [data['road_bounds']['bottom'], data['road_bounds']['top']]
                }
            }
        
        report["检查结果"][region] = region_report
    
    # 保存报告
    with open('data/data_alignment_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成Markdown报告
    md_report = "# 数据对齐检查报告\n\n"
    md_report += f"**检查时间**: {report['检查时间']}\n\n"
    
    for region, region_data in report["检查结果"].items():
        md_report += f"## {region}\n\n"
        
        # 文件完整性
        md_report += "### 文件完整性\n"
        for file_type, exists in region_data["文件完整性"].items():
            status = "✅" if exists else "❌"
            md_report += f"- {file_type}: {status}\n"
        md_report += "\n"
        
        # 数据对齐状态
        md_report += "### 数据对齐状态\n"
        for status_type, status_data in region_data["数据对齐状态"].items():
            if isinstance(status_data, bool):
                status_icon = "✅" if status_data else "❌"
                md_report += f"- {status_type}: {status_icon}\n"
            elif isinstance(status_data, dict):
                md_report += f"- {status_type}:\n"
                for key, value in status_data.items():
                    md_report += f"  - {key}: {value}\n"
        md_report += "\n"
    
    with open('data/data_alignment_report.md', 'w', encoding='utf-8') as f:
        f.write(md_report)
    
    return report

if __name__ == "__main__":
    print("开始检查数据对齐情况...")
    
    # 检查数据对齐
    results = check_data_alignment()
    
    # 可视化结果
    visualize_data_alignment(results)
    
    # 生成报告
    report = generate_alignment_report(results)
    
    print("\n=== 检查完成 ===")
    print("已生成以下文件:")
    print("- data/data_alignment_check.png (可视化结果)")
    print("- data/data_alignment_report.json (详细报告)")
    print("- data/data_alignment_report.md (Markdown报告)") 