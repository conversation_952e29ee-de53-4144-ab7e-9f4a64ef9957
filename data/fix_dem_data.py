#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复DEM数据脚本
重新生成正确的区域DEM数据，确保与LULC数据对齐
"""

import os
import ee
import geemap
import rasterio
import numpy as np
import matplotlib.pyplot as plt
from rasterio.plot import show
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 定义服务账号密钥文件路径
SERVICE_ACCOUNT_KEY_PATH = '/home/<USER>/下载/just-landing-468209-u0-d7f8ef6de834.json'
SERVICE_ACCOUNT_EMAIL = '<EMAIL>'

def initialize_earth_engine():
    """初始化Earth Engine"""
    try:
        ee.Initialize(ee.ServiceAccountCredentials(
            SERVICE_ACCOUNT_EMAIL,
            key_file=SERVICE_ACCOUNT_KEY_PATH
        ))
        print("✅ Earth Engine 初始化成功")
        return True
    except Exception as e:
        print(f"❌ Earth Engine 初始化失败: {e}")
        return False

def download_correct_dem_data(region_name, bbox, output_dir):
    """
    下载正确的DEM数据
    
    Args:
        region_name (str): 区域名称
        bbox (list): 边界框 [min_lon, min_lat, max_lon, max_lat]
        output_dir (str): 输出目录
    """
    print(f"\n=== 重新下载 {region_name} 的DEM数据 ===")
    
    # 创建区域的几何对象
    region = ee.Geometry.BBox(bbox[0], bbox[1], bbox[2], bbox[3])
    
    # 定义输出路径
    region_output_dir = os.path.join(output_dir, region_name)
    os.makedirs(region_output_dir, exist_ok=True)
    
    # 备份错误的文件
    old_dem_path = os.path.join(region_output_dir, 'temp_dem_mosaic.tif')
    if os.path.exists(old_dem_path):
        backup_path = os.path.join(region_output_dir, 'temp_dem_mosaic_backup.tif')
        os.rename(old_dem_path, backup_path)
        print(f"已备份错误的DEM文件到: {backup_path}")
    
    # 下载DEM数据
    print(f"正在下载 {region_name} 的DEM数据...")
    dem = ee.Image('USGS/SRTMGL1_003')
    dem_output_path = os.path.join(region_output_dir, 'temp_dem_mosaic.tif')
    
    try:
        # 使用更精确的下载参数
        geemap.download_ee_image(
            dem, 
            dem_output_path, 
            scale=30, 
            region=region,
            crs='EPSG:4326'
        )
        print(f"✅ DEM数据已下载到: {dem_output_path}")
        
        # 验证下载的数据
        verify_dem_data(dem_output_path, region_name, bbox)
        
    except Exception as e:
        print(f"❌ 下载 {region_name} 的DEM数据失败: {e}")
        return False
    
    return True

def verify_dem_data(dem_path, region_name, expected_bbox):
    """验证DEM数据的正确性"""
    print(f"\n--- 验证 {region_name} DEM数据 ---")
    
    try:
        with rasterio.open(dem_path) as src:
            bounds = src.bounds
            print(f"文件大小: {src.width} x {src.height}")
            print(f"坐标参考系统: {src.crs}")
            print(f"实际边界: {bounds}")
            
            # 检查地理范围是否合理
            lon_range = bounds.right - bounds.left
            lat_range = bounds.top - bounds.bottom
            
            print(f"经度范围: {bounds.left:.6f} 到 {bounds.right:.6f} (跨度: {lon_range:.6f}度)")
            print(f"纬度范围: {bounds.bottom:.6f} 到 {bounds.top:.6f} (跨度: {lat_range:.6f}度)")
            
            # 检查是否与预期范围匹配
            expected_lon_range = expected_bbox[2] - expected_bbox[0]
            expected_lat_range = expected_bbox[3] - expected_bbox[1]
            
            lon_match = abs(lon_range - expected_lon_range) < 0.1
            lat_match = abs(lat_range - expected_lat_range) < 0.1
            
            if lon_match and lat_match:
                print("✅ DEM数据范围正确")
            else:
                print("⚠️  DEM数据范围与预期不符")
                print(f"预期范围: 经度 {expected_lon_range:.6f}度, 纬度 {expected_lat_range:.6f}度")
            
            # 检查数据质量
            data = src.read(1)
            valid_data = data[~np.isnan(data)]
            if len(valid_data) > 0:
                print(f"数据统计:")
                print(f"  有效像素数: {len(valid_data)}")
                print(f"  高程范围: {np.min(valid_data):.1f} 到 {np.max(valid_data):.1f} 米")
                print(f"  平均高程: {np.mean(valid_data):.1f} 米")
            
    except Exception as e:
        print(f"❌ 验证DEM数据失败: {e}")

def check_lulc_alignment(region_name, dem_path, lulc_path):
    """检查DEM和LULC数据的对齐情况"""
    print(f"\n--- 检查 {region_name} 数据对齐 ---")
    
    try:
        with rasterio.open(dem_path) as dem_src:
            dem_bounds = dem_src.bounds
            dem_crs = dem_src.crs
        
        with rasterio.open(lulc_path) as lulc_src:
            lulc_bounds = lulc_src.bounds
            lulc_crs = lulc_src.crs
        
        print(f"DEM边界: {dem_bounds}")
        print(f"LULC边界: {lulc_bounds}")
        print(f"DEM CRS: {dem_crs}")
        print(f"LULC CRS: {lulc_crs}")
        
        # 检查边界对齐
        bounds_aligned = (
            abs(dem_bounds.left - lulc_bounds.left) < 1e-6 and
            abs(dem_bounds.bottom - lulc_bounds.bottom) < 1e-6 and
            abs(dem_bounds.right - lulc_bounds.right) < 1e-6 and
            abs(dem_bounds.top - lulc_bounds.top) < 1e-6
        )
        
        crs_aligned = str(dem_crs) == str(lulc_crs)
        
        if bounds_aligned and crs_aligned:
            print("✅ DEM和LULC数据完全对齐")
            return True
        else:
            print("❌ DEM和LULC数据未对齐")
            if not bounds_aligned:
                print("  - 边界不匹配")
            if not crs_aligned:
                print("  - 坐标系统不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 检查数据对齐失败: {e}")
        return False

def visualize_fixed_data(region_name, dem_path, lulc_path):
    """可视化修复后的数据"""
    print(f"\n--- 可视化 {region_name} 数据 ---")
    
    try:
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle(f'{region_name} 数据可视化', fontsize=22, fontweight='bold')
        
        # 可视化DEM
        with rasterio.open(dem_path) as src:
            dem_data = src.read(1)
            masked_dem = np.ma.masked_where(dem_data <= -32767, dem_data)
            
            im1 = axes[0].imshow(masked_dem, cmap='terrain')
            axes[0].set_title('数字高程模型 (DEM)', fontsize=20)
            axes[0].set_xlabel('像素列', fontsize=16)
            axes[0].set_ylabel('像素行', fontsize=16)
            
            cbar1 = fig.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)
            cbar1.set_label('高程 (米)', fontsize=16)
        
        # 可视化LULC
        with rasterio.open(lulc_path) as src:
            lulc_data = src.read(1)
            
            # 定义LULC颜色映射
            lulc_colors = {
                10: '#0077BE',  # 水域
                20: '#80CCFF',  # 湿地
                30: '#90EE90',  # 草地
                40: '#228B22',  # 灌木地
                50: '#CD5C5C',  # 建筑用地
                60: '#FFD700',  # 农田
                80: '#006400',  # 森林
                90: '#DEB887',  # 荒地
                255: '#808080'  # 未分类
            }
            
            # 创建颜色映射
            unique_values = np.unique(lulc_data)
            colors = [lulc_colors.get(val, '#808080') for val in unique_values]
            cmap = ListedColormap(colors)
            
            im2 = axes[1].imshow(lulc_data, cmap=cmap, vmin=min(unique_values), vmax=max(unique_values))
            axes[1].set_title('地表覆盖类型 (LULC)', fontsize=20)
            axes[1].set_xlabel('像素列', fontsize=16)
            axes[1].set_ylabel('像素行', fontsize=16)
            
            # 添加图例
            legend_elements = [plt.Rectangle((0,0),1,1, facecolor=lulc_colors.get(val, '#808080'), 
                                           label=f'类型{val}') for val in unique_values]
            axes[1].legend(handles=legend_elements, loc='upper right', fontsize=12)
        
        plt.tight_layout()
        
        # 保存图像
        output_path = f"data/{region_name}/fixed_data_visualization.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化结果已保存到: {output_path}")
        plt.show()
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")

def main():
    """主函数"""
    print("=== 开始修复DEM数据 ===")
    
    # 初始化Earth Engine
    if not initialize_earth_engine():
        return
    
    # 定义区域
    regions = {
        "以色列-巴勒斯坦地区": [34.0, 31.0, 36.0, 33.0],
        "克什米尔地区": [74.0, 33.0, 78.0, 35.0],
        "苏格兰高地": [-6.0, 56.0, -3.0, 58.0]
    }
    
    success_count = 0
    
    for region_name, bbox in regions.items():
        print(f"\n{'='*60}")
        print(f"处理区域: {region_name}")
        print(f"边界框: {bbox}")
        print(f"{'='*60}")
        
        # 检查LULC文件是否存在
        lulc_path = f"data/{region_name}/lulc.tif"
        if not os.path.exists(lulc_path):
            print(f"❌ LULC文件不存在: {lulc_path}")
            continue
        
        # 重新下载DEM数据
        if download_correct_dem_data(region_name, bbox, "data"):
            dem_path = f"data/{region_name}/temp_dem_mosaic.tif"
            
            # 检查数据对齐
            if check_lulc_alignment(region_name, dem_path, lulc_path):
                # 可视化结果
                visualize_fixed_data(region_name, dem_path, lulc_path)
                success_count += 1
                print(f"✅ {region_name} 数据处理完成")
            else:
                print(f"⚠️  {region_name} 数据对齐有问题")
        else:
            print(f"❌ {region_name} 数据处理失败")
    
    print(f"\n{'='*60}")
    print(f"修复完成！成功处理 {success_count}/{len(regions)} 个区域")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 