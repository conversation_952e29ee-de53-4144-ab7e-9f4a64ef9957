#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件问题诊断脚本
详细检查文件的问题所在
"""

import os
import rasterio
import numpy as np
from rasterio.errors import RasterioIOError
import warnings
warnings.filterwarnings('ignore')

def diagnose_file(file_path, file_type):
    """详细诊断单个文件"""
    print(f"\n{'='*60}")
    print(f"诊断 {file_type} 文件: {file_path}")
    print(f"{'='*60}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size / (1024*1024):.2f} MB")
    
    try:
        with rasterio.open(file_path) as src:
            print(f"✅ 文件可以打开")
            print(f"文件大小: {src.width} x {src.height}")
            print(f"坐标参考系统: {src.crs}")
            print(f"变换参数: {src.transform}")
            print(f"边界: {src.bounds}")
            print(f"数据类型: {src.dtypes}")
            print(f"波段数: {src.count}")
            
            # 尝试读取一小块数据
            try:
                # 读取中心区域的一小块数据
                center_row = src.height // 2
                center_col = src.width // 2
                window_size = min(100, src.height // 4, src.width // 4)
                
                window = rasterio.windows.Window(
                    center_col - window_size // 2,
                    center_row - window_size // 2,
                    window_size,
                    window_size
                )
                
                data = src.read(1, window=window)
                print(f"✅ 数据读取成功")
                print(f"读取的数据形状: {data.shape}")
                print(f"数据范围: {np.min(data)} 到 {np.max(data)}")
                print(f"有效数据数量: {np.sum(~np.isnan(data))}")
                
                if file_type == "LULC":
                    unique_values = np.unique(data)
                    print(f"唯一值: {unique_values}")
                
            except Exception as e:
                print(f"❌ 数据读取失败: {e}")
                return False
            
    except RasterioIOError as e:
        print(f"❌ RasterioIOError: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False
    
    return True

def check_geographic_reference(file_path, file_type):
    """检查地理参考信息"""
    print(f"\n--- 检查 {file_type} 地理参考信息 ---")
    
    try:
        with rasterio.open(file_path) as src:
            bounds = src.bounds
            transform = src.transform
            
            print(f"变换矩阵: {transform}")
            print(f"边界: {bounds}")
            
            # 检查变换矩阵是否合理
            if transform[0] == 0 and transform[4] == 0:
                print("❌ 变换矩阵异常: 分辨率为0")
                return False
            
            # 检查边界是否合理
            lon_range = bounds.right - bounds.left
            lat_range = bounds.top - bounds.bottom
            
            print(f"经度范围: {lon_range:.6f}度")
            print(f"纬度范围: {lat_range:.6f}度")
            
            if lon_range > 180 or lat_range > 90:
                print("❌ 地理范围异常: 超出合理范围")
                return False
            elif lon_range < 0.001 or lat_range < 0.001:
                print("⚠️  地理范围过小")
            elif lon_range > 10 or lat_range > 10:
                print("⚠️  地理范围较大")
            else:
                print("✅ 地理范围合理")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查地理参考失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 文件问题诊断 ===")
    
    regions = ['以色列-巴勒斯坦地区', '克什米尔地区', '苏格兰高地']
    
    for region in regions:
        print(f"\n{'='*80}")
        print(f"检查区域: {region}")
        print(f"{'='*80}")
        
        # 检查LULC文件
        lulc_path = f"data/{region}/lulc.tif"
        if os.path.exists(lulc_path):
            lulc_ok = diagnose_file(lulc_path, "LULC")
            if lulc_ok:
                check_geographic_reference(lulc_path, "LULC")
        else:
            print(f"❌ LULC文件不存在: {lulc_path}")
        
        # 检查DEM文件
        dem_path = f"data/{region}/temp_dem_mosaic.tif"
        if os.path.exists(dem_path):
            dem_ok = diagnose_file(dem_path, "DEM")
            if dem_ok:
                check_geographic_reference(dem_path, "DEM")
        else:
            print(f"❌ DEM文件不存在: {dem_path}")
    
    print(f"\n{'='*80}")
    print("诊断完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main() 