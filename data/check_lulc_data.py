#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查LULC数据脚本
验证LULC数据的正确性和地理范围
"""

import os
import rasterio
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def inspect_lulc_file(file_path, region_name):
    """详细检查LULC文件"""
    print(f"\n=== 检查 {region_name} LULC文件 ===")
    print(f"文件路径: {file_path}")
    
    try:
        with rasterio.open(file_path) as src:
            print(f"文件大小: {src.width} x {src.height}")
            print(f"坐标参考系统: {src.crs}")
            print(f"变换参数: {src.transform}")
            print(f"边界: {src.bounds}")
            print(f"数据类型: {src.dtypes}")
            print(f"波段数: {src.count}")
            
            # 读取数据
            data = src.read(1)  # 读取第一个波段
            print(f"数据形状: {data.shape}")
            print(f"数据范围: {np.min(data)} 到 {np.max(data)}")
            
            # 检查唯一值
            unique_values = np.unique(data)
            print(f"唯一值: {unique_values}")
            
            # 统计各类地物
            lulc_classes = {
                10: "水域",
                20: "湿地", 
                30: "草地",
                40: "灌木地",
                50: "建筑用地",
                60: "农田",
                80: "森林",
                90: "荒地",
                255: "未分类"
            }
            
            print(f"\n地物类型统计:")
            for value in unique_values:
                count = np.sum(data == value)
                percentage = (count / data.size) * 100
                class_name = lulc_classes.get(value, f"未知类型{value}")
                print(f"  {class_name} ({value}): {count} 像素 ({percentage:.2f}%)")
            
            # 检查地理范围是否合理
            bounds = src.bounds
            lon_range = bounds.right - bounds.left
            lat_range = bounds.top - bounds.bottom
            
            print(f"\n地理范围分析:")
            print(f"经度范围: {bounds.left:.6f} 到 {bounds.right:.6f} (跨度: {lon_range:.6f}度)")
            print(f"纬度范围: {bounds.bottom:.6f} 到 {bounds.top:.6f} (跨度: {lat_range:.6f}度)")
            
            # 判断是否合理
            if lon_range > 10 or lat_range > 10:
                print("⚠️  注意: 地理范围较大，可能不是预期的区域数据")
            elif lon_range < 0.1 or lat_range < 0.1:
                print("⚠️  注意: 地理范围较小，可能数据不完整")
            else:
                print("✅ 地理范围看起来合理")
            
            return {
                'bounds': bounds,
                'data': data,
                'transform': src.transform,
                'crs': src.crs,
                'width': src.width,
                'height': src.height,
                'unique_values': unique_values
            }
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def visualize_lulc_data(region_name, lulc_path):
    """可视化LULC数据"""
    print(f"\n--- 可视化 {region_name} LULC数据 ---")
    
    try:
        with rasterio.open(lulc_path) as src:
            lulc_data = src.read(1)
            
            # 定义LULC颜色映射
            lulc_colors = {
                10: '#0077BE',  # 水域
                20: '#80CCFF',  # 湿地
                30: '#90EE90',  # 草地
                40: '#228B22',  # 灌木地
                50: '#CD5C5C',  # 建筑用地
                60: '#FFD700',  # 农田
                80: '#006400',  # 森林
                90: '#DEB887',  # 荒地
                255: '#808080'  # 未分类
            }
            
            # 创建颜色映射
            unique_values = np.unique(lulc_data)
            colors = [lulc_colors.get(val, '#808080') for val in unique_values]
            cmap = ListedColormap(colors)
            
            fig, ax = plt.subplots(1, 1, figsize=(12, 10))
            im = ax.imshow(lulc_data, cmap=cmap, vmin=min(unique_values), vmax=max(unique_values))
            ax.set_title(f'{region_name} 地表覆盖类型 (LULC)', fontsize=20)
            ax.set_xlabel('像素列', fontsize=16)
            ax.set_ylabel('像素行', fontsize=16)
            
            # 添加图例
            legend_elements = [plt.Rectangle((0,0),1,1, facecolor=lulc_colors.get(val, '#808080'), 
                                           label=f'类型{val}') for val in unique_values]
            ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
            
            plt.tight_layout()
            
            # 保存图像
            output_path = f"data/{region_name}/lulc_visualization_check.png"
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"✅ LULC可视化结果已保存到: {output_path}")
            plt.show()
            
    except Exception as e:
        print(f"❌ 可视化失败: {e}")

def compare_lulc_files():
    """比较所有LULC文件"""
    regions = ['以色列-巴勒斯坦地区', '克什米尔地区', '苏格兰高地']
    
    results = {}
    
    for region in regions:
        lulc_path = f"data/{region}/lulc.tif"
        result = inspect_lulc_file(lulc_path, region)
        results[region] = result
        
        if result:
            # 可视化LULC数据
            visualize_lulc_data(region, lulc_path)
    
    # 比较结果
    print("\n" + "="*60)
    print("LULC文件比较结果")
    print("="*60)
    
    for region, result in results.items():
        if result:
            bounds = result['bounds']
            print(f"\n{region}:")
            print(f"  经度: {bounds.left:.6f} 到 {bounds.right:.6f}")
            print(f"  纬度: {bounds.bottom:.6f} 到 {bounds.top:.6f}")
            print(f"  大小: {result['width']} x {result['height']}")
            print(f"  地物类型: {result['unique_values']}")
    
    # 检查是否所有文件都相同
    if len(results) > 1:
        first_bounds = None
        all_same = True
        
        for region, result in results.items():
            if result:
                if first_bounds is None:
                    first_bounds = result['bounds']
                elif (abs(result['bounds'].left - first_bounds.left) > 1e-6 or
                      abs(result['bounds'].bottom - first_bounds.bottom) > 1e-6 or
                      abs(result['bounds'].right - first_bounds.right) > 1e-6 or
                      abs(result['bounds'].top - first_bounds.top) > 1e-6):
                    all_same = False
                    break
        
        if all_same:
            print("\n❌ 严重问题: 所有LULC文件都有相同的地理范围！")
        else:
            print("\n✅ LULC文件地理范围正常，各不相同")

if __name__ == "__main__":
    compare_lulc_files() 