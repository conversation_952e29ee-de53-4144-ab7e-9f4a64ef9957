import os
import glob
import rasterio
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap, BoundaryNorm
import cartopy.crs as ccrs
from cartopy.feature import BORDERS, COASTLINE

def get_lulc_colormap_and_labels():
    """根据项目规范定义地表覆盖类型颜色和标签。"""
    spec = {
        10: ('水域', '#0077BE'), 20: ('湿地', '#80CCFF'),
        30: ('草地', '#90EE90'), 40: ('灌木地', '#228B22'),
        50: ('建筑用地', '#CD5C5C'), 60: ('农田', '#FFD700'),
        80: ('森林', '#006400'), 90: ('荒地', '#DEB887'),
        255: ('未分类', '#808080')
    }
    sorted_classes = sorted(spec.keys())
    classes = sorted_classes
    labels = [spec[key][0] for key in sorted_classes]
    colors = [spec[key][1] for key in sorted_classes]
    cmap = ListedColormap(colors)
    bounds = [c - 0.5 for c in classes] + [255.5]
    norm = BoundaryNorm(bounds, cmap.N)
    return cmap, norm, classes, labels

def visualize_single_file(tiff_path, output_png_path):
    """可视化单个处理好的TIF文件。"""
    plt.rc('font', family='WenQuanYi Micro Hei')
    try:
        with rasterio.open(tiff_path) as src:
            bounds = src.bounds
            data = src.read(1)
            
            fig = plt.figure(figsize=(12, 12))
            ax = fig.add_subplot(1, 1, 1, projection=ccrs.PlateCarree())
            ax.set_extent([bounds.left, bounds.right, bounds.bottom, bounds.top], crs=ccrs.PlateCarree())

            cmap, norm, ticks, tick_labels = get_lulc_colormap_and_labels()

            im = ax.imshow(data, origin='upper', extent=[bounds.left, bounds.right, bounds.bottom, bounds.top], 
                           transform=ccrs.PlateCarree(), cmap=cmap, norm=norm)

            ax.add_feature(COASTLINE, linewidth=0.8)
            ax.add_feature(BORDERS, linestyle=':', linewidth=0.6)
            ax.gridlines(draw_labels=True, dms=True, x_inline=False, y_inline=False)
            
            region_name = os.path.basename(os.path.dirname(tiff_path)).replace("_", " ")
            file_type = os.path.splitext(os.path.basename(tiff_path))[0].upper()
            title = f"{region_name} - {file_type}"
            ax.set_title(title, fontsize=22)
            
            cbar = plt.colorbar(im, ax=ax, orientation='vertical', fraction=0.03, pad=0.04, ticks=ticks)
            cbar.ax.set_yticklabels(tick_labels)
            cbar.ax.tick_params(labelsize=10)

            plt.savefig(output_png_path, dpi=300, bbox_inches='tight')
            print(f"成功生成可视化地图: {output_png_path}")
            plt.close()

    except Exception as e:
        print(f"可视化文件 {tiff_path} 时失败: {e}")

if __name__ == "__main__":
    # 使用os.listdir和os.path.isdir来正确处理包含特殊字符的目录名
    base_dir = "data"
    processed_dirs = [os.path.join(base_dir, d) for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    
    print("开始为所有已处理的数据生成最终可视化地图...")
    for region_dir in processed_dirs:
        lulc_file = os.path.join(region_dir, "lulc.tif")
        if os.path.exists(lulc_file):
            output_path = os.path.join(region_dir, "lulc_visualization.png")
            visualize_single_file(lulc_file, output_path)
            
    print("\n所有可视化任务完成。") 