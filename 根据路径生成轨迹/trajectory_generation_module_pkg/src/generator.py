"""
轨迹生成器模块

本模块实现了一个基于规则和统计特性的轨迹生成器，可以：
1. 接收路径规划的离散航点序列
2. 基于真实数据分析的规则生成连续轨迹
3. 支持参数化调整以生成不同特性的轨迹

输入:
- waypoints: List[Tuple[float, float]] - 路径规划航点序列 (UTM坐标)
- initial_state: dict - 初始状态 (位置、速度、航向)
- goal_id: int - 目标点ID (0-3)
- env_maps: EnvironmentMaps - 环境地图对象
- generation_rules: dict - 生成规则参数
- control_params: dict - 控制参数
- sim_params: dict - 仿真参数

输出:
- trajectory_df: pd.DataFrame - 生成的轨迹数据(4Hz采样)
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any
from dataclasses import dataclass
import pyproj
import os
import glob

# 导入环境地图类
from .environment import EnvironmentMaps

@dataclass
class EnvironmentState:
    """环境状态数据类"""
    landcover: int  # 土地覆盖类型
    slope_magnitude: float  # 坡度大小
    slope_aspect: float  # 坡向(度)
    
@dataclass
class VehicleState:
    """车辆状态数据类"""
    position: np.ndarray  # [x, y] UTM坐标
    velocity: np.ndarray  # [vx, vy] 速度分量
    heading: float  # 航向角(度)
    acceleration: np.ndarray  # [ax, ay] 加速度分量
    timestamp: float  # 时间戳(秒)

class TrajectoryGenerator:
    """轨迹生成器类"""
    
    # 默认的地类基准速度(m/s)，基于分析结果估计的平均值
    DEFAULT_BASE_SPEEDS = {
        20: 6.2,  # 林地 - 根据分析结果调整为更高值
        40: 5.5,  # 灌木地 - 根据分析结果调整为更高值
        60: 4.8,  # 水体 - 根据分析结果调整为更高值
        90: 3.5   # 其他 - 提高默认值以匹配观察到的速度
    }
    
    # 默认的坡度影响系数，基于分析结果估计的平均值
    DEFAULT_SLOPE_COEFFICIENTS = {
        20: -0.032,  # 林地 - 坡度影响更显著
        40: -0.025,  # 灌木地 - 中等坡度影响
        60: -0.020,  # 水体 - 较弱坡度影响
        90: -0.028   # 其他 - 增加默认值
    }
    
    def __init__(self, env_maps=None, analysis_results_path=None):
        """
        初始化轨迹生成器
        
        参数:
            env_maps: EnvironmentMaps - 环境地图对象
            analysis_results_path: str - 分析结果路径，如果为None则使用默认路径
        """
        self.env_maps = env_maps
        self.utm_to_wgs84 = pyproj.Transformer.from_crs(
            "EPSG:32630", "EPSG:4326", always_xy=True
        )
        
        # 从分析结果加载更精确的模型
        # 为每条轨迹建立单独的模型
        self.slope_speed_models = {}
        self.trajectory_specific_models = {}  # 存储特定轨迹的模型
        self.residual_distributions = {}  # 存储残差分布
        self.load_analysis_results(analysis_results_path)
        
    def load_analysis_results(self, analysis_results_path=None):
        """加载分析结果，构建更精确的速度模型"""
        # 基础路径，可以根据实际情况调整
        # 如果提供了分析结果路径，则使用提供的路径
        if analysis_results_path:
            analysis_dir = analysis_results_path
        else:
            analysis_dir = '/home/<USER>/data/Sucess_or_Die/ai_agent_generation/analysisi_result'
        
        # 定义地类映射
        landcover_names = {
            20: "林地",
            40: "灌木地",
            60: "水体"
        }
        
        # 1. 为每种地类建立通用模型
        self._load_general_models(analysis_dir, landcover_names)
        
        # 2. 为每条轨迹建立特定模型
        self._load_trajectory_specific_models(analysis_dir, landcover_names)
        
        # 3. 加载残差分布
        self._load_residual_distributions(analysis_dir, landcover_names)
            
        # 如果没有加载到任何模型，使用默认值
        if not self.slope_speed_models and not self.trajectory_specific_models:
            print("警告: 未能加载任何分析结果，将使用默认的速度和坡度系数")
    
    def _load_general_models(self, analysis_dir, landcover_names):
        """加载通用速度-坡度模型（按地类）"""
        # 加载所有轨迹的坡度分箱统计
        for landcover_id, landcover_name in landcover_names.items():
            slope_bins = []
            slope_values = []
            speed_means = []
            speed_stds = []
            
            # 查找所有相关的CSV文件（使用1秒窗口的分析结果）
            pattern = os.path.join(analysis_dir, f"轨迹*_坡度分箱统计_1秒_{landcover_name}.csv")
            csv_files = glob.glob(pattern)
            
            for csv_file in csv_files:
                try:
                    print(f"正在处理: {csv_file}")
                    df = pd.read_csv(csv_file)
                    
                    # 合并数据
                    if not slope_bins:
                        slope_bins = df['slope_bin'].tolist()
                        # 从斜率范围的字符串中提取中间值
                        for bin_str in slope_bins:
                            # 处理字符串格式 "(-35, -30]"
                            try:
                                # 去除引号和括号
                                clean_str = bin_str.replace('"', '').replace('(', '').replace(']', '')
                                # 分割并获取两个值
                                parts = clean_str.split(',')
                                # 取中间值
                                if len(parts) == 2:
                                    min_val = float(parts[0].strip())
                                    max_val = float(parts[1].strip())
                                    slope_values.append((min_val + max_val) / 2)
                                else:
                                    slope_values.append(0)  # 默认值
                            except:
                                print(f"解析斜率范围时出错: {bin_str}")
                                slope_values.append(0)  # 默认值
                    
                    # 累加均值和标准差（后面会取平均）
                    if 'mean_speed' in df.columns:
                        if not speed_means:
                            speed_means = df['mean_speed'].fillna(0).tolist()
                            speed_stds = df['std_speed'].fillna(0).tolist()
                        else:
                            for i, speed in enumerate(df['mean_speed'].fillna(0)):
                                if i < len(speed_means):
                                    speed_means[i] += speed
                                    speed_stds[i] += df['std_speed'].fillna(0).iloc[i]
                except Exception as e:
                    print(f"警告: 无法加载 {csv_file}: {e}")
            
            # 计算平均值
            if csv_files and speed_means:
                n_files = len(csv_files)
                speed_means = [speed / n_files for speed in speed_means]
                speed_stds = [std / n_files for std in speed_stds]
                
                # 创建完整的模型（斜率、截距和方差）
                # 使用简单线性回归：speed = slope * slope_magnitude + intercept
                if len(slope_values) >= 2 and len(speed_means) >= 2:
                    # 确保数据长度一致
                    min_len = min(len(slope_values), len(speed_means))
                    x = np.array(slope_values[:min_len])
                    y = np.array(speed_means[:min_len])
                    
                    # 过滤掉NaN值
                    valid_indices = ~np.isnan(x) & ~np.isnan(y)
                    x_filtered = x[valid_indices]
                    y_filtered = y[valid_indices]
                    
                    if len(x_filtered) >= 2:  # 需要至少2个点才能进行回归
                        # 简单线性回归
                        slope, intercept = np.polyfit(x_filtered, y_filtered, 1)
                        
                        # 寻找斜率为0的基准速度
                        flat_speed_idx = np.argmin(np.abs(x_filtered))
                        base_speed = y_filtered[flat_speed_idx] if flat_speed_idx < len(y_filtered) else y_filtered.mean()
                        
                        # 存储模型
                        self.slope_speed_models[landcover_id] = {
                            'slope': slope,
                            'intercept': intercept,
                            'base_speed': base_speed,
                            'std': np.nanmean(speed_stds[:min_len])  # 使用平均标准差
                        }
                        
                        print(f"已加载{landcover_name}的速度-坡度模型: speed = {slope:.4f} * slope + {intercept:.2f}")
                    else:
                        print(f"警告: {landcover_name}的有效数据不足，使用默认模型")
                else:
                    print(f"警告: {landcover_name}的数据不足，使用默认模型")
    
    def _load_trajectory_specific_models(self, analysis_dir, landcover_names):
        """为每条轨迹加载特定的速度-坡度模型"""
        # 轨迹ID模式：轨迹1, 轨迹2, 轨迹3, 轨迹4
        trajectory_ids = [1, 2, 3, 4]
        
        for traj_id in trajectory_ids:
            # 为每个地类创建模型
            traj_models = {}
            
            for landcover_id, landcover_name in landcover_names.items():
                # 找到特定轨迹的分析结果
                csv_path = os.path.join(analysis_dir, f"轨迹{traj_id}_坡度分箱统计_1秒_{landcover_name}.csv")
                
                if os.path.exists(csv_path):
                    try:
                        df = pd.read_csv(csv_path)
                        
                        # 从斜率范围中提取中间值
                        slope_values = []
                        for bin_str in df['slope_bin'].tolist():
                            try:
                                # 去除引号和括号
                                clean_str = bin_str.replace('"', '').replace('(', '').replace(']', '')
                                # 分割并获取两个值
                                parts = clean_str.split(',')
                                # 取中间值
                                if len(parts) == 2:
                                    min_val = float(parts[0].strip())
                                    max_val = float(parts[1].strip())
                                    slope_values.append((min_val + max_val) / 2)
                                else:
                                    slope_values.append(0)  # 默认值
                            except:
                                print(f"解析斜率范围时出错: {bin_str}")
                                slope_values.append(0)  # 默认值
                        
                        # 检查数据完整性
                        if 'mean_speed' in df.columns and len(slope_values) >= 2:
                            x = np.array(slope_values)
                            y = df['mean_speed'].fillna(0).values
                            
                            # 过滤掉NaN值
                            valid_indices = ~np.isnan(x) & ~np.isnan(y)
                            x_filtered = x[valid_indices]
                            y_filtered = y[valid_indices]
                            
                            if len(x_filtered) >= 2:
                                # 线性回归
                                slope, intercept = np.polyfit(x_filtered, y_filtered, 1)
                                
                                # 标准差计算
                                std_values = df['std_speed'].fillna(0).values
                                std_filtered = std_values[valid_indices]
                                
                                # 存储模型
                                traj_models[landcover_id] = {
                                    'slope': slope,
                                    'intercept': intercept,
                                    'std': np.nanmean(std_filtered) if len(std_filtered)>0 else 0.0
                                }
                            else:
                                print(f"警告: 轨迹{traj_id}的{landcover_name}数据不足")
                    except Exception as e:
                        print(f"警告: 无法加载轨迹{traj_id}的{landcover_name}模型: {e}")
            
            if traj_models:
                self.trajectory_specific_models[traj_id] = traj_models
                print(f"已加载轨迹{traj_id}的特定速度模型")

    def _load_residual_distributions(self, analysis_dir, landcover_names):
        """加载残差分布模型 (GMM)
        TODO: 实现GMM模型的加载和采样，当前为占位符
        """
        # trajectory_ids = [1, 2, 3, 4]
        # for traj_id in trajectory_ids:
        #     self.residual_distributions[traj_id] = {}
        #     for landcover_id in landcover_names.keys():
        #         # 假设GMM模型保存在 .joblib 文件中
        #         model_path = os.path.join(analysis_dir, f'gmm_model_traj{traj_id}_lc{landcover_id}.joblib')
        #         if os.path.exists(model_path):
        #             try:
        #                 # self.residual_distributions[traj_id][landcover_id] = joblib.load(model_path)
        #                 pass # Placeholder
        #             except Exception as e:
        #                 print(f"无法加载GMM模型 {model_path}: {e}")
        #         else:
        #             # 使用默认正态分布
        #             # self.residual_distributions[traj_id][landcover_id] = {'mean': 0, 'std': 1.0} # Placeholder
        #             pass
        pass # 当前无实际加载

    def _sample_from_residual_distribution(self, traj_id, landcover_id):
        """从残差分布中采样
        TODO: 实现基于GMM的采样
        """
        # if traj_id in self.residual_distributions and landcover_id in self.residual_distributions[traj_id]:
        #     model = self.residual_distributions[traj_id][landcover_id]
        #     if isinstance(model, dict): # 默认正态分布
        #         return np.random.normal(model['mean'], model['std'])
        #     else: # GMM模型
        #         # return model.sample(1)[0][0]
        #         pass # Placeholder
        return np.random.normal(0, 0.5) # 默认一个小的随机扰动

    def generate(
        self,
        waypoints: List[Tuple[float, float]],
        initial_state: Dict,
        goal_id: int,
        env_maps: Any, # Expects EnvironmentMaps object
        generation_rules: Dict = None,
        control_params: Dict = None,
        sim_params: Dict = None
    ) -> pd.DataFrame:
        """生成轨迹
        
        Args:
            waypoints: 航点列表 [(x1,y1), (x2,y2), ...]
            initial_state: 初始状态
            goal_id: 目标点ID
            env_maps: 环境地图对象
            generation_rules: 生成规则参数(可选)
            control_params: 控制参数(可选)
            sim_params: 仿真参数(可选)
            
        Returns:
            pd.DataFrame: 生成的轨迹数据
        """
        print("\n开始生成轨迹...")
        
        # 检查轨迹点数量
        total_points = len(waypoints)
        print(f"输入航点数量: {total_points}")
        
        # 如果输入点过多，进行抽样减少计算量
        if total_points > 10000:
            print(f"输入点数过多，进行抽样...")
            sample_rate = max(1, total_points // 5000)  # 抽样比例，保证不超过5000个点
            waypoints = waypoints[::sample_rate]
            print(f"抽样后航点数量: {len(waypoints)}")
        
        # 初始化参数
        print(f"初始状态: {initial_state}")
        
        # 1. 使用默认参数
        if generation_rules is None:
            generation_rules = {
                'base_speeds': self.DEFAULT_BASE_SPEEDS,
                'slope_coefficients': self.DEFAULT_SLOPE_COEFFICIENTS,
                'trajectory_id': goal_id + 1  # 使用goal_id+1作为轨迹ID
            }
        else:
            # 确保有轨迹ID
            if 'trajectory_id' not in generation_rules:
                generation_rules['trajectory_id'] = goal_id + 1
            
        if control_params is None:
            # 对于使用所有原始点的情况，调整控制参数以获得更平滑的曲线
            control_params = {
                'global_speed_multiplier': 0.8,  # 降低全局速度，使用原始速度作为主导
                'max_speed': 8.0,
                'max_acceleration': 1.5,  # 降低加速度限制
                'max_deceleration': 2.0,  # 降低减速度限制
                'max_turn_rate': 25.0,     # 降低最大转向速率
                'turn_p_gain': 0.6,        # 降低转向响应以更平滑
                'waypoint_arrival_threshold': 5.0,  # 减小到达阈值
                'curvature_factor': 0.65,    # 增强曲率影响
                'use_original_speeds': True  # 使用原始速度作为参考
            }
            
        if sim_params is None:
            sim_params = {'dt_sim': 0.25}  # 4Hz
            
        # 检查是否需要控制轨迹点数量
        match_original_count = False
        target_points_count = None
        if sim_params and 'points_count' in sim_params:
            match_original_count = True
            target_points_count = sim_params.get('points_count', 0)
            print(f"目标轨迹点数量: {target_points_count}")
            
        # 检查是否有原始时间戳
        use_original_timestamps = False
        original_timestamps = None
        if sim_params and 'original_timestamps' in sim_params and sim_params['original_timestamps'] is not None:
            use_original_timestamps = True
            original_timestamps = sim_params['original_timestamps']
            print(f"使用原始时间戳: {len(original_timestamps)}个点")
            
            # 强制匹配原始点数
            if len(original_timestamps) > 0:
                match_original_count = True
                target_points_count = len(original_timestamps)
                print(f"将匹配原始轨迹点数: {target_points_count}点")
        
        # 提取原始速度信息，用于更好地匹配原始轨迹的速度模式
        original_speeds = None
        if 'original_speeds' in sim_params and sim_params['original_speeds'] is not None:
            original_speeds = sim_params['original_speeds']
            print(f"使用原始速度作为参考: {len(original_speeds)}个点")
        elif control_params.get('use_original_speeds', False) and sim_params and 'original_df' in sim_params:
            # 如果有原始数据帧，提取速度
            orig_df = sim_params.get('original_df')
            if orig_df is not None and 'velocity_north_ms' in orig_df.columns and 'velocity_east_ms' in orig_df.columns:
                original_speeds = np.sqrt(orig_df['velocity_north_ms']**2 + orig_df['velocity_east_ms']**2).values
                print(f"从原始数据中提取了速度: {len(original_speeds)}个点")
            
        # 2. 初始化状态
        current_state = self._init_vehicle_state(initial_state)
        trajectory_points = []  # 存储轨迹点
        dt = sim_params.get('dt_sim', 0.25)
        
        # 当使用所有原始点时，目标索引的步进会更小
        target_step = 1
        if len(waypoints) > 1000:
            target_step = max(1, len(waypoints) // 500)  # 动态调整步进，确保生成过程高效
        
        target_waypoint_idx = target_step  # 当前目标航点索引
        
        # 简化的曲率计算，只在关键点处计算
        path_curvatures = None
        if len(waypoints) < 5000:
            path_curvatures = self._calculate_path_curvatures(waypoints)
        else:
            print("轨迹点过多，跳过预计算曲率")
            path_curvatures = [0.0] * len(waypoints)
        
        # 记录初始状态
        # 如果有原始时间戳，使用第一个时间戳
        if use_original_timestamps and original_timestamps is not None and len(original_timestamps) > 0:
            current_state.timestamp = original_timestamps[0]
            
        self._record_state(trajectory_points, current_state, goal_id)
        
        # 4. 主循环
        # 使用更高效的算法跟踪轨迹，适合点数多的情况
        while target_waypoint_idx < len(waypoints):
            target_waypoint = np.array(waypoints[target_waypoint_idx])
            
            if len(trajectory_points) % 1000 == 0:  # 每1000步打印一次
                print(f"\n当前位置: ({current_state.position[0]:.2f}, {current_state.position[1]:.2f})")
                print(f"目标航点索引: {target_waypoint_idx}/{len(waypoints)}")
                print(f"目标航点: ({target_waypoint[0]:.2f}, {target_waypoint[1]:.2f})")
                if match_original_count:
                    progress = len(trajectory_points) / target_points_count * 100 if target_points_count > 0 else 0
                    print(f"轨迹点进度: {len(trajectory_points)}/{target_points_count} ({progress:.1f}%)")
                    
                    # 如果超出目标点数，提前结束
                    if len(trajectory_points) >= target_points_count:
                        print("已达到目标点数量，停止生成")
                        break
            
            # 计算到目标航点的向量和距离
            vec_to_target = target_waypoint - current_state.position
            dist_to_target = np.linalg.norm(vec_to_target)
            
            # 检查是否到达当前目标航点
            if dist_to_target < control_params.get('waypoint_arrival_threshold', 5.0):
                # 增加目标航点索引，使用步进以提高效率
                target_waypoint_idx += target_step
                
                # 如果接近终点，使用更小的步进以确保精确跟踪
                if target_waypoint_idx > len(waypoints) - 50:
                    target_step = 1
                
                if target_waypoint_idx < len(waypoints):
                    if len(trajectory_points) % 1000 == 0:
                        print(f"到达航点，前往下一个索引: {target_waypoint_idx}")
                continue
            
            # 查询当前环境状态
            env_state = self._get_environment_state(env_maps, current_state.position)
            
            # 获取当前参考速度（如果有）
            reference_speed = None
            if original_speeds is not None and len(trajectory_points) < len(original_speeds):
                reference_speed = original_speeds[len(trajectory_points)]
            
            # 计算目标速度 (考虑地形和坡度)
            base_target_speed = self._calculate_target_speed(
                env_state, current_state, generation_rules, control_params, reference_speed
            )
            
            # 应用曲率调整 (考虑转弯)
            curvature = self._get_current_curvature(target_waypoint_idx, path_curvatures)
            adjusted_target_speed = self._adjust_speed_for_curvature(
                base_target_speed, curvature, control_params
            )
            
            # 如果需要匹配原始轨迹长度，调整速度
            if match_original_count and target_points_count > 0:
                # 计算剩余路程和剩余点数
                remaining_points = target_points_count - len(trajectory_points)
                
                if remaining_points > 0:
                    # 估算剩余距离，使用更高效的方法
                    remaining_distance = dist_to_target
                    if len(waypoints) - target_waypoint_idx > 10:
                        # 采样计算距离，不必计算每个点
                        sample_step = max(1, (len(waypoints) - target_waypoint_idx) // 10)
                        for i in range(target_waypoint_idx + sample_step, len(waypoints), sample_step):
                            remaining_distance += np.linalg.norm(
                                np.array(waypoints[i]) - np.array(waypoints[i-sample_step])
                            )
                        
                        # 调整采样偏差
                        remaining_distance *= (len(waypoints) - target_waypoint_idx) / (len(waypoints) - target_waypoint_idx) * 10 / ((len(waypoints) - target_waypoint_idx) // sample_step)
                    
                    # 计算平均每点的距离
                    avg_distance_per_point = remaining_distance / remaining_points
                    
                    # 计算目标dt
                    target_dt = dt
                    if use_original_timestamps and original_timestamps is not None:
                        if len(trajectory_points) < len(original_timestamps):
                            next_timestamp = original_timestamps[len(trajectory_points)]
                            target_dt = next_timestamp - current_state.timestamp
                            target_dt = max(0.001, target_dt)  # 确保dt为正值
                    
                    # 计算调整后的速度
                    target_speed = avg_distance_per_point / target_dt
                    
                    # 使用平滑的调整倍数
                    max_adjustment = 2.0  # 最大调整倍数
                    min_adjustment = 0.5  # 最小调整倍数
                    
                    # 如果有参考速度，优先使用
                    if reference_speed is not None:
                        # 混合使用参考速度和计算速度
                        blend_factor = 0.7  # 参考速度的权重
                        target_speed = reference_speed * blend_factor + target_speed * (1 - blend_factor)
                    
                    speed_ratio = target_speed / adjusted_target_speed if adjusted_target_speed > 0 else 1.0
                    speed_ratio = max(min_adjustment, min(speed_ratio, max_adjustment))
                    
                    # 应用调整，使用平滑函数避免突变
                    adjusted_target_speed *= speed_ratio
            
            # 计算目标航向
            target_heading = np.degrees(np.arctan2(vec_to_target[1], vec_to_target[0])) #  Y-axis is North, X-axis is East
            turn_rate = self._calculate_turn_rate(
                current_state.heading, target_heading,
                control_params
            )
            
            # 设置时间步长
            current_dt = dt
            if use_original_timestamps and original_timestamps is not None:
                if len(trajectory_points) < len(original_timestamps):
                    # 使用原始轨迹的时间步长
                    next_timestamp = original_timestamps[len(trajectory_points)]
                    current_dt = next_timestamp - current_state.timestamp
                    # 确保dt为正值
                    current_dt = max(0.001, current_dt)
            
            # 更新状态
            new_state = self._update_state(current_state, adjusted_target_speed, turn_rate, current_dt)
            current_state = new_state
            
            # 记录状态
            self._record_state(trajectory_points, current_state, goal_id)
            
            # 检查是否需要提前结束（基于点数）
            if match_original_count and len(trajectory_points) >= target_points_count:
                print(f"已生成足够的轨迹点 ({len(trajectory_points)}/{target_points_count})，提前结束")
                break
        
        print(f"\n轨迹生成完成，总步数: {len(trajectory_points)}")
        
        # 5. 生成输出数据框
        generated_df = self._create_output_dataframe(trajectory_points)
        
        # 6. 如果需要，使用原始轨迹的时间戳
        if use_original_timestamps and original_timestamps is not None:
            if len(generated_df) != len(original_timestamps):
                print(f"警告: 生成的轨迹点数 ({len(generated_df)}) 与原始时间戳数量 ({len(original_timestamps)}) 不匹配")
                print("将通过重采样匹配时间戳...")
                
                # 重采样轨迹点以匹配原始时间戳数量
                generated_df = self._resample_trajectory(generated_df, len(original_timestamps))
            
            # 替换时间戳
            print("应用原始时间戳...")
            generated_df['timestamp_ms'] = original_timestamps * 1000  # 转换为毫秒
            
        return generated_df
    
    def _init_vehicle_state(self, initial_state: Dict[str, Any]) -> VehicleState:
        """初始化车辆状态"""
        # 从position字段获取位置，如果不存在则从x0/y0获取
        if 'position' in initial_state:
            position = np.array(initial_state['position'])
        else:
            position = np.array([initial_state['x0'], initial_state['y0']])
            
        # 从velocity字段获取速度，如果不存在则从vx0/vy0获取或使用默认值
        if 'velocity' in initial_state:
            velocity = np.array(initial_state['velocity'])
        else:
            velocity = np.array([
                initial_state.get('vx0', 0.0),  # East velocity
                initial_state.get('vy0', 0.0)   # North velocity
            ])
            
        return VehicleState(
            position=position,
            velocity=velocity,
            heading=initial_state.get('heading', initial_state.get('heading0', 0.0)),
            acceleration=np.array([0.0, 0.0]),
            timestamp=initial_state.get('time', initial_state.get('timestamp', 0.0))
        )

    def _get_environment_state(self, env_maps: EnvironmentMaps, position: np.ndarray) -> EnvironmentState:
        """获取给定位置的环境状态"""
        env_features = env_maps.query_by_xy(position[0], position[1])
        return EnvironmentState(
            landcover=env_features['landcover'],
            slope_magnitude=env_features['slope_magnitude'],
            slope_aspect=env_features['slope_aspect']
        )

    def _calculate_target_speed(
        self,
        env_state: EnvironmentState,
        state: VehicleState,
        generation_rules: Dict[str, Any],
        control_params: Dict[str, Any],
        reference_speed: float = None # Optional reference speed from original trajectory
    ) -> float:
        """计算目标速度
        
        核心逻辑：
        1. 使用特定轨迹的模型（如果存在）或通用模型确定基准速度和坡度影响。
        2. 应用全局速度乘数。
        3. 如果提供了参考速度 (use_original_speeds=True)，则混合参考速度和模型计算速度。
        4. 限制在最大/最小速度范围内。
        """
        traj_id = generation_rules.get('trajectory_id', 0) # 默认为0，代表通用模型
        landcover_id = env_state.landcover
        slope_magnitude = env_state.slope_magnitude
        
        # 尝试使用特定于轨迹的模型
        specific_model = None
        if traj_id in self.trajectory_specific_models and landcover_id in self.trajectory_specific_models[traj_id]:
            specific_model = self.trajectory_specific_models[traj_id][landcover_id]
        
        if specific_model:
            # 使用特定模型的斜率和截距
            base_speed_lc = specific_model['intercept'] + specific_model['slope'] * slope_magnitude
            speed_std_lc = specific_model.get('std', 0.5) # 使用模型标准差或默认值
        elif landcover_id in self.slope_speed_models:
            # 使用通用模型的斜率和截距
            model = self.slope_speed_models[landcover_id]
            base_speed_lc = model['intercept'] + model['slope'] * slope_magnitude
            speed_std_lc = model.get('std', 0.5)
        else:
            # 使用默认规则 (如果模型缺失)
            base_speeds = generation_rules.get('base_speeds', self.DEFAULT_BASE_SPEEDS)
            slope_coeffs = generation_rules.get('slope_coefficients', self.DEFAULT_SLOPE_COEFFICIENTS)
            
            base_speed_lc = base_speeds.get(landcover_id, base_speeds.get(90)) # 默认使用类型90
            slope_coeff = slope_coeffs.get(landcover_id, slope_coeffs.get(90))
            base_speed_lc += slope_magnitude * slope_coeff
            speed_std_lc = 0.5 # 默认标准差

        # 应用全局速度乘数
        target_speed = base_speed_lc * control_params.get('global_speed_multiplier', 1.0)

        # 添加基于残差分布的随机扰动 (更真实的模拟)
        # residual_sample = self._sample_from_residual_distribution(traj_id, landcover_id)
        # target_speed += residual_sample
        
        # 如果使用原始轨迹速度作为参考
        if control_params.get('use_original_speeds', False) and reference_speed is not None:
            # 混合模型计算速度和参考速度
            # 权重可以调整，例如更相信原始数据
            blend_factor = control_params.get('original_speed_blend_factor', 0.7) 
            target_speed = (reference_speed * blend_factor) + (target_speed * (1 - blend_factor))

        # 限制速度在最大和最小范围内
        min_speed = control_params.get('min_speed', 1.0)  # 最小速度 m/s
        max_speed = control_params.get('max_speed', 10.0) # 最大速度 m/s
        target_speed = np.clip(target_speed, min_speed, max_speed)
        
        return target_speed

    def _calculate_turn_rate(
        self,
        current_heading: float,
        target_heading: float,
        control_params: Dict[str, Any]
    ) -> float:
        """计算转向速率 (度/秒)
        使用比例控制，限制最大转向速率。
        """
        heading_error = target_heading - current_heading
        # 规范化角度差到 (-180, 180]
        if heading_error > 180:
            heading_error -= 360
        elif heading_error <= -180:
            heading_error += 360
            
        turn_p_gain = control_params.get('turn_p_gain', 0.5)
        turn_rate = heading_error * turn_p_gain
        
        max_turn_rate = control_params.get('max_turn_rate', 30.0) # 度/秒
        turn_rate = np.clip(turn_rate, -max_turn_rate, max_turn_rate)
        return turn_rate

    def _update_state(
        self,
        state: VehicleState,
        target_speed: float,
        turn_rate: float,
        dt: float
    ) -> VehicleState:
        """更新车辆状态
        
        Args:
            state: 当前状态
            target_speed: 目标速度 (m/s)
            turn_rate: 转向速率 (度/秒)
            dt: 时间步长 (秒)
            
        Returns:
            VehicleState: 更新后的状态
        """
        # 当前速度大小
        current_speed_magnitude = np.linalg.norm(state.velocity)
        
        # 计算目标加速度 (纵向)
        # 使用比例控制来平滑速度变化
        # TODO: 从 control_params 获取 speed_p_gain
        speed_error = target_speed - current_speed_magnitude
        target_acceleration_magnitude = speed_error * 0.8 # 比例增益
        
        # 限制最大加/减速度
        # TODO: 从 control_params 获取 max_acceleration/deceleration
        max_accel = 1.5  # m/s^2
        max_decel = -2.0 # m/s^2 (负值)
        target_acceleration_magnitude = np.clip(target_acceleration_magnitude, max_decel, max_accel)
        
        # 更新航向
        new_heading = state.heading + turn_rate * dt
        # 规范化航向到 [0, 360)
        new_heading = new_heading % 360
        
        # 更新速度大小 (应用加速度)
        new_speed_magnitude = current_speed_magnitude + target_acceleration_magnitude * dt
        new_speed_magnitude = max(0, new_speed_magnitude) # 速度不能为负
        
        # 计算新的速度分量 (vx, vy)
        # 注意：航向角通常是从正北方向顺时针为正，数学角度是从X轴逆时针为正
        # 这里假设航向角是从X轴（东向）逆时针为正，符合np.arctan2的输出
        # 如果航向角定义不同，需要相应调整 sin/cos
        # 假设 self.heading 是地理航向（0度为北，90度为东）
        # 需要转换为数学角度 (0度为东，90度为北)
        math_heading_rad = np.radians(90.0 - new_heading) 
        
        new_vx = new_speed_magnitude * np.cos(math_heading_rad) # 东向速度
        new_vy = new_speed_magnitude * np.sin(math_heading_rad) # 北向速度
        new_velocity = np.array([new_vx, new_vy])
        
        # 计算实际加速度分量
        new_acceleration = (new_velocity - state.velocity) / dt
        
        # 更新位置
        # 使用平均速度进行积分，提高精度: pos_new = pos_old + (vel_old + vel_new)/2 * dt
        new_position = state.position + (state.velocity + new_velocity) / 2.0 * dt
        
        return VehicleState(
            position=new_position,
            velocity=new_velocity,
            heading=new_heading,
            acceleration=new_acceleration,
            timestamp=state.timestamp + dt
        )

    def _record_state(
        self,
        points: List[Tuple],
        state: VehicleState,
        goal_id: int
    ) -> None:
        """记录当前状态
        
        Args:
            points: 轨迹点列表
            state: 当前状态
            goal_id: 目标点ID
        """
        points.append((
            int(state.timestamp * 1000),  # timestamp_ms
            state.position[0],  # x
            state.position[1],  # y
            state.velocity[1],  # velocity_north_ms (y方向)
            state.velocity[0],  # velocity_east_ms (x方向)
            state.heading,  # heading_deg
            state.acceleration[1],  # acceleration_north_ms2 (y方向)
            state.acceleration[0],  # acceleration_east_ms2 (x方向)
            goal_id  # goal_id
        ))
    
    def _create_output_dataframe(self, points: List[Tuple]) -> pd.DataFrame:
        """创建输出数据框
        
        Args:
            points: 轨迹点列表
            
        Returns:
            pd.DataFrame: 轨迹数据框
        """
        df = pd.DataFrame(points, columns=[
            'timestamp_ms',
            'x',
            'y',
            'velocity_north_ms',
            'velocity_east_ms',
            'heading_deg',
            'acceleration_north_ms2',
            'acceleration_east_ms2',
            'goal_id'
        ])
        
        # 添加UTM坐标列
        df['utm_x'] = df['x']
        df['utm_y'] = df['y']
        
        return df

    def _calculate_path_curvatures(self, waypoints: List[Tuple[float, float]]) -> List[float]:
        """计算路径上每个点的曲率 (1/R)
        使用 Menger 曲率公式：K = 2 * |x1(y2 - y3) + x2(y3 - y1) + x3(y1 - y2)| / ((d12*d23*d31))
        其中 d12 是点1和点2之间的距离。
        为了稳定性，在直线段或接近直线段的地方，曲率近似为0。
        这里采用简化的方法，计算相邻三个点形成的圆的曲率。
        """
        curvatures = [0.0] * len(waypoints)
        if len(waypoints) < 3:
            return curvatures

        points_arr = np.array(waypoints)
        
        for i in range(1, len(waypoints) - 1):
            p1 = points_arr[i-1]
            p2 = points_arr[i]
            p3 = points_arr[i+1]
            
            # 计算边长
            d12 = np.linalg.norm(p2 - p1)
            d23 = np.linalg.norm(p3 - p2)
            d31 = np.linalg.norm(p1 - p3)
            
            # 检查共线或点重合的情况
            # 使用三角形面积的两倍来判断： |x1(y2 - y3) + x2(y3 - y1) + x3(y1 - y2)|
            # 如果面积接近0，则点共线，曲率为0
            area_x2 = p1[0]*(p2[1] - p3[1]) + p2[0]*(p3[1] - p1[1]) + p3[0]*(p1[1] - p2[1])
            
            # 避免除以零
            denominator = d12 * d23 * d31
            if denominator < 1e-6 or abs(area_x2) < 1e-6: # 阈值可以调整
                curvatures[i] = 0.0
            else:
                curvatures[i] = abs(area_x2) / denominator # Menger曲率公式简化版，只取正值
                # 注意：原始Menger公式是 2 * area / (d12*d23*d31)，这里area_x2已经是2倍面积
        
        # 对首尾点，使用相邻点的曲率
        if len(waypoints) >= 3:
            curvatures[0] = curvatures[1]
            curvatures[-1] = curvatures[-2]
            
        # 可以选择对曲率进行平滑处理
        # curvatures = gaussian_filter1d(curvatures, sigma=1)
        return curvatures

    def _get_current_curvature(self, waypoint_idx: int, curvatures: List[float]) -> float:
        """获取当前或预估的路径曲率"""
        if not curvatures or waypoint_idx >= len(curvatures):
            return 0.0
        return curvatures[waypoint_idx]

    def _adjust_speed_for_curvature(
        self, 
        base_speed: float, 
        curvature: float,
        control_params: Dict[str, Any]
    ) -> float:
        """根据路径曲率调整速度
        
        Args:
            base_speed: 基础目标速度 (m/s)
            curvature: 当前路径曲率 (1/R)
            control_params: 控制参数字典
            
        Returns:
            float: 调整后的速度 (m/s)
        """
        # 曲率越大，速度应该越慢
        # 可以使用类似 V = sqrt(a_lat_max * R) 的关系
        # a_lat_max: 最大允许侧向加速度 (e.g., 0.3g to 0.5g for comfort)
        # R = 1 / curvature
        
        max_lateral_accel = control_params.get('max_lateral_acceleration', 2.0) # m/s^2
        curvature_factor = control_params.get('curvature_factor', 0.5) # 曲率影响因子
        
        if curvature > 1e-5: # 避免除以零或极小曲率
            radius = 1.0 / curvature
            # V_crit = sqrt(a_lat_max * R)
            # 使用一个更柔和的调整，而不是硬限制
            # 例如，速度调整系数 = 1 / (1 + curvature_factor * curvature)
            # 或者，使用一个与半径相关的乘数，半径越大，允许速度越大
            # speed_multiplier = np.clip(radius / control_params.get('curvature_ref_radius', 50.0), 0.3, 1.0)
            
            # 简化模型：速度与曲率的倒数成正比，通过一个因子调整影响程度
            # 当曲率大（转弯急）时，乘数减小
            # 当曲率小（直线）时，乘数接近1
            # speed_reduction_factor = 1.0 / (1.0 + curvature_factor * curvature * 100) # 乘以100放大曲率影响

            # 尝试基于临界速度的模型，但进行平滑调整
            # V_crit = sqrt(a_lat_max * R)
            v_critical = np.sqrt(max_lateral_accel / (curvature + 1e-6)) # 加一个小数防止除零
            
            # 将基础速度与临界转弯速度结合，取较小者，并考虑一个调整因子
            # adjusted_speed = min(base_speed, v_critical * curvature_factor)
            # 平滑过渡: 如果 v_critical 很小，则速度主要由此决定
            if v_critical < base_speed:
                 # 使用 curvature_factor 来混合，当 v_critical 远小于 base_speed 时，更倾向于 v_critical
                blend = np.exp(-curvature_factor * (base_speed - v_critical) / (v_critical + 1e-6) )
                adjusted_speed = base_speed * blend + v_critical * (1-blend)
            else:
                adjusted_speed = base_speed
        else:
            adjusted_speed = base_speed
            
        return max(control_params.get('min_speed', 1.0), adjusted_speed) # 确保不低于最小速度

    def _resample_trajectory(self, df: pd.DataFrame, target_length: int) -> pd.DataFrame:
        """等距离重采样轨迹"""
        # 计算累计距离
        cumulative_distance = np.zeros(len(df))
        for i in range(1, len(df)):
            dx = df.iloc[i]['x'] - df.iloc[i-1]['x']
            dy = df.iloc[i]['y'] - df.iloc[i-1]['y']
            distance = np.sqrt(dx**2 + dy**2)
            cumulative_distance[i] = cumulative_distance[i-1] + distance
        
        # 创建要重采样的距离点
        total_distance = cumulative_distance[-1]
        if total_distance == 0:  # 防止除以零
            return df
        
        sample_distances = np.linspace(0, total_distance, target_length)
        
        # 为每列创建插值函数
        from scipy.interpolate import interp1d
        resampled_data = {}
        
        for column in df.columns:
            if column in ['x', 'y', 'vx', 'vy', 'speed', 'heading', 'time']:
                # 为数值列执行插值
                interpolator = interp1d(cumulative_distance, df[column], kind='linear')
                resampled_data[column] = interpolator(sample_distances)
        
        # 创建重采样后的DataFrame
        resampled_df = pd.DataFrame(resampled_data)
        return resampled_df
        
    def generate_trajectory(self, waypoints, initial_state, generation_params=None, simulation_params=None):
        """
        生成轨迹的主要接口方法
        
        参数:
            waypoints: numpy.ndarray - 路径点坐标 [[x1, y1], [x2, y2], ...]
            initial_state: dict - 初始状态，包含position, velocity, heading, time
            generation_params: dict - 生成参数
            simulation_params: dict - 仿真参数
            
        返回:
            list - 轨迹点列表，每个点为字典
        """
        if generation_params is None:
            generation_params = {
                'dt': 1.0,  # 时间步长
                'max_speed': 10.0,  # 最大速度m/s
                'min_speed': 0.1,   # 最小速度m/s
                'max_accel': 0.5,   # 最大加速度m/s²
                'max_decel': 0.5,   # 最大减速度m/s²
            }
            
        if simulation_params is None:
            simulation_params = {
                'dt': 1.0,  # 仿真时间步长
                'path_tracking_gain': 0.5,  # 路径跟踪增益
                'lookahead_distance': 5.0,  # 前视距离
                'target_speed': 5.0,  # 目标速度m/s
            }
            
        # 将参数转换为内部使用的格式
        generation_rules = {
            'dt': generation_params.get('dt', 1.0),
            'max_speed': generation_params.get('max_speed', 10.0),
            'min_speed': generation_params.get('min_speed', 0.1),
        }
        
        control_params = {
            'max_acceleration': generation_params.get('max_accel', 0.5),
            'max_deceleration': generation_params.get('max_decel', 0.5),
            'path_tracking_gain': simulation_params.get('path_tracking_gain', 0.5),
            'lookahead_distance': simulation_params.get('lookahead_distance', 5.0),
            'target_speed': simulation_params.get('target_speed', 5.0),
        }
        
        sim_params = {
            'dt': simulation_params.get('dt', 1.0),
        }
        
        # 确保waypoints是numpy数组
        if not isinstance(waypoints, np.ndarray):
            waypoints = np.array(waypoints)
            
        # 调用内部generate方法
        trajectory_df = self.generate(
            waypoints=waypoints.tolist(),
            initial_state=initial_state,
            goal_id=0,  # 默认使用第一个目标
            env_maps=self.env_maps,
            generation_rules=generation_rules,
            control_params=control_params,
            sim_params=sim_params
        )
        
        # 转换DataFrame为轨迹点列表
        trajectory = []
        for i in range(len(trajectory_df)):
            point = {
                'time': trajectory_df.iloc[i]['time'],
                'position': np.array([trajectory_df.iloc[i]['x'], trajectory_df.iloc[i]['y']]),
                'velocity': np.array([trajectory_df.iloc[i]['vx'], trajectory_df.iloc[i]['vy']]),
                'speed': trajectory_df.iloc[i]['speed'],
                'heading': trajectory_df.iloc[i]['heading'],
                'angular_velocity': trajectory_df.iloc[i]['angular_velocity'] if 'angular_velocity' in trajectory_df.columns else 0.0
            }
            trajectory.append(point)
            
        return trajectory 