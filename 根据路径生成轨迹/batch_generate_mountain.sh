#!/bin/bash

# 批量生成山地特殊路径的轨迹数据脚本

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate wargame

# 设置参数
PATH_DIR="/home/<USER>/data/Sucess_or_Die/ai_agent_generation/gen_exp/mountain_special"
ENV_DIR="/home/<USER>/data/Sucess_or_Die/ai_agent_generation/trajectory_generator/data/environment"
OUTPUT_DIR="/home/<USER>/data/Sucess_or_Die/ai_agent_generation/generated_trajectories_mountain_special"
BATCH_SIZE=10  # 每批处理的文件数
TOTAL_FILES=-1  # 修改这里，-1表示处理所有文件
TARGET_SPEED_KMH=50.0 # 设置山地特殊路径的目标速度

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 开始生成
echo "开始批量生成山地特殊路径的轨迹数据..."
echo "目标平均速度: ${TARGET_SPEED_KMH} km/h"
echo "路径数据目录: $PATH_DIR"
echo "环境数据目录: $ENV_DIR"
echo "输出目录: $OUTPUT_DIR"

# 计算批次数
if [ $TOTAL_FILES -eq -1 ]; then
    TOTAL_FILES=$(ls $PATH_DIR/*_mountain_special.npy | wc -l)
fi

NUM_BATCHES=$(( ($TOTAL_FILES + $BATCH_SIZE - 1) / $BATCH_SIZE ))
echo "总共处理 $TOTAL_FILES 个文件，分 $NUM_BATCHES 批进行"

# 批量处理
for ((i=0; i<$NUM_BATCHES; i++)); do
    start_idx=$(( $i * $BATCH_SIZE ))
    remaining=$(( $TOTAL_FILES - $start_idx ))
    
    if [ $remaining -lt $BATCH_SIZE ]; then
        batch_size=$remaining
    else
        batch_size=$BATCH_SIZE
    fi
    
    echo "----------------------------------------------"
    echo "处理第 $(($i+1))/$NUM_BATCHES 批，文件 $start_idx-$(($start_idx+$batch_size-1))/$TOTAL_FILES"
    echo "----------------------------------------------"
    
    python generate_trajectories.py \
        --path_dir $PATH_DIR \
        --env_dir $ENV_DIR \
        --output_dir $OUTPUT_DIR \
        --num_files $batch_size \
        --skip_files $start_idx \
        --target_speed_kmh $TARGET_SPEED_KMH \
        --path_suffix "mountain_special"
    
    echo "第 $(($i+1))/$NUM_BATCHES 批处理完成"
done

echo "所有山地特殊路径的轨迹数据生成完成！"
echo "结果已保存到: $OUTPUT_DIR" 