#!/usr/bin/env python3
"""
Google Earth Engine数据下载脚本
下载DEM、土地利用覆盖(LULC)和卫星图像数据
"""

import ee
import os
import json
from datetime import datetime, timedelta
import requests
import zipfile
import time

# 服务账户密钥文件路径
SERVICE_ACCOUNT_KEY_PATH = 'just-landing-468209-u0-d7f8ef6de834.json'

def initialize_earth_engine():
    """初始化Google Earth Engine"""
    try:
        # 读取服务账户信息
        with open(SERVICE_ACCOUNT_KEY_PATH, 'r') as f:
            key_data = json.load(f)
        
        service_account_email = key_data['client_email']
        
        # 使用服务账户初始化
        credentials = ee.ServiceAccountCredentials(service_account_email, SERVICE_ACCOUNT_KEY_PATH)
        ee.Initialize(credentials)
        print("✓ Google Earth Engine 初始化成功")
        return True
    except Exception as e:
        print(f"✗ Earth Engine 初始化失败: {e}")
        return False

def download_dem_data(region_name, bbox, output_dir):
    """下载DEM数据"""
    print(f"\n📊 下载 {region_name} 的DEM数据...")
    
    # 创建输出目录
    dem_dir = os.path.join(output_dir, region_name, 'DEM')
    os.makedirs(dem_dir, exist_ok=True)
    
    # 定义区域
    region = ee.Geometry.BBox(bbox[0], bbox[1], bbox[2], bbox[3])
    
    try:
        # SRTM 30m DEM数据
        srtm = ee.Image('USGS/SRTMGL1_003').clip(region)
        
        # 导出任务
        task = ee.batch.Export.image.toDrive(
            image=srtm,
            description=f'DEM_{region_name}',
            folder='GEE_Downloads',
            fileNamePrefix=f'DEM_{region_name}',
            scale=30,
            region=region,
            maxPixels=1e9
        )
        
        task.start()
        print(f"✓ DEM导出任务已启动: {task.id}")
        return task
        
    except Exception as e:
        print(f"✗ DEM数据下载失败: {e}")
        return None

def download_lulc_data(region_name, bbox, output_dir):
    """下载土地利用覆盖数据"""
    print(f"\n🌍 下载 {region_name} 的LULC数据...")
    
    # 创建输出目录
    lulc_dir = os.path.join(output_dir, region_name, 'LULC')
    os.makedirs(lulc_dir, exist_ok=True)
    
    # 定义区域
    region = ee.Geometry.BBox(bbox[0], bbox[1], bbox[2], bbox[3])
    
    try:
        # ESA WorldCover 2021 (10m分辨率)
        worldcover = ee.ImageCollection("ESA/WorldCover/v200").first().clip(region)
        
        # 导出任务
        task = ee.batch.Export.image.toDrive(
            image=worldcover,
            description=f'LULC_{region_name}',
            folder='GEE_Downloads',
            fileNamePrefix=f'LULC_{region_name}',
            scale=10,
            region=region,
            maxPixels=1e9
        )
        
        task.start()
        print(f"✓ LULC导出任务已启动: {task.id}")
        return task
        
    except Exception as e:
        print(f"✗ LULC数据下载失败: {e}")
        return None

def download_satellite_imagery(region_name, bbox, output_dir, start_date='2023-01-01', end_date='2023-12-31'):
    """下载卫星图像数据"""
    print(f"\n🛰️ 下载 {region_name} 的卫星图像数据...")
    
    # 创建输出目录
    satellite_dir = os.path.join(output_dir, region_name, 'Satellite')
    os.makedirs(satellite_dir, exist_ok=True)
    
    # 定义区域
    region = ee.Geometry.BBox(bbox[0], bbox[1], bbox[2], bbox[3])
    
    try:
        # Landsat 8-9 数据
        landsat = (ee.ImageCollection('LANDSAT/LC08/C02/T1_L2')
                  .merge(ee.ImageCollection('LANDSAT/LC09/C02/T1_L2'))
                  .filterDate(start_date, end_date)
                  .filterBounds(region)
                  .filter(ee.Filter.lt('CLOUD_COVER', 20))
                  .median()
                  .clip(region))
        
        # 选择可见光和近红外波段
        landsat_rgb = landsat.select(['SR_B4', 'SR_B3', 'SR_B2']).multiply(0.0000275).add(-0.2)
        
        # 导出任务
        task = ee.batch.Export.image.toDrive(
            image=landsat_rgb,
            description=f'Satellite_{region_name}',
            folder='GEE_Downloads',
            fileNamePrefix=f'Satellite_{region_name}',
            scale=30,
            region=region,
            maxPixels=1e9
        )
        
        task.start()
        print(f"✓ 卫星图像导出任务已启动: {task.id}")
        return task
        
    except Exception as e:
        print(f"✗ 卫星图像下载失败: {e}")
        return None

def download_sentinel2_imagery(region_name, bbox, output_dir, start_date='2023-01-01', end_date='2023-12-31'):
    """下载Sentinel-2高分辨率卫星图像"""
    print(f"\n🛰️ 下载 {region_name} 的Sentinel-2图像数据...")
    
    # 创建输出目录
    sentinel_dir = os.path.join(output_dir, region_name, 'Sentinel2')
    os.makedirs(sentinel_dir, exist_ok=True)
    
    # 定义区域
    region = ee.Geometry.BBox(bbox[0], bbox[1], bbox[2], bbox[3])
    
    try:
        # Sentinel-2 数据
        sentinel2 = (ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')
                    .filterDate(start_date, end_date)
                    .filterBounds(region)
                    .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20))
                    .median()
                    .clip(region))
        
        # 选择RGB波段
        sentinel2_rgb = sentinel2.select(['B4', 'B3', 'B2']).multiply(0.0001)
        
        # 导出任务
        task = ee.batch.Export.image.toDrive(
            image=sentinel2_rgb,
            description=f'Sentinel2_{region_name}',
            folder='GEE_Downloads',
            fileNamePrefix=f'Sentinel2_{region_name}',
            scale=10,
            region=region,
            maxPixels=1e9
        )
        
        task.start()
        print(f"✓ Sentinel-2导出任务已启动: {task.id}")
        return task
        
    except Exception as e:
        print(f"✗ Sentinel-2图像下载失败: {e}")
        return None

def monitor_tasks(tasks):
    """监控导出任务状态"""
    print(f"\n⏳ 监控 {len(tasks)} 个导出任务...")
    
    while True:
        completed = 0
        failed = 0
        
        for task in tasks:
            if task is None:
                continue
                
            status = task.status()
            state = status['state']
            
            if state == 'COMPLETED':
                completed += 1
            elif state == 'FAILED':
                failed += 1
                print(f"✗ 任务失败: {status.get('description', 'Unknown')}")
                if 'error_message' in status:
                    print(f"  错误信息: {status['error_message']}")
        
        print(f"进度: {completed}/{len([t for t in tasks if t is not None])} 完成, {failed} 失败")
        
        if completed + failed >= len([t for t in tasks if t is not None]):
            break
            
        time.sleep(30)  # 等待30秒后再次检查
    
    print(f"✓ 所有任务完成! {completed} 成功, {failed} 失败")

def main():
    """主函数"""
    print("🚀 开始下载地理空间数据...")
    
    # 初始化Earth Engine
    if not initialize_earth_engine():
        return
    
    # 定义输出目录
    output_dir = 'downloaded_data'
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义感兴趣的区域
    regions = {
        "海湾战争区域": [47.0, 28.5, 48.5, 30.5],  # 科威特
        "顿巴斯地区": [37.0, 47.5, 39.0, 49.0],    # 乌克兰东部
        "苏格兰高地": [-5.0, 56.0, -3.0, 58.0],    # 苏格兰
        "克什米尔地区": [74.0, 33.0, 76.0, 35.0],   # 克什米尔
        "以色列-巴勒斯坦": [34.0, 31.0, 36.0, 33.0] # 以色列-巴勒斯坦
    }
    
    all_tasks = []
    
    # 为每个区域下载数据
    for region_name, bbox in regions.items():
        print(f"\n{'='*50}")
        print(f"处理区域: {region_name}")
        print(f"边界框: {bbox}")
        
        # 下载DEM数据
        dem_task = download_dem_data(region_name, bbox, output_dir)
        if dem_task:
            all_tasks.append(dem_task)
        
        # 下载LULC数据
        lulc_task = download_lulc_data(region_name, bbox, output_dir)
        if lulc_task:
            all_tasks.append(lulc_task)
        
        # 下载Landsat卫星图像
        satellite_task = download_satellite_imagery(region_name, bbox, output_dir)
        if satellite_task:
            all_tasks.append(satellite_task)
        
        # 下载Sentinel-2图像
        sentinel_task = download_sentinel2_imagery(region_name, bbox, output_dir)
        if sentinel_task:
            all_tasks.append(sentinel_task)
    
    # 监控所有任务
    if all_tasks:
        monitor_tasks(all_tasks)
        print(f"\n✅ 数据下载完成!")
        print(f"请检查您的Google Drive中的'GEE_Downloads'文件夹")
    else:
        print("❌ 没有成功启动任何下载任务")

if __name__ == '__main__':
    main()
