import os
import math
import shutil
import glob
import subprocess

# --- 全局定义 ---
REGIONS = {
    "苏格兰高地": [-6, 56, -3, 58],
    "以色列-巴勒斯坦地区": [34, 31, 36, 33],
    "克什米尔地区": [74, 33, 78, 35],
    "海湾战争区域(科威特)": [46, 28, 49, 31],
    "顿巴斯地区": [37, 47, 40, 49]
}

BASE_OUTPUT_DIR = "data"
TEMP_DEM_DIR = os.path.join(BASE_OUTPUT_DIR, "temp_dem_zip")
LULC_RAW_DIR = os.path.join(BASE_OUTPUT_DIR, "LULC_raw")
ARCHIVE_DIR = "垃圾中转"
DEM_BASE_URL = "http://viewfinderpanoramas.org/dem3/"

def calculate_all_required_zips(region_bbox_map):
    """计算所有定义区域所需的DEM zip文件。"""
    required = set()
    for bbox in region_bbox_map.values():
        min_lat, max_lat = math.floor(bbox[1]), math.ceil(bbox[3])
        for lat in range(min_lat, max_lat + 1): # +1确保覆盖上边界
            if lat >= 0:
                required.add(f"N{lat}.zip")
    return sorted(list(required))

def main():
    print("--- 1. 创建项目目录结构 ---")
    os.makedirs(TEMP_DEM_DIR, exist_ok=True)
    os.makedirs(LULC_RAW_DIR, exist_ok=True)
    os.makedirs(ARCHIVE_DIR, exist_ok=True)
    for name in REGIONS.keys():
        os.makedirs(os.path.join(BASE_OUTPUT_DIR, name.replace(" ", "_")), exist_ok=True)
        
    # 将原始LULC数据移动到规范目录以便后续处理
    print("正在整理LULC源文件...")
    for lulc_dir in glob.glob("./N*_2010LC030"):
        if os.path.isdir(lulc_dir) and not os.path.exists(os.path.join(LULC_RAW_DIR, os.path.basename(lulc_dir))):
             shutil.move(lulc_dir, LULC_RAW_DIR)

    print("\n--- 2. 计算所有区域所需的DEM文件 ---")
    all_zips_needed = calculate_all_required_zips(REGIONS)
    print(f"总共需要 {len(all_zips_needed)} 个DEM文件。")

    print("\n--- 3. 生成并启动后台下载脚本 ---")
    script_content = f"""#!/bin/bash
DEM_DIR="{TEMP_DEM_DIR}"
BASE_URL="{DEM_BASE_URL}"
ALL_ZIPS=({' '.join(f'"{z}"' for z in all_zips_needed)})

echo "--- 后台下载器已启动于 $(date) ---"
echo "将检查和下载 {len(all_zips_needed)} 个文件..."

while true; do
    all_done=true
    for zip_name in "${{ALL_ZIPS[@]}}"; do
        dest_path="$DEM_DIR/$zip_name"
        if [ ! -f "$dest_path" ] || [ $(stat -c%s "$dest_path") -lt 1024 ]; then
            all_done=false
            echo "BG DOWNLOAD [$(date)] -- 正在下载: $zip_name"
            wget -q -c -T 120 -O "$dest_path.tmp" "$BASE_URL$zip_name"
            if [ $? -eq 0 ]; then
                mv "$dest_path.tmp" "$dest_path"
                echo "BG DOWNLOAD [$(date)] -- 成功: $zip_name"
            else
                rm -f "$dest_path.tmp"
                echo "BG DOWNLOAD [$(date)] -- 失败，将在下一轮重试: $zip_name"
            fi
            sleep 5 # 每次下载间歇
        fi
    done

    if [ "$all_done" = true ]; then
        echo "--- 所有DEM文件下载完成于 $(date) ---"
        break
    fi

    echo "本轮检查完毕, 5分钟后开始下一轮..."
    sleep 300
done
"""
    script_path = "background_dem_downloader.sh"
    with open(script_path, "w") as f:
        f.write(script_content)

    # 启动后台进程
    subprocess.Popen(f"chmod +x {script_path} && nohup ./{script_path} > background_downloader.log 2>&1 &", shell=True)
    
    print(f"\n成功！已在后台启动下载任务。")
    print("您现在可以安全地关闭终端或继续执行其他任务。")
    print("下载进度和日志会记录在 `background_downloader.log` 文件中。")
    print("您可以使用 `tail -f background_downloader.log` 命令来实时监控。")
    
    # 清理掉之前失败的脚本
    if os.path.exists("run_all_tasks.py"):
        os.remove("run_all_tasks.py")


if __name__ == "__main__":
    main() 