#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
粗略路径规划模块 (High-Level Path Planning)
实现分层A*算法，生成从起点到终点的几何路径

核心功能：
1. 环境代价模型
2. 分层A*算法
3. 多场景权重配置
"""

import numpy as np
import heapq
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
from enum import Enum
import math

class ScenarioType(Enum):
    """场景类型"""
    STANDARD = "standard"           # 标准作战
    HIGH_MOBILITY = "high_mobility"  # 高机动性
    STEALTH = "stealth_priority"    # 隐蔽优先
    MOUNTAIN = "mountain_special"   # 山地特种

@dataclass
class CostWeights:
    """代价权重配置"""
    distance: float = 1.0          # 距离权重
    slope: float = 1.0             # 坡度权重
    landcover: float = 1.0         # 地表类型权重
    exposure: float = 1.0          # 暴露度权重
    elevation_change: float = 0.5   # 高程变化权重

# 预定义场景权重
SCENARIO_WEIGHTS = {
    ScenarioType.STANDARD: CostWeights(
        distance=1.0, slope=0.8, landcover=0.6, exposure=0.4, elevation_change=0.3
    ),
    ScenarioType.HIGH_MOBILITY: CostWeights(
        distance=1.2, slope=0.4, landcover=0.3, exposure=0.2, elevation_change=0.2
    ),
    ScenarioType.STEALTH: CostWeights(
        distance=0.8, slope=0.6, landcover=1.2, exposure=1.5, elevation_change=0.4
    ),
    ScenarioType.MOUNTAIN: CostWeights(
        distance=0.9, slope=1.5, landcover=0.8, exposure=0.6, elevation_change=1.0
    )
}

# 地表类型代价系数
LANDCOVER_COSTS = {
    10: 8.0,    # 水域 - 很高代价
    20: 3.0,    # 湿地 - 中高代价
    30: 1.0,    # 草地 - 低代价
    40: 2.0,    # 灌木地 - 中等代价
    50: 0.5,    # 建筑用地 - 很低代价
    60: 1.2,    # 农田 - 稍高代价
    80: 4.0,    # 森林 - 高代价
    90: 2.5,    # 荒地 - 中等代价
    255: 3.0    # 未分类 - 默认代价
}

# 坡度代价函数参数
SLOPE_COST_PARAMS = {
    'flat_threshold': 5.0,      # 平坦地形阈值（度）
    'steep_threshold': 25.0,    # 陡峭地形阈值（度）
    'max_cost_multiplier': 10.0 # 最大代价倍数
}

class EnvironmentCostModel:
    """环境代价模型"""
    
    def __init__(self, env_maps, weights: CostWeights):
        """
        初始化环境代价模型
        
        Args:
            env_maps: 环境地图对象
            weights: 代价权重配置
        """
        self.env_maps = env_maps
        self.weights = weights
        
    def calculate_node_cost(self, x: float, y: float) -> float:
        """
        计算节点代价
        
        Args:
            x, y: UTM坐标
            
        Returns:
            节点总代价
        """
        # 获取环境特征
        features = self.env_maps.query_features(x, y)
        
        total_cost = 0.0
        
        # 1. 地表类型代价
        landcover_code = int(features.get('landcover', 255))
        landcover_cost = LANDCOVER_COSTS.get(landcover_code, 3.0)
        total_cost += self.weights.landcover * landcover_cost
        
        # 2. 坡度代价
        slope = features.get('slope', 0.0)
        slope_cost = self._calculate_slope_cost(slope)
        total_cost += self.weights.slope * slope_cost
        
        # 3. 暴露度代价（基于地形开阔程度）
        exposure_cost = self._calculate_exposure_cost(features)
        total_cost += self.weights.exposure * exposure_cost
        
        return total_cost
        
    def calculate_edge_cost(self, from_x: float, from_y: float, 
                          to_x: float, to_y: float) -> float:
        """
        计算边代价
        
        Args:
            from_x, from_y: 起始点UTM坐标
            to_x, to_y: 终点UTM坐标
            
        Returns:
            边总代价
        """
        # 基础距离代价
        distance = math.sqrt((to_x - from_x)**2 + (to_y - from_y)**2)
        distance_cost = self.weights.distance * distance
        
        # 高程变化代价
        from_features = self.env_maps.query_features(from_x, from_y)
        to_features = self.env_maps.query_features(to_x, to_y)
        
        elevation_change = abs(to_features.get('dem', 0) - from_features.get('dem', 0))
        elevation_cost = self.weights.elevation_change * elevation_change
        
        # 中点环境代价
        mid_x = (from_x + to_x) / 2
        mid_y = (from_y + to_y) / 2
        mid_node_cost = self.calculate_node_cost(mid_x, mid_y)
        
        return distance_cost + elevation_cost + mid_node_cost * 0.5
        
    def _calculate_slope_cost(self, slope_degrees: float) -> float:
        """计算坡度代价"""
        if slope_degrees <= SLOPE_COST_PARAMS['flat_threshold']:
            return 1.0
        elif slope_degrees <= SLOPE_COST_PARAMS['steep_threshold']:
            # 线性增长
            ratio = (slope_degrees - SLOPE_COST_PARAMS['flat_threshold']) / \
                   (SLOPE_COST_PARAMS['steep_threshold'] - SLOPE_COST_PARAMS['flat_threshold'])
            return 1.0 + ratio * (SLOPE_COST_PARAMS['max_cost_multiplier'] - 1.0)
        else:
            return SLOPE_COST_PARAMS['max_cost_multiplier']
            
    def _calculate_exposure_cost(self, features: Dict) -> float:
        """计算暴露度代价"""
        # 基于地表类型估算暴露度
        landcover_code = int(features.get('landcover', 255))
        
        # 开阔地形暴露度高
        if landcover_code in [30, 60, 90]:  # 草地、农田、荒地
            return 2.0
        elif landcover_code in [40, 80]:     # 灌木地、森林
            return 0.5
        elif landcover_code == 50:          # 建筑用地
            return 1.5
        else:
            return 1.0

class HierarchicalAStar:
    """分层A*路径规划器"""
    
    def __init__(self, env_maps, scenario: ScenarioType = ScenarioType.STANDARD):
        """
        初始化分层A*规划器
        
        Args:
            env_maps: 环境地图对象
            scenario: 场景类型
        """
        self.env_maps = env_maps
        self.scenario = scenario
        self.weights = SCENARIO_WEIGHTS[scenario]
        self.cost_model = EnvironmentCostModel(env_maps, self.weights)
        
        # 规划参数
        self.coarse_resolution = 50.0   # 粗规划分辨率（米）
        self.fine_resolution = 10.0     # 精规划分辨率（米）
        
        print(f"初始化分层A*规划器，场景: {scenario.value}")
        
    def plan_path(self, start_utm: Tuple[float, float], 
                  goal_utm: Tuple[float, float]) -> List[Tuple[float, float]]:
        """
        规划路径
        
        Args:
            start_utm: 起点UTM坐标
            goal_utm: 终点UTM坐标
            
        Returns:
            路径点列表 [(x1, y1), (x2, y2), ...]
        """
        print(f"开始路径规划: {start_utm} -> {goal_utm}")
        
        # 第一层：粗规划
        coarse_path = self._coarse_planning(start_utm, goal_utm)
        if not coarse_path:
            print("粗规划失败")
            return []
            
        print(f"粗规划完成，路径点数: {len(coarse_path)}")
        
        # 第二层：精规划
        fine_path = self._fine_planning(coarse_path)
        if not fine_path:
            print("精规划失败，返回粗规划结果")
            return coarse_path
            
        print(f"精规划完成，最终路径点数: {len(fine_path)}")
        return fine_path
        
    def _coarse_planning(self, start: Tuple[float, float], 
                        goal: Tuple[float, float]) -> List[Tuple[float, float]]:
        """粗规划：大尺度A*搜索"""
        return self._astar_search(start, goal, self.coarse_resolution)
        
    def _fine_planning(self, coarse_path: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """精规划：对粗规划的每一段进行细化"""
        if len(coarse_path) < 2:
            return coarse_path
            
        fine_path = [coarse_path[0]]
        
        for i in range(len(coarse_path) - 1):
            segment_start = coarse_path[i]
            segment_goal = coarse_path[i + 1]
            
            # 对每一段进行精规划
            segment_path = self._astar_search(segment_start, segment_goal, self.fine_resolution)
            
            if segment_path and len(segment_path) > 1:
                # 添加精规划结果（除第一个点外）
                fine_path.extend(segment_path[1:])
            else:
                # 精规划失败，直接连接
                fine_path.append(segment_goal)
                
        return fine_path
        
    def _astar_search(self, start: Tuple[float, float], 
                     goal: Tuple[float, float], 
                     resolution: float) -> List[Tuple[float, float]]:
        """A*搜索算法"""
        
        # 将坐标转换为网格索引
        start_grid = self._utm_to_grid(start, resolution)
        goal_grid = self._utm_to_grid(goal, resolution)
        
        # 检查起点和终点是否可达
        if not self._is_valid_position(start) or not self._is_valid_position(goal):
            return []
            
        # A*搜索数据结构
        open_set = []
        heapq.heappush(open_set, (0.0, start_grid))
        
        came_from = {}
        g_score = {start_grid: 0.0}
        f_score = {start_grid: self._heuristic(start_grid, goal_grid, resolution)}
        
        closed_set = set()
        
        while open_set:
            current_f, current = heapq.heappop(open_set)
            
            if current in closed_set:
                continue
                
            closed_set.add(current)
            
            # 检查是否到达目标
            if self._grid_distance(current, goal_grid) <= 1:
                # 重构路径
                path_grid = self._reconstruct_path(came_from, current)
                # 转换回UTM坐标
                path_utm = [self._grid_to_utm(grid_pos, resolution) for grid_pos in path_grid]
                # 确保终点准确
                path_utm.append(goal)
                return path_utm
                
            # 探索邻居
            for neighbor in self._get_neighbors(current):
                if neighbor in closed_set:
                    continue
                    
                neighbor_utm = self._grid_to_utm(neighbor, resolution)
                if not self._is_valid_position(neighbor_utm):
                    continue
                    
                current_utm = self._grid_to_utm(current, resolution)
                tentative_g = g_score[current] + self.cost_model.calculate_edge_cost(
                    current_utm[0], current_utm[1], neighbor_utm[0], neighbor_utm[1]
                )
                
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score[neighbor] = tentative_g + self._heuristic(neighbor, goal_grid, resolution)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
                    
        # 搜索失败
        return []
        
    def _utm_to_grid(self, utm_pos: Tuple[float, float], resolution: float) -> Tuple[int, int]:
        """UTM坐标转网格索引"""
        bounds = self.env_maps.bounds
        grid_x = int((utm_pos[0] - bounds.left) / resolution)
        grid_y = int((utm_pos[1] - bounds.bottom) / resolution)
        return (grid_x, grid_y)
        
    def _grid_to_utm(self, grid_pos: Tuple[int, int], resolution: float) -> Tuple[float, float]:
        """网格索引转UTM坐标"""
        bounds = self.env_maps.bounds
        utm_x = bounds.left + grid_pos[0] * resolution + resolution / 2
        utm_y = bounds.bottom + grid_pos[1] * resolution + resolution / 2
        return (utm_x, utm_y)
        
    def _is_valid_position(self, utm_pos: Tuple[float, float]) -> bool:
        """检查位置是否有效"""
        bounds = self.env_maps.bounds
        if not (bounds.left <= utm_pos[0] <= bounds.right and
                bounds.bottom <= utm_pos[1] <= bounds.top):
            return False
            
        # 检查是否为不可通过区域（如水域）
        features = self.env_maps.query_features(utm_pos[0], utm_pos[1])
        landcover = int(features.get('landcover', 255))
        
        # 水域基本不可通过
        if landcover == 10:
            return False
            
        return True
        
    def _get_neighbors(self, grid_pos: Tuple[int, int]) -> List[Tuple[int, int]]:
        """获取8邻域"""
        x, y = grid_pos
        neighbors = []
        
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx == 0 and dy == 0:
                    continue
                neighbors.append((x + dx, y + dy))
                
        return neighbors
        
    def _grid_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """计算网格距离"""
        return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
        
    def _heuristic(self, pos1: Tuple[int, int], pos2: Tuple[int, int], resolution: float) -> float:
        """启发函数（欧几里得距离）"""
        grid_dist = self._grid_distance(pos1, pos2)
        return grid_dist * resolution * self.weights.distance
        
    def _reconstruct_path(self, came_from: Dict, current: Tuple[int, int]) -> List[Tuple[int, int]]:
        """重构路径"""
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        path.reverse()
        return path

def plan_multiple_paths(env_maps, waypoints: List[Tuple[float, float]], 
                       scenario: ScenarioType = ScenarioType.STANDARD) -> List[Tuple[float, float]]:
    """
    规划多航点路径
    
    Args:
        env_maps: 环境地图对象
        waypoints: 航点列表 [(x1, y1), (x2, y2), ...]
        scenario: 场景类型
        
    Returns:
        完整路径点列表
    """
    if len(waypoints) < 2:
        return waypoints
        
    planner = HierarchicalAStar(env_maps, scenario)
    complete_path = []
    
    for i in range(len(waypoints) - 1):
        segment_path = planner.plan_path(waypoints[i], waypoints[i + 1])
        
        if not segment_path:
            print(f"警告: 无法规划路径段 {i}: {waypoints[i]} -> {waypoints[i + 1]}")
            # 直接连接
            if not complete_path:
                complete_path.append(waypoints[i])
            complete_path.append(waypoints[i + 1])
        else:
            # 添加路径段（避免重复点）
            if not complete_path:
                complete_path.extend(segment_path)
            else:
                complete_path.extend(segment_path[1:])  # 跳过重复的起点
                
    return complete_path

if __name__ == "__main__":
    # 示例使用
    from data_preprocessing import HighResolutionEnvironmentMaps
    
    # 初始化环境地图
    env_data_dir = "trajectory_generation_module_pkg/examples/data/environment"
    env_maps = HighResolutionEnvironmentMaps(env_data_dir, use_original_resolution=True)
    
    # 定义起点和终点
    start = (env_maps.bounds.left + 1000, env_maps.bounds.bottom + 1000)
    goal = (env_maps.bounds.right - 1000, env_maps.bounds.top - 1000)
    
    # 规划路径
    planner = HierarchicalAStar(env_maps, ScenarioType.STANDARD)
    path = planner.plan_path(start, goal)
    
    print(f"规划完成，路径包含 {len(path)} 个点")
    for i, point in enumerate(path[:5]):  # 显示前5个点
        print(f"Point {i}: {point}")