#!/bin/bash
DEM_DIR="data/temp_dem_zip"
BASE_URL="http://viewfinderpanoramas.org/dem3/"
ALL_ZIPS=("N28.zip" "N29.zip" "N30.zip" "N31.zip" "N32.zip" "N33.zip" "N34.zip" "N35.zip" "N47.zip" "N48.zip" "N49.zip" "N56.zip" "N57.zip" "N58.zip")

echo "--- 后台下载器已启动于 $(date) ---"
echo "将检查和下载 14 个文件..."

while true; do
    all_done=true
    for zip_name in "${ALL_ZIPS[@]}"; do
        dest_path="$DEM_DIR/$zip_name"
        if [ ! -f "$dest_path" ] || [ $(stat -c%s "$dest_path") -lt 1024 ]; then
            all_done=false
            echo "BG DOWNLOAD [$(date)] -- 正在下载: $zip_name"
            wget -q -c -T 120 -O "$dest_path.tmp" "$BASE_URL$zip_name"
            if [ $? -eq 0 ]; then
                mv "$dest_path.tmp" "$dest_path"
                echo "BG DOWNLOAD [$(date)] -- 成功: $zip_name"
            else
                rm -f "$dest_path.tmp"
                echo "BG DOWNLOAD [$(date)] -- 失败，将在下一轮重试: $zip_name"
            fi
            sleep 5 # 每次下载间歇
        fi
    done

    if [ "$all_done" = true ]; then
        echo "--- 所有DEM文件下载完成于 $(date) ---"
        break
    fi

    echo "本轮检查完毕, 5分钟后开始下一轮..."
    sleep 300
done
