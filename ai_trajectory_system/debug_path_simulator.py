#!/usr/bin/env python3
"""
调试版本的基于路径点仿真器
用于检查环境数据和仿真问题
"""

import numpy as np
import pandas as pd
import os
import rasterio
import warnings
warnings.filterwarnings('ignore')

# 环境数据路径
ENV_DATA_DIR = 'trajectory_generator/data/environment'
SLOPE_FILE = os.path.join(ENV_DATA_DIR, 'slope_aligned.tif')
ASPECT_FILE = os.path.join(ENV_DATA_DIR, 'aspect_aligned.tif') 
LANDCOVER_FILE = os.path.join(ENV_DATA_DIR, 'landcover_aligned.tif')

# 土地覆盖类型映射
LANDCOVER_MAPPING = {
    20: '林地',
    40: '灌木地', 
    60: '水体',
}

def check_environment_files():
    """检查环境文件是否存在"""
    print("=== 检查环境文件 ===")
    files = [SLOPE_FILE, ASPECT_FILE, LANDCOVER_FILE]
    names = ['坡度', '坡向', '土地覆盖']
    
    for i, file_path in enumerate(files):
        if os.path.exists(file_path):
            print(f"✓ {names[i]}文件存在: {file_path}")
            try:
                with rasterio.open(file_path) as src:
                    print(f"  - 形状: {src.shape}")
                    print(f"  - 范围: {src.bounds}")
                    print(f"  - CRS: {src.crs}")
            except Exception as e:
                print(f"  - 读取错误: {e}")
        else:
            print(f"✗ {names[i]}文件不存在: {file_path}")

def load_and_check_trajectory():
    """加载并检查轨迹数据"""
    print("\n=== 检查轨迹数据 ===")
    trajectory_file = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    
    if not os.path.exists(trajectory_file):
        print(f"✗ 轨迹文件不存在: {trajectory_file}")
        return None
    
    df = pd.read_csv(trajectory_file)
    print(f"✓ 轨迹文件加载成功，行数: {len(df)}")
    print(f"  - 列名: {list(df.columns)}")
    
    # 重命名坐标列
    if 'latitude' in df.columns and 'longitude' in df.columns:
        df.rename(columns={'latitude': 'utm_x', 'longitude': 'utm_y'}, inplace=True)
        print("  - 已重命名坐标列: latitude->utm_x, longitude->utm_y")
    
    # 数据清洗
    print(f"  - UTM-X 范围: {df['utm_x'].min():.1f} ~ {df['utm_x'].max():.1f}")
    print(f"  - UTM-Y 范围: {df['utm_y'].min():.1f} ~ {df['utm_y'].max():.1f}")
    
    valid_mask = (
        (df['utm_x'] > 1000000) & (df['utm_x'] < 50000000) &
        (df['utm_y'] > -50000000) & (df['utm_y'] < 50000000)
    )
    df_clean = df[valid_mask].copy()
    print(f"  - 清洗后行数: {len(df_clean)}")
    
    return df_clean

def get_environment_features_debug(df):
    """获取环境特征数据（调试版本）"""
    print("\n=== 获取环境特征 ===")
    
    # 准备坐标列表
    coordinates = []
    for i, (_, row) in enumerate(df.iterrows()):
        x = row['utm_x']  # UTM-X (easting)
        y = row['utm_y']  # UTM-Y (northing)
        coordinates.append((x, y))
        if i < 5:  # 只打印前5个
            print(f"  点{i}: UTM坐标({x:.1f}, {y:.1f})")
    
    print(f"总坐标数: {len(coordinates)}")
    
    # 使用rasterio.sample方法读取环境数据
    slope_values = get_raster_values_debug(SLOPE_FILE, coordinates, "坡度")
    aspect_values = get_raster_values_debug(ASPECT_FILE, coordinates, "坡向")
    landcover_values = get_raster_values_debug(LANDCOVER_FILE, coordinates, "土地覆盖")
    
    return slope_values, aspect_values, landcover_values

def get_raster_values_debug(raster_path, coordinates, name):
    """从栅格文件中获取指定坐标的值（调试版本）"""
    print(f"\n--- 读取{name}数据 ---")
    values = []
    try:
        with rasterio.open(raster_path) as src:
            print(f"{name}文件边界: {src.bounds}")
            print(f"{name}文件CRS: {src.crs}")
            
            # 检查前几个坐标是否在边界内
            bounds = src.bounds
            in_bounds_count = 0
            for i, (x, y) in enumerate(coordinates[:10]):
                in_bounds = (bounds.left <= x <= bounds.right and 
                           bounds.bottom <= y <= bounds.top)
                if in_bounds:
                    in_bounds_count += 1
                if i < 5:
                    print(f"  坐标{i}: ({x:.1f}, {y:.1f}) -> {'在边界内' if in_bounds else '超出边界'}")
            
            print(f"前10个坐标中，{in_bounds_count}/10 在边界内")
            
            # 使用rasterio.sample方法
            sampled_values = list(src.sample(coordinates))
            
            # 转换结果
            valid_count = 0
            for i, val in enumerate(sampled_values):
                if len(val) > 0 and not np.isnan(val[0]):
                    values.append(val[0])
                    valid_count += 1
                    if i < 10:
                        print(f"  样本{i}: {val[0]}")
                else:
                    values.append(np.nan)
                    if i < 10:
                        print(f"  样本{i}: NaN")
            
            print(f"{name}有效样本数: {valid_count}/{len(coordinates)}")
            
    except Exception as e:
        print(f"读取{name}数据时出错: {e}")
        values = [np.nan] * len(coordinates)
    
    return values

def main():
    """主调试函数"""
    print("=== 路径仿真器调试工具 ===")
    
    # 1. 检查环境文件
    check_environment_files()
    
    # 2. 检查轨迹数据
    df = load_and_check_trajectory()
    if df is None:
        return
    
    # 3. 获取环境特征（前100个点）
    df_sample = df.head(100)
    slope_values, aspect_values, landcover_values = get_environment_features_debug(df_sample)
    
    if slope_values is None:
        return
    
    # 4. 总结
    print("\n=== 总结 ===")
    valid_slopes = sum(1 for x in slope_values if not np.isnan(x))
    valid_aspects = sum(1 for x in aspect_values if not np.isnan(x))
    valid_landcovers = sum(1 for x in landcover_values if not np.isnan(x))
    mapped_landcovers = sum(1 for x in landcover_values if not np.isnan(x) and x in LANDCOVER_MAPPING)
    
    print(f"样本数量: {len(df_sample)}")
    print(f"有效坡度: {valid_slopes}")
    print(f"有效坡向: {valid_aspects}")
    print(f"有效土地覆盖: {valid_landcovers}")
    print(f"可映射土地覆盖: {mapped_landcovers}")
    
    if mapped_landcovers == 0:
        print("\n❌ 问题：没有可映射的土地覆盖类型，这是仿真失败的原因！")
        print("   解决方案：需要更新土地覆盖类型映射表")
    else:
        print(f"\n✓ 有 {mapped_landcovers} 个点可以进行仿真")

if __name__ == "__main__":
    main() 