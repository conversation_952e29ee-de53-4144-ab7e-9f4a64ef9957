# AI 轨迹生成系统 v2.0

这是一个基于机器学习的智能轨迹生成系统。其核心思想是**从真实轨迹数据中学习环境特征与车辆速度之间的关系**，然后利用这个模型，在给定任意路径和初始状态的情况下，生成一条全新的、带有合理速度变化的、物理可行的轨迹。

本系统是对早期基于深度学习（Transformer）控制模仿方案的重大改进。实践证明，旧方案生成的轨迹与原始轨迹耦合过紧，缺乏创造性。新方案通过解耦"速度预测"和"路径跟踪"，实现了真正意义上的轨迹**生成**而非**复现**。

## 系统架构

系统分为两个核心阶段：

### 阶段一：离线训练 (环境-速度模型)

- **脚本**: `environment_speed_model.py`
- **目标**: 训练一个能根据环境和路径几何特征预测速度的模型。
- **流程**:
  1. 加载多条真实轨迹数据 (`/trajectory_generator/data/trajectories/*.csv`)。
  2. 从轨迹中提取环境和几何特征，例如：
     - **路径曲率**: 描述路径的弯曲程度。
     - **局部路径复杂度**: 结合曲率和转向率的综合指标。
     - **高程/坡度代理**: 从坐标变化中推断的伪高程和伪坡度。
     - **位置特征**: 坐标、距起点距离等。
  3. 使用这些特征作为输入（X），真实速度作为输出（y），训练一个**随机森林回归模型** (`RandomForestRegressor`)。
  4. 将训练好的模型和所用特征列表序列化保存到 `ai_trajectory_system/models/environment_speed_model.pkl`。

### 阶段二：在线仿真 (智能轨迹生成)

- **脚本**: `smart_trajectory_simulator.py`
- **目标**: 使用训练好的速度模型，在给定的路径上生成一条新轨迹。
- **流程**:
  1. **初始化**:
     - 加载一个真实轨迹文件以提取其**路径点**（仅用作导航引导）和**初始状态**（可选）。
     - 加载训练好的环境-速度模型。
  2. **仿真循环 (每一步)**:
     - **环境感知**: 计算当前位置周围的路径几何特征（曲率、复杂度等）。
     - **速度预测**: 将环境特征输入速度模型，得到一个目标速度。
     - **控制决策**: 基于目标速度和纯粹追踪算法（Pure Pursuit），计算出瞬时的油门/刹车和转向指令。
     - **状态更新**: 将控制指令输入`vehicle_kinematics.py`中的车辆动力学模型，更新车辆的位置、朝向、速度等状态。
     - **记录与终止**: 记录当前帧的完整状态，并检查是否到达路径终点或触发终止条件。
  3. **结果输出**: 保存生成的轨迹数据和对比分析图。

## 如何使用

### 步骤 1: 安装依赖

```bash
pip install -r ai_trajectory_system/requirements.txt
```

### 步骤 2: 训练速度模型

首先，我们需要运行训练脚本来创建环境-速度预测模型。

```bash
python ai_trajectory_system/environment_speed_model.py
```

该脚本会自动处理数据、训练模型，并将最终模型保存在 `ai_trajectory_system/models/` 目录下。同时，会在 `ai_trajectory_system/results/` 目录下生成一张特征重要性图表，帮助我们理解哪些环境因素对速度影响最大。

### 步骤 3: 运行轨迹仿真

模型训练好后，运行智能仿真器脚本来生成轨迹。

```bash
python ai_trajectory_system/smart_trajectory_simulator.py
```

此脚本会：
1.  加载一个示例轨迹作为路径参考。
2.  加载上一步训练好的速度模型。
3.  执行仿真，生成一条全新的轨迹。
4.  在 `ai_trajectory_system/results/` 目录下保存生成的轨迹数据(CSV)和一张将生成轨迹与原始路径进行对比的可视化图(PNG)。

## 核心模块 (文件结构)

项目结构已被大幅简化，核心文件如下：

```
ai_trajectory_system/
├── config.py                     # 系统配置，包含物理参数、路径等
├── data_processor.py             # 复杂数据处理工具（如处理栅格数据）
├── environment_speed_model.py    # (核心) 训练环境-速度预测模型
├── smart_trajectory_simulator.py # (核心) 智能轨迹仿真器
├── test_environment_model.py     # 用于验证速度模型的测试脚本
├── vehicle_kinematics.py         # 车辆运动学模型 (自行车模型)
├── comparison_summary.py         # 用于生成详细对比报告的脚本
├── deviation_monitor.py          # 路径偏差监控工具
├── requirements.txt              # Python 依赖库
├── README.md                     # 本文档
├── models/                       # 存放训练好的模型
└── results/                      # 存放仿真和分析结果
```

## 关键概念

- **解耦**: 我们将复杂的轨迹生成问题解耦为两个子问题：① **速度预测** (机器学习任务) 和 ② **路径跟踪** (经典控制任务)。这种方式比端到端的模仿学习更稳定、可解释性更强。
- **环境驱动**: 车辆的速度由其所处的环境（路径的弯曲、复杂程度）动态决定，而非简单重复原始轨迹的速度曲线。这是实现轨迹"生成"而非"复现"的关键。
- **物理一致性**: 所有生成的轨迹都经过`vehicle_kinematics.py`中的动力学模型演算，确保其符合车辆的基本物理限制（如最大速度、加速度、转向半径等）。

🎉 系统当前状态稳定，核心功能已完成，可以稳定地生成高质量轨迹。 