import os
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import joblib
import logging
from scipy.spatial import cKDTree
import matplotlib.pyplot as plt

# --- 基本设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 静态配置 ---
class Config:
    """存放所有静态配置，方便统一管理"""
    # 文件路径
    REAL_TRAJECTORY_FILEPATH = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    DATA_PATH_PREFIX = 'trajectory_generator/data/environment/'

    # 特征列
    ENVIRONMENT_FEATURES = ['dem', 'slope', 'aspect', 'landcover']
    STATE_FEATURES = ['speed']
    GUIDANCE_FEATURES = ['distance_to_target', 'angle_to_target']
    
    # 模型和仿真参数
    MODEL_FILENAME = "target_speed_model.pkl"
    SIMULATION_DT = 0.5  # 仿真步长时间 (秒)
    MAX_SIM_STEPS = 800

    # 物理约束
    MAX_SPEED = 20.0  # m/s
    ACCELERATION_RANGE = (-3.0, 2.5)  # (min_acel, max_acel) in m/s^2
    HEADING_CHANGE_RATE_RANGE = (-np.pi / 4, np.pi / 4)  # (min_rate, max_rate) in rad/s

# --- 环境数据模型 ---
class Environment:
    """负责加载和查询所有环境栅格数据"""
    def __init__(self, data_path_prefix):
        self.data_sources = {}
        try:
            for name in Config.ENVIRONMENT_FEATURES:
                filepath = os.path.join(data_path_prefix, f"{name}_aligned.tif")
                if os.path.exists(filepath):
                    self._load_raster(name, filepath)
        else:
                    logging.warning(f"警告: 环境数据文件未找到: {filepath}")
        except Exception as e:
            logging.error(f"加载环境数据时出错: {e}", exc_info=True)
            raise

    def _load_raster(self, name, filepath):
        import rasterio
        raster = rasterio.open(filepath)
        self.data_sources[name] = {'raster': raster, 'transform': raster.transform}
        logging.info(f"成功加载环境数据: {name}")

    def get_features_at_point(self, x, y):
        """获取单个点位的环境特征值"""
        features = {}
        for name, source in self.data_sources.items():
            try:
                row, col = source['raster'].index(x, y)
                value = source['raster'].read(1, window=((row, row+1), (col, col+1)))
                features[name] = value[0, 0] if value.size > 0 else 0
            except IndexError:
                logging.warning(f"点({x:.2f}, {y:.2f}) 在栅格 '{name}' 的范围外。返回默认值 0。")
                features[name] = 0
        return features

# --- AI模型: 目标速度预测 ---
class TargetSpeedModel:
    """基于随机森林的模型，用于预测目标速度"""
    def __init__(self, model_path=Config.MODEL_FILENAME):
        self.model_path = model_path
        self.model = None
        self.feature_names = Config.ENVIRONMENT_FEATURES + Config.STATE_FEATURES + Config.GUIDANCE_FEATURES

    def create_features_and_labels(self, df):
        logging.info("为目标速度模型创建特征和标签...")
        X = df[self.feature_names]
        y = df['speed'].shift(-1)
        X, y = X.iloc[:-1], y.iloc[:-1]
        logging.info("特征和标签创建完成。")
        return X, y

    def train(self, X, y, force_retrain=False):
        if not force_retrain and os.path.exists(self.model_path):
            self.model = joblib.load(self.model_path)
            logging.info(f"从 {self.model_path} 加载已训练的模型。")
            return

        logging.info("训练新的目标速度模型...")
        self.model = RandomForestRegressor(
            n_estimators=100, max_depth=10, min_samples_split=5, 
            min_samples_leaf=2, random_state=42, n_jobs=-1
        )
        self.model.fit(X, y)
        joblib.dump(self.model, self.model_path)
        logging.info(f"目标速度模型训练完成并保存到 {self.model_path}")

    def predict(self, features_df):
        if self.model is None: raise RuntimeError("模型尚未训练或加载。")
        return self.model.predict(features_df[self.feature_names])

# --- PID控制器 ---
class PIDController:
    """一个简单的PID控制器"""
    def __init__(self, Kp, Ki, Kd, output_limits, integral_limits):
        self.Kp, self.Ki, self.Kd = Kp, Ki, Kd
        self.output_limits = output_limits
        self.integral_limits = integral_limits
        self._integral = 0
        self._previous_error = 0

    def update(self, setpoint, current_value, dt):
        error = setpoint - current_value
        self._integral += error * dt
        self._integral = np.clip(self._integral, self.integral_limits[0], self.integral_limits[1])
        derivative = (error - self._previous_error) / dt
        self._previous_error = error
        output = self.Kp * error + self.Ki * self._integral + self.Kd * derivative
        return np.clip(output, self.output_limits[0], self.output_limits[1])

# --- 主仿真器: AI+PID 分层控制 ---
class PIDPathGuidedSimulator:
    """使用AI预测目标，PID执行控制的仿真器"""
    def __init__(self, ai_model, environment, guide_path_df):
        logging.info("基于PID的智能轨迹仿真器初始化...")
        self.ai_model = ai_model
        self.environment = environment
        self.guide_path = guide_path_df[['x', 'y']].values
        self.guide_path_kdtree = cKDTree(self.guide_path)
        
        # 修正: 大幅降低PID增益，使其更平滑
        # Kp降低，响应变缓；Ki降低，消除稳态误差的速度变慢，但能减少积分饱和
        self.speed_pid = PIDController(Kp=0.4, Ki=0.05, Kd=0.1, output_limits=Config.ACCELERATION_RANGE, integral_limits=(-4, 4))
        self.steering_pid = PIDController(Kp=0.6, Ki=0.02, Kd=0.08, output_limits=Config.HEADING_CHANGE_RATE_RANGE, integral_limits=(-np.pi/8, np.pi/8))
        
        self.state = {}
        self.simulation_results = []
        logging.info("PID仿真器初始化完成。")

    def set_initial_state(self, x, y, speed, heading_deg):
        self.state = {'x': x, 'y': y, 'speed': speed, 'heading': np.deg2rad(heading_deg)}
        self.simulation_results = [self.state.copy()]
        logging.info(f"设置初始状态: 位置({x:.1f}, {y:.1f}), 速度={speed:.3f} m/s, 航向={heading_deg:.1f}°")

    def _get_current_features_and_target_heading(self):
        x, y = self.state['x'], self.state['y']
        env_features = self.environment.get_features_at_point(x, y)
        
        _, target_idx = self.guide_path_kdtree.query([x, y], k=1)
        # 修正: 缩短引导距离，防止低速时目标点太远导致航向剧烈变化
        next_target_idx = min(target_idx + 2, len(self.guide_path) - 1) # 从+5改为+2
        target_x, target_y = self.guide_path[next_target_idx]
        
        distance_to_target = np.hypot(target_x - x, target_y - y)
        angle_to_target = np.arctan2(target_y - y, target_x - x) # 目标航向

        features = {**env_features, 'speed': self.state['speed'], 'distance_to_target': distance_to_target, 'angle_to_target': angle_to_target}
        return features, angle_to_target

    def _simulation_step(self):
        features_dict, target_heading = self._get_current_features_and_target_heading()
        features_df = pd.DataFrame([features_dict])
        
        target_speed = self.ai_model.predict(features_df)[0]
        target_speed = np.clip(target_speed, 0, Config.MAX_SPEED)
        
        acceleration = self.speed_pid.update(target_speed, self.state['speed'], Config.SIMULATION_DT)
        
        heading_error = target_heading - self.state['heading']
        heading_error = (heading_error + np.pi) % (2 * np.pi) - np.pi
        heading_change_rate = self.steering_pid.update(0, -heading_error, Config.SIMULATION_DT)

        speed, heading = self.state['speed'], self.state['heading']
        new_speed = np.clip(speed + acceleration * Config.SIMULATION_DT, 0, Config.MAX_SPEED)
        new_heading = (heading + heading_change_rate * Config.SIMULATION_DT + np.pi) % (2 * np.pi) - np.pi
        
        avg_speed = (speed + new_speed) / 2
        self.state.update({
            'x': self.state['x'] + avg_speed * np.cos(new_heading) * Config.SIMULATION_DT,
            'y': self.state['y'] + avg_speed * np.sin(new_heading) * Config.SIMULATION_DT,
            'speed': new_speed, 'heading': new_heading, 'target_speed': target_speed,
            'acceleration': acceleration, 'heading_change_rate': heading_change_rate
        })

    def run_simulation(self, max_steps=Config.MAX_SIM_STEPS):
        logging.info(f"开始PID驱动仿真 (最大步数: {max_steps})")
        for i in range(max_steps):
            self._simulation_step()
            self.simulation_results.append(self.state.copy())
            if (i + 1) % 100 == 0:
                logging.info(f"步数: {i+1}, Pos:({self.state['x']:.1f},{self.state['y']:.1f}), Vel:{self.state['speed']:.2f} m/s, Accel:{self.state['acceleration']:.2f} m/s²")
        logging.info(f"仿真完成: 总步数 {len(self.simulation_results) - 1}")
        return pd.DataFrame(self.simulation_results)

# --- 数据处理 ---
def prepare_data(filepath, environment):
    """加载并准备所有训练和参考所需的数据"""
    logging.info(f"开始预处理轨迹数据: {filepath}")
    df = pd.read_csv(filepath)
    df.rename(columns={'latitude': 'x', 'longitude': 'y', 'timestamp_ms': 'time'}, inplace=True)
    
    # 计算运动学特征
    df['dt'] = df['time'].diff() / 1000.0 # 转换为秒
    df['dt'] = df['dt'].replace(0, 0.001).fillna(0.5) # 避免除以零并填充
    df['speed'] = np.hypot(df['x'].diff(), df['y'].diff()) / df['dt']
    df['acceleration'] = df['speed'].diff() / df['dt'] # 为真实轨迹计算加速度
    df['heading'] = np.arctan2(df['y'].diff(), df['x'].diff())
    
    # 使用前向和后向填充处理NaN值
    df.fillna(method='bfill', inplace=True)
    df.fillna(method='ffill', inplace=True)
    
    logging.info("为轨迹点添加环境特征...")
    env_features = df.apply(lambda row: environment.get_features_at_point(row['x'], row['y']), axis=1, result_type='expand')
    df = pd.concat([df, env_features], axis=1)

    logging.info("为轨迹点添加引导特征...")
    guide_path = df[['x', 'y']].values
    kdtree = cKDTree(guide_path)
    
    def add_guidance(row, i):
        target_idx = min(i + 5, len(guide_path) - 1)
        target_pos = guide_path[target_idx]
        dist = np.hypot(target_pos[0] - row['x'], target_pos[1] - row['y'])
        angle = np.arctan2(target_pos[1] - row['y'], target_pos[0] - row['x'])
        return pd.Series([dist, angle], index=['distance_to_target', 'angle_to_target'])

    guidance_df = pd.DataFrame([add_guidance(row, i) for i, row in df.iterrows()])
    return pd.concat([df, guidance_df], axis=1).copy() # 返回副本以避免SettingWithCopyWarning

# --- 可视化 ---
def visualize_comprehensive_results(real_df, sim_df, output_dir):
    """生成包含轨迹、速度和加速度的综合分析图"""
    logging.info(f"正在生成综合分析图表...")
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # 按照您的要求设置字体
    font_config = {'family': 'sans-serif', 'sans-serif': ['DejaVu Sans']}
    plt.rcParams.update({'font.sans-serif': font_config['sans-serif']})
    
    fig, axs = plt.subplots(3, 1, figsize=(18, 30), gridspec_kw={'height_ratios': [4, 1, 1]})

    # --- 子图1: 轨迹对比图 ---
    ax = axs[0]
    ax.plot(real_df['x'], real_df['y'], 'b-', label='真实轨迹', linewidth=3, alpha=0.7)
    ax.plot(sim_df['x'], sim_df['y'], 'r--', label='仿真轨迹 (AI+PID)', linewidth=2.5, alpha=0.9)
    ax.plot(real_df['x'].iloc[0], real_df['y'].iloc[0], 'go', markersize=15, label='起点', alpha=0.8)
    ax.plot(sim_df['x'].iloc[-1], sim_df['y'].iloc[-1], 'rx', markersize=15, mew=3, label='仿真终点', alpha=0.8)
    ax.set_title('真实轨迹 vs. AI+PID 仿真轨迹对比', fontsize=22, pad=20)
    ax.set_xlabel('UTM X 坐标 (m)', fontsize=20)
    ax.set_ylabel('UTM Y 坐标 (m)', fontsize=20)
    ax.legend(fontsize=20)
    ax.grid(True, which='both', linestyle='--', linewidth=0.7)
    ax.set_aspect('equal', adjustable='box')
    ax.tick_params(axis='both', which='major', labelsize=14)

    # --- 子图2: 速度 vs. 时间 ---
    ax = axs[1]
    real_time = (real_df['time'] - real_df['time'].iloc[0]) / 1000.0
    sim_time = np.arange(len(sim_df)) * Config.SIMULATION_DT
    ax.plot(real_time, real_df['speed'], 'b-', label='真实速度', alpha=0.7)
    ax.plot(sim_time, sim_df['speed'], 'r--', label='仿真速度', alpha=0.9)
    ax.plot(sim_time, sim_df['target_speed'], 'g:', label='AI目标速度', alpha=0.8, linewidth=2.5)
    ax.set_title('速度变化曲线', fontsize=22, pad=20)
    ax.set_xlabel('时间 (s)', fontsize=20)
    ax.set_ylabel('速度 (m/s)', fontsize=20)
    ax.legend(fontsize=20)
    ax.grid(True)
    ax.tick_params(axis='both', which='major', labelsize=14)
    ax.set_xlim(left=0)

    # --- 子图3: 加速度 vs. 时间 ---
    ax = axs[2]
    ax.plot(real_time, real_df['acceleration'], 'b-', label='真实加速度', alpha=0.7)
    ax.plot(sim_time, sim_df['acceleration'], 'r--', label='仿真加速度 (PID输出)', alpha=0.9)
    ax.set_title('加速度变化曲线', fontsize=22, pad=20)
    ax.set_xlabel('时间 (s)', fontsize=20)
    ax.set_ylabel('加速度 (m/s²)', fontsize=20)
    ax.legend(fontsize=20)
    ax.grid(True)
    ax.set_ylim(Config.ACCELERATION_RANGE[0] - 1, Config.ACCELERATION_RANGE[1] + 1)
    ax.tick_params(axis='both', which='major', labelsize=14)
    ax.set_xlim(left=0)

    fig.tight_layout(pad=3.0)
    save_path = os.path.join(output_dir, "comprehensive_analysis.png")
    plt.savefig(save_path, dpi=300)
    plt.close(fig)
    logging.info(f"综合分析图表已保存到: {save_path}")

# --- 主执行流程 ---
def main():
    environment = Environment(Config.DATA_PATH_PREFIX)
    ai_model = TargetSpeedModel()
    
    full_data_df = prepare_data(Config.REAL_TRAJECTORY_FILEPATH, environment)
    
    X, y = ai_model.create_features_and_labels(full_data_df)
    ai_model.train(X, y, force_retrain=True)

    simulator = PIDPathGuidedSimulator(ai_model, environment, full_data_df)
    
    scenarios = {
        "real_initial": {"speed": full_data_df['speed'].iloc[0], "heading_deg": np.rad2deg(full_data_df['heading'].iloc[0])},
        "from_rest": {"speed": 0.0, "heading_deg": np.rad2deg(full_data_df['heading'].iloc[0])},
        "high_speed_wrong_dir": {"speed": 15.0, "heading_deg": 0.0}
    }
    
    start_x, start_y = full_data_df['x'].iloc[0], full_data_df['y'].iloc[0]
    
    for name, params in scenarios.items():
        logging.info(f"\n--- 运行场景: {name} ---")
        output_dir = os.path.join("results", f"pid_sim_{name}")
        os.makedirs(output_dir, exist_ok=True)
            
        simulator.set_initial_state(x=start_x, y=start_y, **params)
        sim_results_df = simulator.run_simulation()
        
        sim_results_df.to_csv(os.path.join(output_dir, "trajectory.csv"), index=False)
        visualize_comprehensive_results(full_data_df, sim_results_df, output_dir)
    
    logging.info("\n所有PID仿真场景测试完成！")

if __name__ == "__main__":
    main() 