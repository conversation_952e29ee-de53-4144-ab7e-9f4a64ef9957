#!/usr/bin/env python3
"""
训练运动学模型的独立脚本
- 输入: 真实轨迹数据和环境数据
- 输出: 一个训练好的模型文件 (motion_model.pkl)
- 模型: 学习从 (状态+环境+导航) -> (加速度, 航向变化率) 的映射
"""

import numpy as np
import pandas as pd
import joblib
import os
import logging
import rasterio
from sklearn.model_selection import train_test_split
import xgboost as xgb

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 全局配置 ---
TRAJECTORY_FILE = 'trajectory_generator/data/trajectories/trajectory_1.csv'
ENV_DATA_DIR = 'trajectory_generator/data/environment'
MODEL_OUTPUT_PATH = "ai_trajectory_system/motion_model.pkl"

ENV_FILES = {
    'slope': os.path.join(ENV_DATA_DIR, 'slope_aligned.tif'),
    'aspect': os.path.join(ENV_DATA_DIR, 'aspect_aligned.tif'),
    'landcover': os.path.join(ENV_DATA_DIR, 'landcover_aligned.tif'),
    'dem': os.path.join(ENV_DATA_DIR, 'dem_aligned.tif')
}

# --- 辅助函数 ---

def get_raster_values(raster_path, coordinates):
    try:
        with rasterio.open(raster_path) as src:
            sampled_values = src.sample(coordinates)
            return [val[0] if len(val) > 0 else np.nan for val in sampled_values]
    except Exception as e:
        logger.error(f"读取栅格文件 {raster_path} 时出错: {e}")
        return [np.nan] * len(coordinates)

def calculate_effective_slope(slope, aspect, heading):
    if pd.isna(slope) or pd.isna(aspect) or pd.isna(heading): return np.nan
    angle_diff = abs(aspect - heading) % 360
    angle_diff = angle_diff if angle_diff <= 180 else 360 - angle_diff
    direction = -1 if angle_diff > 90 else 1
    return direction * slope * abs(np.cos(np.radians(angle_diff)))

def _calculate_guidance_features(df, waypoints):
    guidance_features = []
    waypoint_index = 0
    current_waypoint = waypoints[waypoint_index]
    for _, row in df.iterrows():
        vec_to_waypoint_x = current_waypoint[0] - row['longitude']
        vec_to_waypoint_y = current_waypoint[1] - row['latitude']
        distance_to_waypoint = np.sqrt(vec_to_waypoint_x**2 + vec_to_waypoint_y**2)
        angle_to_waypoint = np.arctan2(vec_to_waypoint_y, vec_to_waypoint_x)
        angle_diff = angle_to_waypoint - row['heading_rad']
        while angle_diff > np.pi: angle_diff -= 2 * np.pi
        while angle_diff < -np.pi: angle_diff += 2 * np.pi
        guidance_features.append({
            'distance_to_wp': distance_to_waypoint,
            'angle_to_wp': angle_diff
        })
        if distance_to_waypoint < 50.0 and waypoint_index < len(waypoints) - 1:
            waypoint_index += 1
            current_waypoint = waypoints[waypoint_index]
    return pd.DataFrame(guidance_features, index=df.index)

# --- 主训练流程 ---

def create_feature_dataset():
    logger.info("开始创建特征数据集...")
    df = pd.read_csv(TRAJECTORY_FILE)
    coordinates = list(zip(df['longitude'], df['latitude']))
    for feature_name, file_path in ENV_FILES.items():
        logger.info(f"正在加载环境特征: {feature_name}...")
        if os.path.exists(file_path):
            df[feature_name] = get_raster_values(file_path, coordinates)
        else:
            logger.warning(f"环境文件未找到: {file_path}, 将跳过该特征。")
            df[feature_name] = np.nan
    df['heading_rad'] = np.arctan2(df['velocity_north_ms'], df['velocity_east_ms'])
    df['effective_slope'] = [calculate_effective_slope(s, a, np.degrees(h)) for s, a, h in zip(df['slope'], df['aspect'], df['heading_rad'])]
    df['acceleration'] = df['velocity_2d_ms'].diff() / df['delta_time_s']
    df['heading_change_rate'] = df['heading_rad'].diff() / df['delta_time_s']
    waypoint_interval = 50
    waypoints = df[['longitude', 'latitude']].iloc[::waypoint_interval].values
    if len(waypoints) == 0:
        waypoints = df[['longitude', 'latitude']].iloc[[-1]].values
    guidance_df = _calculate_guidance_features(df, waypoints)
    df = pd.concat([df, guidance_df], axis=1)
    df['prev_acceleration'] = df['acceleration'].shift(1)
    df_clean = df.dropna(subset=[
        'velocity_2d_ms', 'effective_slope', 'landcover', 'dem',
        'acceleration', 'heading_change_rate', 'prev_acceleration',
        'distance_to_wp', 'angle_to_wp'
    ]).copy()
    df_clean.loc[:, 'landcover_code'] = df_clean['landcover'].astype('category').cat.codes
    features = df_clean[[
        'velocity_2d_ms', 'effective_slope', 'landcover_code', 'dem', 
        'prev_acceleration', 'distance_to_wp', 'angle_to_wp', 'heading_rad'
    ]]
    features.loc[:, 'heading_sin'] = np.sin(features['heading_rad'])
    features.loc[:, 'heading_cos'] = np.cos(features['heading_rad'])
    features = features.drop(columns=['heading_rad'])
    labels = df_clean[['acceleration', 'heading_change_rate']]
    labels.loc[:, 'acceleration'] = np.clip(labels['acceleration'], -4.0, 3.0)
    labels.loc[:, 'heading_change_rate'] = np.clip(labels['heading_change_rate'], -0.5, 0.5)
    logger.info(f"数据集创建完成，包含 {len(features)} 个有效样本")
    return features, labels

def train_model(X, y):
    """训练并保存运动学模型"""
    logger.info("开始使用XGBoost训练运动学模型...")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    model = xgb.XGBRegressor(
        objective='reg:squarederror',
        n_estimators=500,
        learning_rate=0.05,
        max_depth=7,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        n_jobs=-1,
        early_stopping_rounds=20
    )

    model.fit(
        X_train, y_train,
        eval_set=[(X_test, y_test)],
        verbose=False
    )
    
    score = model.score(X_test, y_test)
    logger.info(f"模型训练完成。测试集 R^2 分数: {score:.4f}")
    
    os.makedirs(os.path.dirname(MODEL_OUTPUT_PATH), exist_ok=True)
    joblib.dump(model, MODEL_OUTPUT_PATH)
    logger.info(f"模型已保存至: {MODEL_OUTPUT_PATH}")
    
    return model

def main():
    """完整训练流程"""
    logger.info("===== 开始训练生成式运动学模型 =====")
    features, labels = create_feature_dataset()
    train_model(features, labels)
    logger.info("===== 模型训练成功完成！ =====")

if __name__ == "__main__":
    main() 