#!/usr/bin/env python3
"""
下一代轨迹仿真器
基于BehaviorGPT和最新研究的改进方案
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
from dataclasses import dataclass
import math

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Config:
    """配置类"""
    # 模型参数
    hidden_dim: int = 128
    num_heads: int = 8
    num_layers: int = 4
    patch_size: int = 10  # 10个时间步为一个patch (1秒)
    max_agents: int = 32
    max_map_elements: int = 512
    
    # 训练参数
    batch_size: int = 16
    learning_rate: float = 5e-4
    num_epochs: int = 50
    
    # 仿真参数
    dt: float = 0.1  # 时间间隔
    prediction_horizon: int = 80  # 8秒预测
    num_modes: int = 6  # 多模态数量
    
    # 物理约束
    max_speed: float = 30.0
    max_acceleration: float = 4.0
    min_acceleration: float = -6.0

class RelativeSpacetimeEmbedding(nn.Module):
    """相对时空嵌入"""
    
    def __init__(self, hidden_dim):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.embed_net = nn.Sequential(
            nn.Linear(5, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, hidden_dim)
        )
    
    def forward(self, pos_i, pos_j, time_i, time_j):
        """
        计算相对位置和时间嵌入
        Args:
            pos_i, pos_j: (batch, 2) 位置坐标
            time_i, time_j: (batch,) 时间戳
        """
        # 相对距离
        delta_pos = pos_j - pos_i
        distance = torch.norm(delta_pos, dim=-1, keepdim=True)
        
        # 相对角度
        angle = torch.atan2(delta_pos[..., 1:2], delta_pos[..., 0:1])
        
        # 时间差
        delta_time = (time_j - time_i).unsqueeze(-1)
        
        # 组合特征
        rel_features = torch.cat([
            distance, angle, delta_time, 
            delta_pos
        ], dim=-1)
        
        return self.embed_net(rel_features)

class MultiModalPatchPredictor(nn.Module):
    """多模态patch预测器"""
    
    def __init__(self, hidden_dim, patch_size, num_modes):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.patch_size = patch_size
        self.num_modes = num_modes
        
        # 模式权重预测
        self.mode_predictor = nn.Linear(hidden_dim, num_modes)
        
        # 每个模式的状态预测器
        self.state_predictors = nn.ModuleList([
            nn.GRU(hidden_dim + 6, hidden_dim, batch_first=True)
            for _ in range(num_modes)
        ])
        
        # 状态输出层
        self.output_layers = nn.ModuleList([
            nn.Linear(hidden_dim, 6)  # x, y, vx, vy, ax, ay
            for _ in range(num_modes)
        ])
        
        # 不确定性预测
        self.uncertainty_layers = nn.ModuleList([
            nn.Linear(hidden_dim, 6)  # 对应的不确定性
            for _ in range(num_modes)
        ])
    
    def forward(self, features, current_state):
        """
        Args:
            features: (batch, hidden_dim) patch特征
            current_state: (batch, 6) 当前状态
        Returns:
            modes: (batch, num_modes, patch_size, 6) 多模态预测
            weights: (batch, num_modes) 模式权重
            uncertainties: (batch, num_modes, patch_size, 6) 不确定性
        """
        batch_size = features.size(0)
        
        # 预测模式权重
        mode_weights = F.softmax(self.mode_predictor(features), dim=-1)
        
        modes = []
        uncertainties = []
        
        for mode_idx in range(self.num_modes):
            # 初始化隐藏状态
            hidden = features.unsqueeze(0)  # (1, batch, hidden_dim)
            
            # 当前状态作为输入
            current_input = torch.cat([features, current_state], dim=-1)
            mode_states = []
            mode_uncertainties = []
            
            for step in range(self.patch_size):
                # GRU前向传播
                output, hidden = self.state_predictors[mode_idx](
                    current_input.unsqueeze(1), hidden
                )
                output = output.squeeze(1)
                
                # 预测下一状态
                next_state = self.output_layers[mode_idx](output)
                uncertainty = F.softplus(self.uncertainty_layers[mode_idx](output))
                
                mode_states.append(next_state)
                mode_uncertainties.append(uncertainty)
                
                # 更新输入
                current_input = torch.cat([features, next_state], dim=-1)
            
            modes.append(torch.stack(mode_states, dim=1))
            uncertainties.append(torch.stack(mode_uncertainties, dim=1))
        
        modes = torch.stack(modes, dim=1)
        uncertainties = torch.stack(uncertainties, dim=1)
        
        return modes, mode_weights, uncertainties

class TripleAttentionLayer(nn.Module):
    """三重注意力层"""
    
    def __init__(self, hidden_dim, num_heads):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        # 时序自注意力
        self.temporal_attention = nn.MultiheadAttention(
            hidden_dim, num_heads, batch_first=True
        )
        
        # 智能体-地图交叉注意力
        self.agent_map_attention = nn.MultiheadAttention(
            hidden_dim, num_heads, batch_first=True
        )
        
        # 智能体-智能体自注意力
        self.agent_agent_attention = nn.MultiheadAttention(
            hidden_dim, num_heads, batch_first=True
        )
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim * 4, hidden_dim)
        )
        
        # Layer Normalization
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        self.norm3 = nn.LayerNorm(hidden_dim)
        self.norm4 = nn.LayerNorm(hidden_dim)
        
    def forward(self, agent_features, map_features, temporal_mask=None):
        """
        Args:
            agent_features: (batch, num_agents, seq_len, hidden_dim)
            map_features: (batch, num_map_elements, hidden_dim)
            temporal_mask: 因果mask for temporal attention
        """
        batch_size, num_agents, seq_len, hidden_dim = agent_features.shape
        
        # 重塑为(batch * num_agents, seq_len, hidden_dim)
        agent_flat = agent_features.reshape(-1, seq_len, hidden_dim)
        
        # 1. 时序自注意力
        temporal_out, _ = self.temporal_attention(
            agent_flat, agent_flat, agent_flat, 
            attn_mask=temporal_mask
        )
        agent_flat = self.norm1(agent_flat + temporal_out)
        
        # 重塑回原始形状
        agent_features = agent_flat.reshape(batch_size, num_agents, seq_len, hidden_dim)
        
        # 2. 智能体-地图交叉注意力
        # 使用最后一个时间步的特征与地图交互
        last_step_features = agent_features[:, :, -1, :]  # (batch, num_agents, hidden_dim)
        last_step_flat = last_step_features.reshape(-1, 1, hidden_dim)
        
        # 扩展地图特征
        map_expanded = map_features.unsqueeze(1).repeat(1, num_agents, 1, 1)
        map_flat = map_expanded.reshape(-1, map_features.size(1), hidden_dim)
        
        map_attended, _ = self.agent_map_attention(
            last_step_flat, map_flat, map_flat
        )
        last_step_flat = self.norm2(last_step_flat + map_attended)
        
        # 更新最后一步特征
        last_step_updated = last_step_flat.reshape(batch_size, num_agents, hidden_dim)
        agent_features[:, :, -1, :] = last_step_updated
        
        # 3. 智能体-智能体自注意力
        agent_agent_features = last_step_updated.reshape(batch_size, num_agents, hidden_dim)
        agent_attended, _ = self.agent_agent_attention(
            agent_agent_features, agent_agent_features, agent_agent_features
        )
        agent_agent_features = self.norm3(agent_agent_features + agent_attended)
        
        # 4. 前馈网络
        ffn_out = self.ffn(agent_agent_features)
        output = self.norm4(agent_agent_features + ffn_out)
        
        return output

class NextGenTrajectorySimulator(nn.Module):
    """下一代轨迹仿真器"""
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        
        # 输入嵌入
        self.agent_embed = nn.Linear(6, config.hidden_dim)  # x, y, vx, vy, ax, ay
        self.map_embed = nn.Linear(4, config.hidden_dim)   # road type features
        
        # 位置编码
        self.temporal_pos_embed = nn.Parameter(
            torch.randn(config.patch_size * 20, config.hidden_dim)
        )
        
        # 相对时空嵌入
        self.spacetime_embed = RelativeSpacetimeEmbedding(config.hidden_dim)
        
        # 多层三重注意力
        self.attention_layers = nn.ModuleList([
            TripleAttentionLayer(config.hidden_dim, config.num_heads)
            for _ in range(config.num_layers)
        ])
        
        # Patch级别的预测器
        self.patch_predictor = MultiModalPatchPredictor(
            config.hidden_dim, config.patch_size, config.num_modes
        )
        
        # 驾驶行为分类器
        self.behavior_classifier = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(config.hidden_dim // 2, 4)  # 4种驾驶风格
        )
        
    def create_causal_mask(self, seq_len):
        """创建因果mask"""
        mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1)
        mask = mask.masked_fill(mask == 1, float('-inf'))
        return mask
    
    def forward(self, agent_history, map_features, predict_steps=1):
        """
        Args:
            agent_history: (batch, num_agents, seq_len, 6)
            map_features: (batch, num_map_elements, 4)
            predict_steps: 预测的patch数量
        Returns:
            predictions: (batch, num_agents, predict_steps, patch_size, 6)
            behavior_probs: (batch, num_agents, 4)
        """
        batch_size, num_agents, seq_len, _ = agent_history.shape
        
        # 1. 输入嵌入
        agent_embeds = self.agent_embed(agent_history)
        map_embeds = self.map_embed(map_features)
        
        # 2. 位置编码
        pos_embeds = self.temporal_pos_embed[:seq_len].unsqueeze(0).unsqueeze(0)
        agent_embeds = agent_embeds + pos_embeds
        
        # 3. 创建因果mask
        causal_mask = self.create_causal_mask(seq_len)
        if agent_embeds.is_cuda:
            causal_mask = causal_mask.cuda()
        
        # 4. 多层注意力处理
        for layer in self.attention_layers:
            agent_features = layer(agent_embeds, map_embeds, causal_mask)
        
        # 5. 驾驶行为分类
        behavior_probs = F.softmax(self.behavior_classifier(agent_features), dim=-1)
        
        # 6. 自回归预测
        predictions = []
        current_features = agent_features
        current_state = agent_history[:, :, -1, :]  # 最后一个状态
        
        for step in range(predict_steps):
            # 预测下一个patch
            modes, weights, uncertainties = self.patch_predictor(
                current_features.reshape(-1, self.config.hidden_dim),
                current_state.reshape(-1, 6)
            )
            
            # 重塑输出
            modes = modes.reshape(batch_size, num_agents, self.config.num_modes, 
                                self.config.patch_size, 6)
            weights = weights.reshape(batch_size, num_agents, self.config.num_modes)
            
            # 采样最可能的模式
            best_mode_idx = torch.argmax(weights, dim=-1)
            predicted_patch = torch.gather(
                modes, 2, 
                best_mode_idx.unsqueeze(-1).unsqueeze(-1).unsqueeze(-1).expand(
                    -1, -1, 1, self.config.patch_size, 6
                )
            ).squeeze(2)
            
            predictions.append(predicted_patch)
            
            # 更新当前状态为patch的最后一步
            current_state = predicted_patch[:, :, -1, :]
            
        predictions = torch.stack(predictions, dim=2)
        return predictions, behavior_probs

class TrajectoryDataset(Dataset):
    """轨迹数据集"""
    
    def __init__(self, trajectory_files, config):
        self.config = config
        self.data = self._load_and_process_data(trajectory_files)
    
    def _load_and_process_data(self, trajectory_files):
        """加载并处理轨迹数据"""
        all_data = []
        
        for file_path in trajectory_files:
            if Path(file_path).exists():
                df = pd.read_csv(file_path)
                
                # 计算速度和加速度
                df['speed'] = df['calculated_speed_ms'].fillna(0)
                df['vx'] = df['velocity_east_ms'].fillna(0)
                df['vy'] = df['velocity_north_ms'].fillna(0)
                df['ax'] = df['acceleration_x_ms2'].fillna(0)
                df['ay'] = df['acceleration_y_ms2'].fillna(0)
                
                # 使用latitude/longitude作为位置（实际为UTM坐标）
                df['x'] = df['latitude']
                df['y'] = df['longitude']
                
                # 创建sequence
                sequence_length = len(df)
                for i in range(20, sequence_length - self.config.patch_size):
                    # 历史窗口
                    history = df.iloc[i-20:i][['x', 'y', 'vx', 'vy', 'ax', 'ay']].values
                    # 未来patch
                    future = df.iloc[i:i+self.config.patch_size][['x', 'y', 'vx', 'vy', 'ax', 'ay']].values
                    
                    all_data.append({
                        'history': history,
                        'future': future,
                        'current_state': history[-1]
                    })
        
        logger.info(f"加载了 {len(all_data)} 个训练样本")
        return all_data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        return {
            'history': torch.FloatTensor(sample['history']),
            'future': torch.FloatTensor(sample['future']),
            'current_state': torch.FloatTensor(sample['current_state'])
        }

class NextGenSimulatorTrainer:
    """训练器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        self.model = NextGenTrajectorySimulator(config).to(self.device)
        
        # 优化器
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(), 
            lr=config.learning_rate,
            weight_decay=0.01
        )
        
        # 损失函数
        self.trajectory_loss = nn.MSELoss()
        self.behavior_loss = nn.CrossEntropyLoss()
        
    def train_epoch(self, dataloader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        
        for batch_idx, batch in enumerate(dataloader):
            # 准备数据
            history = batch['history'].to(self.device)
            future = batch['future'].to(self.device)
            
            # 添加batch维度和agent维度
            history = history.unsqueeze(1)  # (batch, 1, seq_len, 6)
            future = future.unsqueeze(1)    # (batch, 1, patch_size, 6)
            
            # 虚拟地图特征
            map_features = torch.randn(history.size(0), 50, 4).to(self.device)
            
            # 前向传播
            predictions, behavior_probs = self.model(history, map_features, predict_steps=1)
            
            # 计算损失
            traj_loss = self.trajectory_loss(predictions.squeeze(2), future)
            
            # 行为分类损失（使用虚拟标签）
            behavior_labels = torch.randint(0, 4, (history.size(0), 1)).to(self.device)
            behavior_loss = self.behavior_loss(
                behavior_probs.squeeze(1), 
                behavior_labels.squeeze(1)
            )
            
            total_loss_batch = traj_loss + 0.1 * behavior_loss
            
            # 反向传播
            self.optimizer.zero_grad()
            total_loss_batch.backward()
            self.optimizer.step()
            
            total_loss += total_loss_batch.item()
            
            if batch_idx % 10 == 0:
                logger.info(f"Batch {batch_idx}, Loss: {total_loss_batch.item():.4f}")
        
        return total_loss / len(dataloader)
    
    def train(self, trajectory_files):
        """完整训练过程"""
        # 创建数据集
        dataset = TrajectoryDataset(trajectory_files, self.config)
        dataloader = DataLoader(
            dataset, 
            batch_size=self.config.batch_size, 
            shuffle=True,
            num_workers=4
        )
        
        logger.info("开始训练下一代轨迹仿真器...")
        
        for epoch in range(self.config.num_epochs):
            avg_loss = self.train_epoch(dataloader)
            logger.info(f"Epoch {epoch+1}/{self.config.num_epochs}, Average Loss: {avg_loss:.4f}")
            
            # 保存模型
            if (epoch + 1) % 10 == 0:
                self.save_model(f"next_gen_simulator_epoch_{epoch+1}.pth")
        
        logger.info("训练完成!")
    
    def save_model(self, filename):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config
        }, filename)
        logger.info(f"模型已保存: {filename}")

def main():
    """主函数"""
    # 配置
    config = Config()
    
    # 训练数据
    trajectory_files = [
        "trajectory_generator/data/trajectories/trajectory_1.csv",
        "trajectory_generator/data/trajectories/trajectory_2.csv",
        "trajectory_generator/data/trajectories/trajectory_3.csv"
    ]
    
    # 创建训练器
    trainer = NextGenSimulatorTrainer(config)
    
    # 开始训练
    trainer.train(trajectory_files)

if __name__ == "__main__":
    main() 