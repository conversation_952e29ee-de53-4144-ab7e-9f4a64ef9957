#!/usr/bin/env python3
"""
可视化路径引导仿真的结果
- 对比真实轨迹与AI生成的轨迹
- 以DEM地形图为背景
"""

import pandas as pd
import matplotlib.pyplot as plt
import rasterio
from rasterio.plot import show
import os

# --- 配置 ---
SIMULATED_TRAJ_FILE = "results/path_guided_simulation/guided_trajectory.csv"
REAL_TRAJ_FILE = 'trajectory_generator/data/trajectories/trajectory_1.csv'
DEM_FILE = 'trajectory_generator/data/environment/dem_aligned.tif'
OUTPUT_IMAGE_FILE = "results/path_guided_simulation/comparison.png"

# 中文字体设置
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def visualize_trajectories():
    """主可视化函数"""
    print("开始生成轨迹对比图...")

    # --- 1. 数据加载 ---
    try:
        sim_df = pd.read_csv(SIMULATED_TRAJ_FILE)
        real_df = pd.read_csv(REAL_TRAJ_FILE)
        dem_src = rasterio.open(DEM_FILE)
    except FileNotFoundError as e:
        print(f"错误：无法找到必要的文件: {e.filename}。请先确保仿真已成功运行。")
        return

    # 从真实轨迹中提取路标点（与仿真器逻辑保持一致）
    waypoint_interval = 50
    waypoints = real_df[['longitude', 'latitude']].iloc[::waypoint_interval]

    # --- 2. 绘图 ---
    fig, ax = plt.subplots(figsize=(14, 12))

    # 绘制DEM背景
    show(dem_src, ax=ax, cmap='terrain', title="") # 使用内置terrain配色，标题留空后续自定义
    
    # 绘制真实轨迹
    ax.plot(real_df['longitude'], real_df['latitude'], 'k--', linewidth=2, label='真实轨迹')
    
    # 绘制AI仿真轨迹
    if not sim_df.empty:
        ax.plot(sim_df['x'], sim_df['y'], 'r-', linewidth=2.5, label='AI仿真轨迹')

    # 绘制路标点
    ax.scatter(waypoints['longitude'], waypoints['latitude'], c='blue', s=80, marker='X', edgecolors='k', zorder=10, label='导航路标点')

    # --- 3. 样式设置 ---
    ax.set_title('AI仿真轨迹 vs. 真实轨迹 (XGBoost模型)', fontsize=22)
    ax.set_xlabel('UTM 东坐标 (米)', fontsize=20)
    ax.set_ylabel('UTM 北坐标 (米)', fontsize=20)
    ax.tick_params(axis='both', which='major', labelsize=16)
    
    legend = ax.legend(fontsize=18, loc='upper left')
    plt.setp(legend.get_texts(), color='k')

    ax.grid(True, linestyle=':', alpha=0.6)
    fig.tight_layout()

    # --- 4. 保存图像 ---
    os.makedirs(os.path.dirname(OUTPUT_IMAGE_FILE), exist_ok=True)
    plt.savefig(OUTPUT_IMAGE_FILE, dpi=200)
    print(f"对比图已保存至: {OUTPUT_IMAGE_FILE}")
    plt.close(fig) # 关闭图像，释放内存

if __name__ == "__main__":
    visualize_trajectories() 