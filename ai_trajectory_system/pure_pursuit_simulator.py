import os
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import joblib
import logging
from scipy.spatial import cKDTree
import matplotlib.pyplot as plt

# --- 基本设置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 静态配置 ---
class Config:
    """存放所有静态配置，方便统一管理"""
    # 文件路径
    REAL_TRAJECTORY_FILEPATH = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    DATA_PATH_PREFIX = 'trajectory_generator/data/environment/'

    # 特征列
    ENVIRONMENT_FEATURES = ['dem', 'slope', 'aspect', 'landcover']
    STATE_FEATURES = ['speed']
    # 引导特征不再直接用于转向，但模型训练时需要它们作为输入
    GUIDANCE_FEATURES = ['distance_to_target', 'angle_to_target']
    
    # 模型和仿真参数
    MODEL_FILENAME = "target_speed_model_rf.pkl" # 使用新的模型文件名
    SIMULATION_DT = 0.5  # 仿真步长时间 (秒)
    MAX_SIM_STEPS = 800

    # 物理约束
    MAX_SPEED = 20.0  # m/s
    ACCELERATION_RANGE = (-3.0, 2.5)  # (min_acel, max_acel) in m/s^2
    HEADING_CHANGE_RATE_RANGE = (-np.pi / 4, np.pi / 4)  # (min_rate, max_rate) in rad/s
    WHEELBASE = 2.5  # 车辆轴距 (m)，用于纯跟踪算法

    # 纯跟踪控制器参数 (最终调优)
    LOOKAHEAD_GAIN = 0.5      # 前瞻距离的速度增益 (k)
    MIN_LOOKAHEAD = 5.0       # 最小前瞻距离 (m)
    MAX_LOOKAHEAD = 28.0      # 最大前瞻距离 (m)

# --- 环境数据模型 ---
class Environment:
    """负责加载和查询所有环境栅格数据"""
    def __init__(self, data_path_prefix):
        self.data_sources = {}
        for name in Config.ENVIRONMENT_FEATURES:
        try:
                filepath = os.path.join(data_path_prefix, f"{name}_aligned.tif")
                if os.path.exists(filepath):
                    self._load_raster(name, filepath)
        else:
                    logging.warning(f"警告: 环境数据文件未找到: {filepath}")
        except Exception as e:
            logging.error(f"加载环境数据时出错: {e}", exc_info=True)
            raise

    def _load_raster(self, name, filepath):
        import rasterio
        raster = rasterio.open(filepath)
        self.data_sources[name] = {'raster': raster, 'transform': raster.transform}
        logging.info(f"成功加载环境数据: {name}")

    def get_features_at_point(self, x, y):
        features = {}
        for name, source in self.data_sources.items():
            try:
                row, col = source['raster'].index(x, y)
                value = source['raster'].read(1, window=((row, row+1), (col, col+1)))
                features[name] = value[0, 0] if value.size > 0 else 0
            except IndexError:
                features[name] = 0
        return features

# --- AI模型: 目标速度预测 ---
class TargetSpeedModel:
    """基于随机森林的模型，用于预测目标速度"""
    def __init__(self, model_path=Config.MODEL_FILENAME):
        self.model_path = model_path
        self.model = None
        self.feature_names = Config.ENVIRONMENT_FEATURES + Config.STATE_FEATURES + Config.GUIDANCE_FEATURES

    def create_features_and_labels(self, df):
        logging.info("为目标速度模型创建特征和标签...")
        X = df[self.feature_names]
        y = df['speed'].shift(-1)
        X, y = X.iloc[:-1], y.iloc[:-1]
        logging.info(f"特征和标签创建完成, X shape: {X.shape}")
        return X, y

    def train(self, X, y, force_retrain=False):
        if not force_retrain and os.path.exists(self.model_path):
            self.model = joblib.load(self.model_path)
            logging.info(f"从 {self.model_path} 加载已训练的模型。")
            return
        logging.info("训练新的目标速度模型 (RandomForest)...")
        self.model = RandomForestRegressor(
            n_estimators=100, max_depth=10, min_samples_split=5, 
            min_samples_leaf=2, random_state=42, n_jobs=-1
        )
        self.model.fit(X, y)
        joblib.dump(self.model, self.model_path)
        logging.info(f"目标速度模型训练完成并保存到 {self.model_path}")

    def predict(self, features_df):
        if self.model is None: raise RuntimeError("模型尚未训练或加载。")
        return self.model.predict(features_df[self.feature_names])

# --- PID控制器 ---
class PIDController:
    """一个简单的PID控制器"""
    def __init__(self, Kp, Ki, Kd, output_limits, integral_limits):
        self.Kp, self.Ki, self.Kd = Kp, Ki, Kd
        self.output_limits = output_limits
        self.integral_limits = integral_limits
        self._integral = 0
        self._previous_error = 0

    def update(self, setpoint, current_value, dt):
        error = setpoint - current_value
        self._integral += error * dt
        self._integral = np.clip(self._integral, self.integral_limits[0], self.integral_limits[1])
        derivative = (error - self._previous_error) / dt
        self._previous_error = error
        output = self.Kp * error + self.Ki * self._integral + self.Kd * derivative
        return np.clip(output, self.output_limits[0], self.output_limits[1])

# --- 路径跟踪控制器 ---
class PurePursuitController:
    """纯跟踪路径跟踪控制器"""
    def __init__(self, guide_path, wheelbase, lookahead_gain, min_lookahead, max_lookahead):
        self.path = guide_path
        self.kdtree = cKDTree(self.path)
        self.wheelbase = wheelbase
        self.k = lookahead_gain
        self.L_min = min_lookahead
        self.L_max = max_lookahead

    def get_target_heading_rate(self, x, y, heading, speed):
        Ld = np.clip(self.k * speed, self.L_min, self.L_max)
        _, closest_idx = self.kdtree.query([x, y], k=1)
        
        target_idx = closest_idx
        while target_idx < len(self.path) - 1:
            dist_to_target = np.hypot(self.path[target_idx][0] - x, self.path[target_idx][1] - y)
            if dist_to_target >= Ld:
                break
            target_idx += 1
        
        target_x, target_y = self.path[target_idx]
        alpha = np.arctan2(target_y - y, target_x - x) - heading
        alpha = (alpha + np.pi) % (2 * np.pi) - np.pi
        
        if speed < 0.1: return 0.0, (target_x, target_y)
            
        heading_change_rate = speed * (2 * np.sin(alpha)) / Ld
        return heading_change_rate, (target_x, target_y)

# --- 主仿真器: AI+PurePursuit ---
class PurePursuitSimulator:
    """使用AI预测目标速度，纯跟踪算法进行路径跟踪的仿真器"""
    def __init__(self, ai_model, environment, guide_path_df):
        logging.info("基于纯跟踪的智能轨迹仿真器初始化...")
        self.ai_model = ai_model
        self.environment = environment
        self.guide_path = guide_path_df[['x', 'y']].values
        
        # 最终PID参数，在响应和平滑之间取得平衡
        self.speed_pid = PIDController(Kp=0.25, Ki=0.04, Kd=0.1, output_limits=Config.ACCELERATION_RANGE, integral_limits=(-3.5, 3.5))
        self.path_tracker = PurePursuitController(
            guide_path=self.guide_path, wheelbase=Config.WHEELBASE, lookahead_gain=Config.LOOKAHEAD_GAIN,
            min_lookahead=Config.MIN_LOOKAHEAD, max_lookahead=Config.MAX_LOOKAHEAD
        )
        self.state = {}
        self.simulation_results = []
        logging.info("纯跟踪仿真器初始化完成。")

    def set_initial_state(self, x, y, speed, heading_deg):
        self.state = {'x': x, 'y': y, 'speed': speed, 'heading': np.deg2rad(heading_deg)}
        self.simulation_results = [self.state.copy()]
        logging.info(f"设置初始状态: 位置({x:.1f}, {y:.1f}), 速度={speed:.3f} m/s, 航向={heading_deg:.1f}°")

    def _get_current_features(self):
        """为当前状态准备AI模型所需的特征"""
        x, y = self.state['x'], self.state['y']
        env_features = self.environment.get_features_at_point(x, y)
        
        # 为了与训练时保持一致，需要计算到引导路径的距离和角度
        _, target_idx = self.path_tracker.kdtree.query([x, y], k=1)
        next_target_idx = min(target_idx + 5, len(self.guide_path) - 1)
        target_x, target_y = self.guide_path[next_target_idx]
        
        dist = np.hypot(target_x - x, target_y - y)
        angle = np.arctan2(target_y - y, target_x - x)

        features = {**env_features, 'speed': self.state['speed'], 'distance_to_target': dist, 'angle_to_target': angle}
        return features

    def _simulation_step(self):
        # 1. AI预测目标速度
        features_dict = self._get_current_features()
        features_df = pd.DataFrame([features_dict], columns=self.ai_model.feature_names)
        target_speed = self.ai_model.predict(features_df)[0]
        target_speed = np.clip(target_speed, 0, Config.MAX_SPEED)
        
        # 2. PID计算加速度
        acceleration = self.speed_pid.update(target_speed, self.state['speed'], Config.SIMULATION_DT)
        
        # 3. 纯跟踪计算航向变化率
        current_speed = self.state['speed'] if self.state['speed'] > 0.1 else 0.1
        heading_change_rate, target_point = self.path_tracker.get_target_heading_rate(
            self.state['x'], self.state['y'], self.state['heading'], current_speed)
        heading_change_rate = np.clip(heading_change_rate, *Config.HEADING_CHANGE_RATE_RANGE)

        # 4. 更新状态
        speed, heading = self.state['speed'], self.state['heading']
        new_speed = np.clip(speed + acceleration * Config.SIMULATION_DT, 0, Config.MAX_SPEED)
        new_heading = (heading + heading_change_rate * Config.SIMULATION_DT + np.pi) % (2 * np.pi) - np.pi
        avg_speed = (speed + new_speed) / 2
        
        self.state.update({
            'x': self.state['x'] + avg_speed * np.cos(new_heading) * Config.SIMULATION_DT,
            'y': self.state['y'] + avg_speed * np.sin(new_heading) * Config.SIMULATION_DT,
            'speed': new_speed, 'heading': new_heading, 'target_speed': target_speed,
            'acceleration': acceleration, 'heading_change_rate': heading_change_rate,
            'target_x': target_point[0], 'target_y': target_point[1]
        })

    def run_simulation(self, max_steps=Config.MAX_SIM_STEPS):
        logging.info(f"开始纯跟踪驱动仿真 (最大步数: {max_steps})")
        for i in range(max_steps):
            try:
            self._simulation_step()
            self.simulation_results.append(self.state.copy())
            if (i + 1) % 100 == 0:
                    logging.info(f"步数: {i+1}, Vel:{self.state['speed']:.2f} m/s, Accel:{self.state['acceleration']:.2f} m/s², Rate: {self.state['heading_change_rate']:.2f} rad/s")
            except Exception as e:
                logging.error(f"仿真在步骤 {i+1} 出错: {e}", exc_info=True)
                break
        logging.info(f"仿真完成: 总步数 {len(self.simulation_results) - 1}")
        return pd.DataFrame(self.simulation_results)

# --- 数据处理 ---
def prepare_data(filepath, environment):
    logging.info(f"开始预处理轨迹数据: {filepath}")
    df = pd.read_csv(filepath)
    df.rename(columns={'latitude': 'x', 'longitude': 'y', 'timestamp_ms': 'time'}, inplace=True)
    
    df['dt'] = df['time'].diff().fillna(500) / 1000.0
    
    # 修改1: 先不填充无效值，让速度和加速度的第一个值为NaN
    df['speed'] = np.hypot(df['x'].diff(), df['y'].diff()) / df['dt']
    df['acceleration'] = df['speed'].diff() / df['dt']
    df['heading'] = np.arctan2(df['y'].diff(), df['x'].diff())
    
    # 修改2: 使用 'bfill' (backfill) 来填充所有列的NaN值
    # 这会用下一个有效值来填充当前无效值，比直接用0更合理
    df.fillna(method='bfill', inplace=True)
    
    env_features = df.apply(lambda row: environment.get_features_at_point(row['x'], row['y']), axis=1, result_type='expand')
    df = pd.concat([df, env_features], axis=1)

    guide_path = df[['x', 'y']].values
    kdtree = cKDTree(guide_path)
    
    def add_guidance(row, i):
        target_idx = min(i + 5, len(guide_path) - 1)
        target_pos = guide_path[target_idx]
        dist = np.hypot(target_pos[0] - row['x'], target_pos[1] - row['y'])
        angle = np.arctan2(target_pos[1] - row['y'], target_pos[0] - row['x'])
        return pd.Series([dist, angle], index=['distance_to_target', 'angle_to_target'])

    guidance_df = pd.concat([df.iloc[i].pipe(add_guidance, i) for i in range(len(df))], axis=1).T
    return pd.concat([df, guidance_df], axis=1).dropna()

# --- 可视化 ---
def visualize_comprehensive_results(real_df, sim_df, output_dir):
    logging.info(f"正在生成综合分析图表...")
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.rcParams['font.family'] = 'sans-serif'  # 优先使用无衬线字体
    
    # --- 数据对齐 ---
    # 计算仿真总时长
    sim_duration = (len(sim_df) - 1) * Config.SIMULATION_DT
    # 计算真实轨迹每一步的时间戳 (s)
    real_time_series = (real_df['time'] - real_df['time'].iloc[0]) / 1000.0
    # 找到与仿真时长匹配的真实轨迹数据
    real_df_aligned = real_df[real_time_series <= sim_duration].copy()
    real_time_aligned = real_time_series[real_time_series <= sim_duration]
    
    fig, axs = plt.subplots(3, 1, figsize=(18, 30), gridspec_kw={'height_ratios': [4, 1, 1]})

    # 轨迹对比图
    ax = axs[0]
    ax.plot(real_df_aligned['x'], real_df_aligned['y'], 'b-', label='真实轨迹 (对齐)', linewidth=3, alpha=0.7)
    ax.plot(sim_df['x'], sim_df['y'], 'r--', label='仿真轨迹 (纯跟踪)', linewidth=2.5, alpha=0.9)
    if 'target_x' in sim_df.columns:
        ax.plot(sim_df['target_x'], sim_df['target_y'], 'g.', markersize=2, label='前瞻点', alpha=0.4)
    ax.plot(real_df_aligned['x'].iloc[0], real_df_aligned['y'].iloc[0], 'go', markersize=15, label='起点', alpha=0.8)
    ax.plot(sim_df['x'].iloc[-1], sim_df['y'].iloc[-1], 'rx', markersize=15, mew=3, label='仿真终点', alpha=0.8)
    ax.set_title('真实轨迹 vs. AI+纯跟踪 仿真轨迹对比', fontsize=22, pad=20)
    ax.set_xlabel('UTM X 坐标 (m)', fontsize=20)
    ax.set_ylabel('UTM Y 坐标 (m)', fontsize=20)
    ax.legend(fontsize=20)
    ax.grid(True)
    ax.set_aspect('equal', adjustable='box')
    ax.tick_params(labelsize=14)

    # 速度 vs. 时间
    ax = axs[1]
    sim_time = np.arange(len(sim_df)) * Config.SIMULATION_DT
    ax.plot(real_time_aligned, real_df_aligned['speed'], 'b-', label='真实速度', alpha=0.7)
    ax.plot(sim_time, sim_df['speed'], 'r--', label='仿真速度', alpha=0.9)
    ax.plot(sim_time, sim_df['target_speed'], 'g:', label='AI目标速度', alpha=0.8, linewidth=2.5)
    ax.set_title('速度变化曲线', fontsize=22, pad=20)
    ax.set_xlabel('时间 (s)', fontsize=20)
    ax.set_ylabel('速度 (m/s)', fontsize=20)
    ax.legend(fontsize=20)
    ax.grid(True)
    ax.tick_params(labelsize=14)
    ax.set_xlim(left=0)

    # 加速度 vs. 时间
    ax = axs[2]
    ax.plot(real_time_aligned, real_df_aligned['acceleration'].clip(*Config.ACCELERATION_RANGE), 'b-', label='真实加速度', alpha=0.7)
    ax.plot(sim_time, sim_df['acceleration'], 'r--', label='仿真加速度', alpha=0.9)
    ax.set_title('加速度变化曲线', fontsize=22, pad=20)
    ax.set_xlabel('时间 (s)', fontsize=20)
    ax.set_ylabel('加速度 (m/s²)', fontsize=20)
    ax.legend(fontsize=20)
    ax.grid(True)
    ax.tick_params(labelsize=14)
    ax.set_xlim(left=0)
    ax.set_ylim(Config.ACCELERATION_RANGE[0] - 1, Config.ACCELERATION_RANGE[1] + 1)

    fig.tight_layout(pad=3.0)
    save_path = os.path.join(output_dir, "pure_pursuit_analysis_aligned.png") # 新文件名
    plt.savefig(save_path, dpi=300)
    plt.close(fig)
    logging.info(f"对齐后的综合分析图表已保存到: {save_path}")

# --- 主执行流程 ---
def main():
    environment = Environment(Config.DATA_PATH_PREFIX)
    ai_model = TargetSpeedModel()
    
    full_data_df = prepare_data(Config.REAL_TRAJECTORY_FILEPATH, environment)
    
    X, y = ai_model.create_features_and_labels(full_data_df)
    ai_model.train(X, y, force_retrain=True)

    simulator = PurePursuitSimulator(ai_model, environment, full_data_df)
    
    # 只运行与真实初始状态最相关的场景
    scenario_name = "real_initial"
    params = {
        "x": full_data_df['x'].iloc[0], 
        "y": full_data_df['y'].iloc[0], 
        "speed": full_data_df['speed'].iloc[0], 
        "heading": np.rad2deg(full_data_df['heading'].iloc[0])
    }
    
    logging.info(f"\n--- 运行指定场景: {scenario_name} ---")
    output_dir = os.path.join("results", f"pure_pursuit_final_tune_{scenario_name}") # 最终调优输出目录
        os.makedirs(output_dir, exist_ok=True)
            
    simulator.set_initial_state(params['x'], params['y'], params['speed'], params['heading'])
        sim_results_df = simulator.run_simulation()
        
    if not sim_results_df.empty:
        sim_results_df.to_csv(os.path.join(output_dir, "trajectory.csv"), index=False)
        visualize_comprehensive_results(full_data_df, sim_results_df, output_dir)
    
    logging.info("\n指定仿真场景测试完成！")

if __name__ == "__main__":
    main() 