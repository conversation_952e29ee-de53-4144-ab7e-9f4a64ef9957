#!/usr/bin/env python3
"""
轨迹对比分析总结报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
from scipy.stats import pearsonr
import os

def load_comparison_data():
    """加载对比数据"""
    results_dir = "ai_trajectory_system/results"
    
    data = {}
    for i in [1, 2]:
        real_file = f"{results_dir}/real_trajectory_{i}_comparison.csv"
        sim_file = f"{results_dir}/sim_trajectory_{i}_comparison.csv"
        
        if os.path.exists(real_file) and os.path.exists(sim_file):
            data[f'trajectory_{i}'] = {
                'real': pd.read_csv(real_file),
                'sim': pd.read_csv(sim_file)
            }
    
    return data

def calculate_detailed_metrics(real_df, sim_df):
    """计算详细的对比指标"""
    metrics = {}
    
    # 速度对比
    speed_real = real_df['velocity_2d_ms']
    speed_sim = sim_df['velocity_2d_ms']
    
    metrics['speed'] = {
        'rmse': np.sqrt(np.mean((speed_real - speed_sim)**2)),
        'mae': np.mean(np.abs(speed_real - speed_sim)),
        'correlation': pearsonr(speed_real, speed_sim)[0],
        'real_mean': speed_real.mean(),
        'real_std': speed_real.std(),
        'sim_mean': speed_sim.mean(),
        'sim_std': speed_sim.std(),
        'real_max': speed_real.max(),
        'sim_max': speed_sim.max()
    }
    
    # 航向对比
    heading_real = real_df['heading_deg']
    heading_sim = sim_df['heading_deg']
    
    # 处理角度差异
    heading_diff = np.abs(heading_real - heading_sim)
    heading_diff = np.minimum(heading_diff, 360 - heading_diff)
    
    metrics['heading'] = {
        'mae': heading_diff.mean(),
        'rmse': np.sqrt(np.mean(heading_diff**2)),
        'max_error': heading_diff.max(),
        'correlation': pearsonr(heading_real, heading_sim)[0]
    }
    
    # 轨迹长度和时间
    metrics['trajectory'] = {
        'real_length': len(real_df),
        'sim_length': len(sim_df),
        'real_duration': real_df['time_s'].max(),
        'sim_duration': sim_df['time_s'].max()
    }
    
    return metrics

def create_summary_visualization(data):
    """创建总结可视化"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 对比指标汇总
    trajectory_names = list(data.keys())
    speed_rmse = []
    speed_corr = []
    heading_mae = []
    
    for traj_name in trajectory_names:
        real_df = data[traj_name]['real']
        sim_df = data[traj_name]['sim']
        metrics = calculate_detailed_metrics(real_df, sim_df)
        
        speed_rmse.append(metrics['speed']['rmse'])
        speed_corr.append(metrics['speed']['correlation'])
        heading_mae.append(metrics['heading']['mae'])
    
    # 1. 速度RMSE对比
    axes[0, 0].bar(trajectory_names, speed_rmse, color=['skyblue', 'lightgreen'])
    axes[0, 0].set_title('速度RMSE对比', fontsize=16, fontweight='bold')
    axes[0, 0].set_ylabel('RMSE (m/s)', fontsize=14)
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 速度相关性对比
    axes[0, 1].bar(trajectory_names, speed_corr, color=['orange', 'pink'])
    axes[0, 1].set_title('速度相关性对比', fontsize=16, fontweight='bold')
    axes[0, 1].set_ylabel('相关系数', fontsize=14)
    axes[0, 1].set_ylim(-1, 1)
    axes[0, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 航向误差对比
    axes[0, 2].bar(trajectory_names, heading_mae, color=['purple', 'brown'])
    axes[0, 2].set_title('航向平均误差对比', fontsize=16, fontweight='bold')
    axes[0, 2].set_ylabel('MAE (度)', fontsize=14)
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4-6. 具体轨迹对比
    for i, traj_name in enumerate(trajectory_names):
        real_df = data[traj_name]['real']
        sim_df = data[traj_name]['sim']
        
        # 速度时间序列
        ax = axes[1, i]
        ax.plot(real_df['time_s'], real_df['velocity_2d_ms'], 'b-', 
                linewidth=2, label='真实速度', alpha=0.8)
        ax.plot(sim_df['time_s'], sim_df['velocity_2d_ms'], 'r--', 
                linewidth=2, label='仿真速度', alpha=0.8)
        ax.set_xlabel('时间 (s)', fontsize=12)
        ax.set_ylabel('速度 (m/s)', fontsize=12)
        ax.set_title(f'{traj_name}速度对比', fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 总体对比表格
    if len(trajectory_names) < 3:
        ax = axes[1, 2]
        ax.axis('off')
        
        # 创建对比表格
        table_data = []
        for traj_name in trajectory_names:
            real_df = data[traj_name]['real']
            sim_df = data[traj_name]['sim']
            metrics = calculate_detailed_metrics(real_df, sim_df)
            
            table_data.append([
                traj_name,
                f"{metrics['speed']['rmse']:.2f}",
                f"{metrics['speed']['correlation']:.3f}",
                f"{metrics['heading']['mae']:.1f}°",
                f"{metrics['trajectory']['real_duration']:.0f}s"
            ])
        
        table = ax.table(
            cellText=table_data,
            colLabels=['轨迹', '速度RMSE', '速度相关性', '航向MAE', '持续时间'],
            cellLoc='center',
            loc='center'
        )
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1.2, 1.5)
        ax.set_title('对比指标总览', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('ai_trajectory_system/results/comparison_summary.png', dpi=300, bbox_inches='tight')
    print("📊 总结图表已保存: ai_trajectory_system/results/comparison_summary.png")
    
    return fig

def print_comprehensive_report(data):
    """打印综合对比报告"""
    print("🎯 AI轨迹生成系统 vs 真实轨迹对比报告")
    print("=" * 80)
    
    for traj_name, traj_data in data.items():
        real_df = traj_data['real']
        sim_df = traj_data['sim']
        metrics = calculate_detailed_metrics(real_df, sim_df)
        
        print(f"\n📊 {traj_name.upper()} 分析:")
        print("-" * 50)
        
        print("🏃 速度性能:")
        print(f"   • 真实速度: {metrics['speed']['real_mean']:.2f}±{metrics['speed']['real_std']:.2f} m/s")
        print(f"   • 仿真速度: {metrics['speed']['sim_mean']:.2f}±{metrics['speed']['sim_std']:.2f} m/s")
        print(f"   • 速度RMSE: {metrics['speed']['rmse']:.2f} m/s")
        print(f"   • 速度相关性: {metrics['speed']['correlation']:.3f}")
        
        print("\n🧭 航向性能:")
        print(f"   • 航向MAE: {metrics['heading']['mae']:.1f}°")
        print(f"   • 航向RMSE: {metrics['heading']['rmse']:.1f}°")
        print(f"   • 航向相关性: {metrics['heading']['correlation']:.3f}")
        
        print("\n⏱️ 时间特性:")
        print(f"   • 真实轨迹时长: {metrics['trajectory']['real_duration']:.0f} 秒")
        print(f"   • 仿真轨迹时长: {metrics['trajectory']['sim_duration']:.0f} 秒")
        print(f"   • 数据点数: 真实{metrics['trajectory']['real_length']} vs 仿真{metrics['trajectory']['sim_length']}")
    
    print("\n🎯 系统性能评估:")
    print("-" * 50)
    
    all_speed_rmse = []
    all_speed_corr = []
    all_heading_mae = []
    
    for traj_name, traj_data in data.items():
        real_df = traj_data['real']
        sim_df = traj_data['sim']
        metrics = calculate_detailed_metrics(real_df, sim_df)
        
        all_speed_rmse.append(metrics['speed']['rmse'])
        all_speed_corr.append(metrics['speed']['correlation'])
        all_heading_mae.append(metrics['heading']['mae'])
    
    print(f"平均速度RMSE: {np.mean(all_speed_rmse):.2f} m/s")
    print(f"平均速度相关性: {np.mean(all_speed_corr):.3f}")
    print(f"平均航向误差: {np.mean(all_heading_mae):.1f}°")
    
    # 性能等级评估
    avg_speed_corr = np.mean(all_speed_corr)
    avg_speed_rmse = np.mean(all_speed_rmse)
    
    print("\n📈 系统性能等级:")
    if avg_speed_corr > 0.7 and avg_speed_rmse < 2.0:
        grade = "优秀 🌟🌟🌟"
    elif avg_speed_corr > 0.5 and avg_speed_rmse < 3.0:
        grade = "良好 🌟🌟"
    elif avg_speed_corr > 0.3 and avg_speed_rmse < 4.0:
        grade = "一般 🌟"
    else:
        grade = "需要改进 ⚠️"
    
    print(f"   {grade}")
    
    print("\n🚀 与已有分析的对比优势:")
    print("-" * 50)
    print("✅ AI系统特色:")
    print("   • 实时控制预测：基于Transformer的智能控制")
    print("   • 物理约束保证：自行车模型确保轨迹可行性") 
    print("   • 多模态融合：历史+环境+路径+车辆特征")
    print("   • 概率控制输出：混合密度网络提供不确定性")
    print("   • 可扩展架构：支持不同车辆类型和环境")
    
    print("\n📊 vs 窗口相关性分析:")
    print("   • 时间精度：0.5秒 vs 1-15秒窗口")
    print("   • 生成能力：新轨迹生成 vs 统计分析")
    print("   • 物理约束：完整运动学模型 vs 数据驱动")
    print("   • 实时性：支持在线仿真 vs 离线分析")

def main():
    """主函数"""
    print("🔍 加载轨迹对比数据...")
    
    data = load_comparison_data()
    
    if not data:
        print("❌ 未找到对比数据文件")
        return
    
    print(f"✅ 找到 {len(data)} 条轨迹的对比数据")
    
    # 创建总结可视化
    create_summary_visualization(data)
    
    # 打印详细报告
    print_comprehensive_report(data)
    
    print("\n🎉 轨迹对比分析完成！")
    print("📁 生成的文件:")
    print("   📊 comparison_summary.png - 总结图表")
    print("   📄 trajectory_*_comparison.png - 详细对比图")
    print("   📈 real_trajectory_*.csv - 真实轨迹数据")
    print("   📈 sim_trajectory_*.csv - 仿真轨迹数据")

if __name__ == "__main__":
    main() 