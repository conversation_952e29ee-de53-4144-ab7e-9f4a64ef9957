#!/usr/bin/env python3
"""
路径引导下的生成式轨迹仿真器
- 输入:
    - 一个训练好的运动模型 (motion_model.pkl)
    - 一个初始状态 (x, y, speed, heading)
    - 一个路标点列表 [(x1, y1), (x2, y2), ...]
- 输出:
    - 一条仿真的轨迹 (pandas DataFrame)
"""
import os
import joblib
import numpy as np
import pandas as pd
import rasterio
import logging
from collections import defaultdict

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 常量配置 ---
ENV_DATA_DIR = 'trajectory_generator/data/environment'
MODEL_PATH = "ai_trajectory_system/motion_model.pkl"
RESULTS_DIR = "results/path_guided_simulation"

# --- 环境数据加载 ---
class Environment:
    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.rasters = {}
        self.load_rasters()

    def load_rasters(self):
        logger.info(f"从 '{self.data_dir}' 加载环境栅格数据...")
        env_files = {
            'slope': os.path.join(self.data_dir, 'slope_aligned.tif'),
            'aspect': os.path.join(self.data_dir, 'aspect_aligned.tif'),
            'landcover': os.path.join(self.data_dir, 'landcover_aligned.tif'),
            'dem': os.path.join(self.data_dir, 'dem_aligned.tif')
        }
        for name, path in env_files.items():
            try:
                self.rasters[name] = rasterio.open(path)
                logger.info(f"  - 已加载: {name}")
            except rasterio.errors.RasterioIOError:
                logger.warning(f"  - 警告: 未找到或无法读取栅格文件 {path}")
                self.rasters[name] = None
    
    def get_features_at_coord(self, lon, lat):
        features = {}
        for name, src in self.rasters.items():
            if src:
                try:
                    # rasterio.sample需要一个可迭代的坐标对
                    value = next(src.sample([(lon, lat)]))[0]
                    features[name] = value
                except Exception as e:
                    logger.warning(f"无法在({lon}, {lat})采样'{name}': {e}")
                    features[name] = np.nan
            else:
                features[name] = np.nan
        return features

    def close(self):
        for src in self.rasters.values():
            if src:
                src.close()

# --- 仿真器核心 ---
class PathGuidedSimulator:
    def __init__(self, model_path, env_data_dir):
        logger.info("初始化路径引导仿真器...")
        self.model = joblib.load(model_path)
        logger.info("  - 驾驶行为模型已加载。")
        self.environment = Environment(env_data_dir)
        # 从模型中获取特征名称，确保顺序一致
        self.feature_names = self.model.feature_names_in_

    def _calculate_effective_slope(self, slope, aspect, heading_deg):
        if pd.isna(slope) or pd.isna(aspect) or pd.isna(heading_deg): return 0.0
        angle_diff = abs(aspect - heading_deg) % 360
        angle_diff = angle_diff if angle_diff <= 180 else 360 - angle_diff
        direction = -1 if angle_diff > 90 else 1
        return direction * slope * abs(np.cos(np.radians(angle_diff)))

    def run(self, initial_state, waypoints, max_steps=1000, dt=0.5):
        """
        运行仿真
        - initial_state: {'x', 'y', 'speed_ms', 'heading_rad'}
        - waypoints: list of (x, y) tuples
        """
        logger.info("开始路径引导仿真...")
        logger.info(f"  - 目标路标点数: {len(waypoints)}")
        logger.info(f"  - 初始状态: x={initial_state['x']:.2f}, y={initial_state['y']:.2f}, speed={initial_state['speed_ms']:.2f} m/s, heading={np.degrees(initial_state['heading_rad']):.2f}°")

        # 状态变量
        x, y = initial_state['x'], initial_state['y']
        speed = initial_state['speed_ms']
        heading = initial_state['heading_rad']
        
        # 初始化轨迹记录
        trajectory = []
        
        # 航点管理
        waypoint_index = 0
        target_waypoint = waypoints[waypoint_index]
        
        # 初始"前一时刻加速度"可以设为0
        prev_acceleration = 0.0

        for step in range(max_steps):
            # 1. 获取环境特征
            env_features = self.environment.get_features_at_coord(x, y)
            
            # 2. 计算衍生特征
            effective_slope = self._calculate_effective_slope(env_features.get('slope'), env_features.get('aspect'), np.degrees(heading))
            landcover_code = int(env_features.get('landcover', 0))

            # 3. 计算引导特征
            vec_to_wp_x = target_waypoint[0] - x
            vec_to_wp_y = target_waypoint[1] - y
            distance_to_wp = np.sqrt(vec_to_wp_x**2 + vec_to_wp_y**2)
            angle_to_wp_rad = np.arctan2(vec_to_wp_y, vec_to_wp_x)
            
            angle_diff = angle_to_wp_rad - heading
            while angle_diff > np.pi: angle_diff -= 2 * np.pi
            while angle_diff < -np.pi: angle_diff += 2 * np.pi

            # 4. 构建特征向量 (必须与训练时完全一致)
            feature_vector = pd.DataFrame([{
                'velocity_2d_ms': speed,
                'effective_slope': effective_slope,
                'landcover_code': landcover_code,
                'dem': env_features.get('dem', 0),
                'prev_acceleration': prev_acceleration,
                'distance_to_wp': distance_to_wp,
                'angle_to_wp': angle_diff,
                'heading_sin': np.sin(heading),
                'heading_cos': np.cos(heading)
            }])
            # 确保列名顺序正确
            feature_vector = feature_vector[self.feature_names]

            # 5. 模型预测
            predicted_action = self.model.predict(feature_vector)[0]
            acceleration, heading_change_rate = predicted_action[0], predicted_action[1]
            
            # --- 修复：增加启动辅助逻辑 ---
            if speed < 1.0 and distance_to_wp > 10.0:  # 如果速度慢且目标远
                # 确保加速度至少为一个正值来启动车辆
                original_accel = acceleration
                acceleration = max(acceleration, 0.5) 
                if original_accel < 0.5:
                     logger.info(f"  - [启动辅助] 速度过低 ({speed:.2f} m/s)，强制提升加速度至 {acceleration:.2f} m/s²。")

            # 对预测值进行裁剪，防止极端值
            acceleration = np.clip(acceleration, -4.0, 3.0)
            heading_change_rate = np.clip(heading_change_rate, -0.5, 0.5)

            # 记录当前状态
            current_log = {
                'step': step, 'x': x, 'y': y, 'speed_ms': speed, 
                'heading_rad': heading, 'acceleration': acceleration, 
                'heading_change_rate': heading_change_rate,
                'target_wp_x': target_waypoint[0], 'target_wp_y': target_waypoint[1],
                'distance_to_wp': distance_to_wp
            }
            trajectory.append(current_log)

            # 6. 更新状态 (运动学模型)
            speed += acceleration * dt
            speed = max(0, speed) # 速度不能为负
            heading += heading_change_rate * dt
            heading = (heading + np.pi) % (2 * np.pi) - np.pi # 归一化到 -pi, pi

            x += speed * np.cos(heading) * dt
            y += speed * np.sin(heading) * dt

            # 更新 prev_acceleration
            prev_acceleration = acceleration
            
            # 7. 检查是否到达航点
            if distance_to_wp < 50.0: # 到达阈值
                if waypoint_index < len(waypoints) - 1:
                    waypoint_index += 1
                    target_waypoint = waypoints[waypoint_index]
                    logger.info(f"  - 步数 {step}: 到达路标点 {waypoint_index}, 前往下一个...")
                else:
                    logger.info(f"  - 步数 {step}: 到达最终路标点！仿真结束。")
                    break
        else: # for循环正常结束，没有break
            logger.warning(f"仿真达到最大步数 {max_steps}，未能到达最终路标点。")

        self.environment.close()
        return pd.DataFrame(trajectory)


def main():
    """主函数：运行一个仿真示例"""
    logger.info("===== 开始路径引导式仿真测试 =====")
    
    # 1. 加载真实轨迹以提取路标点和初始状态
    try:
        real_trajectory = pd.read_csv('trajectory_generator/data/trajectories/trajectory_1.csv')
    except FileNotFoundError:
        logger.error("错误: 无法找到 'trajectory_1.csv'。请确保文件路径正确。")
        return

    # 2. 设置初始状态 (使用真实轨迹的第一行)
    initial_state = {
        'x': real_trajectory['longitude'].iloc[0],
        'y': real_trajectory['latitude'].iloc[0],
        'speed_ms': real_trajectory['velocity_2d_ms'].iloc[0],
        'heading_rad': np.arctan2(real_trajectory['velocity_north_ms'].iloc[0], real_trajectory['velocity_east_ms'].iloc[0])
    }
    
    # 3. 设置路标点 (每隔50个点采样一次)
    waypoint_interval = 50
    waypoints = real_trajectory[['longitude', 'latitude']].iloc[::waypoint_interval].values.tolist()
    # 确保最后一个点是路标点
    if list(real_trajectory[['longitude', 'latitude']].iloc[-1].values) not in waypoints:
        waypoints.append(list(real_trajectory[['longitude', 'latitude']].iloc[-1].values))

    # 4. 运行仿真器
    simulator = PathGuidedSimulator(model_path=MODEL_PATH, env_data_dir=ENV_DATA_DIR)
    simulated_trajectory = simulator.run(initial_state, waypoints, max_steps=5000)

    # 5. 保存结果
    if not simulated_trajectory.empty:
        os.makedirs(RESULTS_DIR, exist_ok=True)
        output_path = os.path.join(RESULTS_DIR, "guided_trajectory.csv")
        simulated_trajectory.to_csv(output_path, index=False)
        logger.info(f"仿真轨迹已保存至: {output_path}")

        # 打印摘要
        logger.info("\n--- 仿真结果摘要 ---")
        logger.info(f"总步数: {len(simulated_trajectory)}")
        logger.info(f"平均速度: {simulated_trajectory['speed_ms'].mean():.2f} m/s")
        logger.info(f"最大速度: {simulated_trajectory['speed_ms'].max():.2f} m/s")
        logger.info(f"平均加速度: {simulated_trajectory['acceleration'].mean():.2f} m/s²")
    else:
        logger.warning("仿真未生成任何轨迹数据。")

    logger.info("===== 仿真测试完成 =====")


if __name__ == "__main__":
    main() 