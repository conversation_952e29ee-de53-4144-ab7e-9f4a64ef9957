#!/usr/bin/env python3
"""
测试环境-速度模型
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config import Config
from environment_speed_model import EnvironmentSpeedModel, train_environment_speed_model
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_model_training_and_prediction():
    """测试模型训练和预测"""
    logger.info("=== 测试环境-速度模型 ===")
    
    # 1. 训练模型
    logger.info("1. 训练环境-速度模型...")
    try:
        model, results = train_environment_speed_model()
        if model is None:
            logger.error("模型训练失败")
            return False
        
        logger.info(f"模型训练成功！R² = {results['r2']:.4f}")
        
        # 2. 测试预测功能
        logger.info("2. 测试速度预测功能...")
        
        # 模拟一些测试状态
        test_states = [
            {
                'utm_x': 4.4491815e+06,
                'utm_y': 4.0639588e+05,
                'speed_2d': 5.0,
                'heading': 0.0
            },
            {
                'utm_x': 4.4491820e+06, 
                'utm_y': 4.0639590e+05,
                'speed_2d': 3.0,
                'heading': 0.1
            }
        ]
        
        test_contexts = [
            {
                'curvature': 0.001,
                'path_angle': 0.0,
                'turning_rate': 0.0,
                'complexity': 0.1,
                'elevation_change': 0.0,
                'time_progress': 10.0,
                'start_x': 4.4491815e+06,
                'start_y': 4.0639588e+05
            },
            {
                'curvature': 0.005,
                'path_angle': 0.2,
                'turning_rate': 0.1,
                'complexity': 0.3,
                'elevation_change': 5.0,
                'time_progress': 50.0,
                'start_x': 4.4491815e+06,
                'start_y': 4.0639588e+05
            }
        ]
        
        for i, (state, context) in enumerate(zip(test_states, test_contexts)):
            predicted_speed = model.predict_speed(state, context)
            logger.info(f"测试 {i+1}: 预测速度 = {predicted_speed:.2f} m/s")
            
        logger.info("速度预测测试成功！")
        
        # 3. 保存特征重要性图
        logger.info("3. 生成特征重要性分析...")
        plot_feature_importance_detailed(results['feature_importance'])
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def plot_feature_importance_detailed(feature_importance):
    """详细的特征重要性分析图"""
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 左图：Top 15 特征重要性
    top_features = feature_importance.head(15)
    bars1 = ax1.barh(range(len(top_features)), top_features['importance'], 
                     color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                           '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'] * 2)
    ax1.set_yticks(range(len(top_features)))
    ax1.set_yticklabels(top_features['feature'], fontsize=10)
    ax1.set_xlabel('特征重要性', fontsize=12)
    ax1.set_title('Top 15 影响速度的环境特征', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.invert_yaxis()
    
    # 在柱子上添加数值
    for i, bar in enumerate(bars1):
        width = bar.get_width()
        ax1.text(width + 0.001, bar.get_y() + bar.get_height()/2, 
                f'{width:.4f}', ha='left', va='center', fontsize=8)
    
    # 右图：特征重要性分布
    ax2.hist(feature_importance['importance'], bins=20, alpha=0.7, 
             color='skyblue', edgecolor='black')
    ax2.set_xlabel('特征重要性值', fontsize=12)
    ax2.set_ylabel('特征数量', fontsize=12)
    ax2.set_title('特征重要性分布直方图', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 添加统计信息
    ax2.axvline(feature_importance['importance'].mean(), color='red', 
                linestyle='--', label=f'平均值: {feature_importance["importance"].mean():.4f}')
    ax2.axvline(feature_importance['importance'].median(), color='green', 
                linestyle='--', label=f'中位数: {feature_importance["importance"].median():.4f}')
    ax2.legend()
    
    plt.tight_layout()
    
    # 确保results目录存在
    os.makedirs("results", exist_ok=True)
    plt.savefig('results/feature_importance_detailed.png', dpi=300, bbox_inches='tight')
    logger.info("详细特征重要性图已保存: results/feature_importance_detailed.png")
    plt.close()

def create_demo_visualization():
    """创建演示可视化"""
    logger.info("4. 创建演示可视化...")
    
    # 模拟不同环境条件下的速度预测
    curvatures = np.linspace(0, 0.01, 50)
    speeds_straight = []
    speeds_curved = []
    
    # 加载模型
    config = Config()
    model = EnvironmentSpeedModel(config)
    
    if os.path.exists("models/environment_speed_model.pkl"):
        model.load_model("models/environment_speed_model.pkl")
        
        base_state = {
            'utm_x': 4.4491815e+06,
            'utm_y': 4.0639588e+05,
            'speed_2d': 5.0,
            'heading': 0.0
        }
        
        for curvature in curvatures:
            # 直线路径
            context_straight = {
                'curvature': 0.0,
                'path_angle': 0.0,
                'turning_rate': 0.0,
                'complexity': 0.1,
                'elevation_change': 0.0,
                'time_progress': 50.0,
                'start_x': base_state['utm_x'],
                'start_y': base_state['utm_y']
            }
            
            # 弯道路径
            context_curved = {
                'curvature': curvature,
                'path_angle': 0.2,
                'turning_rate': curvature * 5,
                'complexity': 0.3,
                'elevation_change': 0.0,
                'time_progress': 50.0,
                'start_x': base_state['utm_x'],
                'start_y': base_state['utm_y']
            }
            
            speed_straight = model.predict_speed(base_state, context_straight)
            speed_curved = model.predict_speed(base_state, context_curved)
            
            speeds_straight.append(speed_straight)
            speeds_curved.append(speed_curved)
        
        # 绘制结果
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        plt.plot(curvatures * 1000, speeds_straight, 'b-', linewidth=2, label='直线路径')
        plt.plot(curvatures * 1000, speeds_curved, 'r-', linewidth=2, label='弯道路径')
        plt.xlabel('路径曲率 (1/km)')
        plt.ylabel('预测速度 (m/s)')
        plt.title('环境-速度模型预测结果')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        speed_diff = np.array(speeds_straight) - np.array(speeds_curved)
        plt.plot(curvatures * 1000, speed_diff, 'g-', linewidth=2)
        plt.xlabel('路径曲率 (1/km)')
        plt.ylabel('速度差异 (m/s)')
        plt.title('直线与弯道速度差异')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('results/environment_speed_prediction_demo.png', dpi=300, bbox_inches='tight')
        logger.info("演示可视化已保存: results/environment_speed_prediction_demo.png")
        plt.close()
    else:
        logger.warning("模型文件不存在，跳过演示可视化")

def main():
    """主函数"""
    logger.info("开始环境-速度模型完整测试")
    
    success = test_model_training_and_prediction()
    
    if success:
        create_demo_visualization()
        logger.info("✅ 环境-速度模型测试全部完成！")
        
        # 显示结果文件
        result_files = [
            "results/feature_importance_detailed.png",
            "results/environment_speed_prediction_demo.png",
            "models/environment_speed_model.pkl"
        ]
        
        logger.info("生成的文件:")
        for file_path in result_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path) / 1024
                logger.info(f"  ✅ {file_path} ({file_size:.1f} KB)")
            else:
                logger.info(f"  ❌ {file_path} (未生成)")
    else:
        logger.error("❌ 测试失败")

if __name__ == "__main__":
    main() 