#!/usr/bin/env python3
"""
基于加速度驱动的智能轨迹仿真器
- 可配置任意初始状态
- 环境感知 → 加速度决策  
- 加速度 → 速度变化
- 速度 → 位置变化
- 包含随机性和不确定性
"""

import numpy as np
import pandas as pd
import joblib
import os
import json
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AccelerationDrivenSimulator:
    """基于加速度的智能轨迹仿真器"""
    
    def __init__(self):
        """初始化仿真器"""
        self.path_points = []
        self.current_state = None
        self.trajectory_history = []
        self.current_path_index = 0
        
        # 加载环境-加速度模型
        self.acceleration_model = self._load_or_train_acceleration_model()
        
        # 物理参数
        self.dt = 0.5  # 时间步长(秒)
        self.max_acceleration = 3.0  # 最大加速度 (m/s²)
        self.max_deceleration = -4.0  # 最大减速度 (m/s²)
        self.max_speed = 15.0  # 最大速度 (m/s)
        self.noise_std = 0.15  # 加速度噪声标准差
        
        logger.info("基于加速度的智能轨迹仿真器初始化完成")
    
    def _load_or_train_acceleration_model(self):
        """加载或训练环境-加速度模型"""
        model_path = 'results/acceleration_model.pkl'
        
        if os.path.exists(model_path):
            logger.info("加载现有加速度预测模型")
            return joblib.load(model_path)
        else:
            logger.info("训练新的加速度预测模型")
            return self._train_acceleration_model()
    
    def _train_acceleration_model(self):
        """训练环境-加速度预测模型"""
        from sklearn.ensemble import RandomForestRegressor
        
        # 从真实轨迹数据中提取环境特征和加速度标签
        trajectory_file = 'trajectory_generator/data/trajectories/trajectory_1.csv'
        df = pd.read_csv(trajectory_file)
        
        # 重命名坐标列
        if 'latitude' in df.columns:
            df.rename(columns={'latitude': 'utm_x', 'longitude': 'utm_y'}, inplace=True)
        
        # 计算加速度（从速度变化推导）
        df['delta_time_filled'] = df['delta_time_s'].fillna(0.5)
        df['acceleration'] = df['velocity_2d_ms'].diff() / df['delta_time_filled']
        df['acceleration'] = df['acceleration'].fillna(0.0)
        
        # 限制加速度范围到合理值
        df['acceleration'] = np.clip(df['acceleration'], -5.0, 5.0)
        
        # 构建环境特征
        features = []
        accelerations = []
        
        for i in range(1, len(df)-1):
            row = df.iloc[i]
            
            # 当前状态特征
            current_speed = row['velocity_2d_ms']
            current_heading = np.radians(row['heading_deg'])
            
            # 路径特征（前后点的曲率、距离等）
            prev_point = df.iloc[i-1]
            next_point = df.iloc[i+1]
            
            # 计算前向距离和方向变化
            dx = next_point['utm_x'] - row['utm_x']
            dy = next_point['utm_y'] - row['utm_y']
            forward_distance = np.sqrt(dx**2 + dy**2)
            target_heading = np.arctan2(dy, dx)
            heading_diff = np.abs(np.arctan2(np.sin(target_heading - current_heading), 
                                           np.cos(target_heading - current_heading)))
            
            # 速度相关特征
            speed_ratio = current_speed / 10.0  # 归一化速度
            
            feature_vector = [
                current_speed,
                speed_ratio, 
                forward_distance,
                heading_diff,
                np.sin(current_heading),
                np.cos(current_heading)
            ]
            
            features.append(feature_vector)
            accelerations.append(row['acceleration'])
        
        # 训练模型
        X = np.array(features)
        y = np.array(accelerations)
        
        # 过滤异常值
        valid_mask = (np.abs(y) < 5.0) & (X[:, 0] >= 0)  # 有效加速度和速度
        X = X[valid_mask]
        y = y[valid_mask]
        
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        
        model.fit(X, y)
        
        # 保存模型
        os.makedirs('results', exist_ok=True)
        joblib.dump(model, 'results/acceleration_model.pkl')
        
        logger.info(f"加速度模型训练完成，样本数: {len(X)}")
        return model
    
    def set_initial_state(self, utm_x: float, utm_y: float, speed_2d: float, heading_deg: float, timestamp: float = None):
        """设置任意初始状态 - 这是您要求的关键功能"""
        if timestamp is None:
            timestamp = 1637841695.0  # 默认时间戳
            
        self.current_state = {
            'utm_x': utm_x,
            'utm_y': utm_y, 
            'speed_2d': speed_2d,
            'heading': np.radians(heading_deg),
            'timestamp': timestamp
        }
        
        # 重置仿真历史
        self.trajectory_history = []
        self.current_path_index = 0
        
        logger.info(f"设置初始状态: 位置({utm_x:.1f}, {utm_y:.1f}), "
                   f"速度={speed_2d:.3f} m/s, 航向={heading_deg:.1f}°")
    
    def set_path_from_real_trajectory(self, trajectory_file):
        """从真实轨迹提取引导路径"""
        logger.info(f"从真实轨迹提取引导路径: {trajectory_file}")
        
        df = pd.read_csv(trajectory_file)
        
        # 修正坐标列名
        if 'latitude' in df.columns and 'longitude' in df.columns:
            df.rename(columns={'latitude': 'utm_x', 'longitude': 'utm_y'}, inplace=True)
        
        # 数据清洗：过滤无效坐标
        valid_mask = (
            (df['utm_x'] > 1000000) & (df['utm_x'] < 50000000) &
            (df['utm_y'] > -50000000) & (df['utm_y'] < 50000000)
        )
        df_clean = df[valid_mask].copy()
        
        if len(df_clean) == 0:
            raise ValueError("清洗后无有效路径点")
        
        # 提取路径点（仅位置信息，不包含速度）
        self.path_points = []
        for _, row in df_clean.iterrows():
            self.path_points.append({
                'utm_x': row['utm_x'],
                'utm_y': row['utm_y'],
                'timestamp': row['timestamp_ms'] / 1000.0
            })
        
        logger.info(f"成功提取 {len(self.path_points)} 个引导路径点")
    
    def predict_acceleration(self, current_state: Dict, path_context: Dict) -> float:
        """基于环境预测加速度 - 这是核心的AI决策模块"""
        # 当前状态特征
        current_speed = current_state['speed_2d']
        current_heading = current_state['heading']
        
        # 路径上下文特征
        forward_distance = path_context.get('forward_distance', 5.0)
        heading_diff = path_context.get('heading_diff', 0.0)
        
        # 速度相关特征
        speed_ratio = current_speed / 10.0
        
        # 构建特征向量
        features = np.array([[
            current_speed,
            speed_ratio,
            forward_distance, 
            heading_diff,
            np.sin(current_heading),
            np.cos(current_heading)
        ]])
        
        # AI预测基础加速度
        try:
            predicted_acceleration = self.acceleration_model.predict(features)[0]
        except:
            # 如果模型预测失败，使用简单的基于规则的加速度
            if current_speed < 3.0:
                predicted_acceleration = 1.0  # 加速到合理速度
            elif current_speed > 8.0:
                predicted_acceleration = -0.5  # 减速
            else:
                predicted_acceleration = 0.1  # 维持速度
        
        # 添加物理约束
        predicted_acceleration = np.clip(predicted_acceleration, 
                                       self.max_deceleration, 
                                       self.max_acceleration)
        
        # 添加随机噪声（模拟不确定性） - 您要求的随机性
        noise = np.random.normal(0, self.noise_std)
        predicted_acceleration += noise
        
        # 速度边界约束
        if current_speed >= self.max_speed and predicted_acceleration > 0:
            predicted_acceleration = -1.0  # 强制减速
        elif current_speed <= 0 and predicted_acceleration < 0:
            predicted_acceleration = max(0.2, predicted_acceleration)  # 防止倒车
        
        return predicted_acceleration
    
    def calculate_path_context(self) -> Dict:
        """计算路径上下文信息"""
        if not self.path_points or self.current_path_index >= len(self.path_points) - 1:
            return {
                'forward_distance': 10.0,
                'heading_diff': 0.0,
                'lateral_deviation': 0.0
            }
        
        current_pos = self.current_state
        target_point = self.path_points[self.current_path_index]
        
        # 前向距离
        dx = target_point['utm_x'] - current_pos['utm_x']
        dy = target_point['utm_y'] - current_pos['utm_y']
        forward_distance = np.sqrt(dx**2 + dy**2)
        
        # 目标航向与当前航向差异
        target_heading = np.arctan2(dy, dx)
        heading_diff = np.abs(np.arctan2(
            np.sin(target_heading - current_pos['heading']),
            np.cos(target_heading - current_pos['heading'])
        ))
        
        # 横向偏差（到路径的垂直距离）
        lateral_deviation = abs(dx * np.sin(current_pos['heading']) - 
                              dy * np.cos(current_pos['heading']))
        
        return {
            'forward_distance': forward_distance,
            'heading_diff': heading_diff,
            'lateral_deviation': lateral_deviation
        }
    
    def physics_update(self, acceleration: float) -> Dict:
        """物理更新：加速度→速度→位置 - 您要求的物理驱动"""
        current = self.current_state
        
        # 1. 更新速度：v = v0 + a*dt (核心物理公式)
        new_speed = current['speed_2d'] + acceleration * self.dt
        new_speed = max(0.0, min(new_speed, self.max_speed))  # 速度约束
        
        # 2. 路径跟踪控制：更新航向
        path_context = self.calculate_path_context()
        
        # 简单的航向控制（向目标点转向）
        if self.current_path_index < len(self.path_points) - 1:
            target_point = self.path_points[self.current_path_index]
            dx = target_point['utm_x'] - current['utm_x']
            dy = target_point['utm_y'] - current['utm_y']
            
            if dx**2 + dy**2 > 1.0:  # 距离足够远才调整航向
                target_heading = np.arctan2(dy, dx)
                heading_error = np.arctan2(
                    np.sin(target_heading - current['heading']),
                    np.cos(target_heading - current['heading'])
                )
                
                # 航向控制增益
                heading_gain = 0.3
                new_heading = current['heading'] + heading_gain * heading_error * self.dt
            else:
                new_heading = current['heading']
                # 移动到下一个路径点
                if self.current_path_index < len(self.path_points) - 1:
                    self.current_path_index += 1
        else:
            new_heading = current['heading']
        
        # 3. 更新位置：基于平均速度和航向 (位置积分)
        avg_speed = (current['speed_2d'] + new_speed) / 2.0
        dx = avg_speed * np.cos(new_heading) * self.dt
        dy = avg_speed * np.sin(new_heading) * self.dt
        
        new_x = current['utm_x'] + dx
        new_y = current['utm_y'] + dy
        
        # 4. 更新时间戳
        new_timestamp = current['timestamp'] + self.dt
        
        # 返回新状态
        new_state = {
            'utm_x': new_x,
            'utm_y': new_y,
            'speed_2d': new_speed,
            'heading': new_heading,
            'timestamp': new_timestamp,
            'acceleration': acceleration,
            'lateral_deviation': path_context['lateral_deviation']
        }
        
        return new_state
    
    def simulate_step(self) -> bool:
        """执行一步仿真"""
        if not self.current_state:
            raise ValueError("未设置初始状态")
        
        # 1. 环境感知
        path_context = self.calculate_path_context()
        
        # 2. AI决策：环境 → 加速度
        predicted_acceleration = self.predict_acceleration(self.current_state, path_context)
        
        # 3. 物理更新：加速度 → 速度 → 位置
        new_state = self.physics_update(predicted_acceleration)
        
        # 4. 记录轨迹
        trajectory_point = {
            'timestamp': new_state['timestamp'],
            'utm_x': new_state['utm_x'],
            'utm_y': new_state['utm_y'],
            'speed_2d': new_state['speed_2d'],
            'heading': new_state['heading'],
            'acceleration': new_state['acceleration'],
            'lateral_deviation': new_state['lateral_deviation']
        }
        self.trajectory_history.append(trajectory_point)
        
        # 5. 更新当前状态
        self.current_state = new_state
        
        # 6. 检查终止条件
        if new_state['lateral_deviation'] > 50.0:  # 偏差过大
            logger.warning(f"横向偏差过大终止: {new_state['lateral_deviation']:.1f} m")
            return False
        
        if self.current_path_index >= len(self.path_points) - 1:  # 路径完成
            return False
            
        return True
    
    def run_simulation(self, max_steps: int = None) -> List[Dict]:
        """运行完整仿真"""
        # 如果没有指定最大步数，使用路径点数量的2倍作为合理上限
        if max_steps is None:
            max_steps = len(self.path_points) * 2
        
        logger.info(f"开始加速度驱动仿真 (最大步数: {max_steps})")
        
        step_count = 0
        while step_count < max_steps:
            try:
                if not self.simulate_step():
                    break
                    
                step_count += 1
                
                # 定期输出进度
                if step_count % 100 == 0:
                    current = self.current_state
                    logger.info(f"步数: {step_count}, 位置: ({current['utm_x']:.1f}, {current['utm_y']:.1f}), "
                              f"速度: {current['speed_2d']:.1f} m/s, 加速度: {current.get('acceleration', 0):.2f} m/s², "
                              f"横向偏差: {current.get('lateral_deviation', 0):.1f} m")
                
            except Exception as e:
                logger.error(f"仿真步骤错误: {e}")
                break
        
        logger.info(f"仿真完成: 总步数 {step_count}")
        return self.trajectory_history
    
    def save_results(self, output_dir: str):
        """保存仿真结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存轨迹数据
        if self.trajectory_history:
            df = pd.DataFrame(self.trajectory_history)
            trajectory_file = os.path.join(output_dir, 'trajectory.csv')
            df.to_csv(trajectory_file, index=False)
            logger.info(f"仿真结果已保存: {trajectory_file}")
            
            # 生成分析报告
            self._generate_analysis_report(df, output_dir)
        else:
            logger.warning("无仿真结果可保存")
    
    def _generate_analysis_report(self, df: pd.DataFrame, output_dir: str):
        """生成仿真分析报告"""
        # 统计分析
        stats = {
            '总仿真时间': f"{df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]:.1f} 秒",
            '平均速度': f"{df['speed_2d'].mean():.2f} m/s",
            '最大速度': f"{df['speed_2d'].max():.2f} m/s", 
            '平均加速度': f"{df['acceleration'].mean():.3f} m/s²",
            '最大加速度': f"{df['acceleration'].max():.2f} m/s²",
            '最小加速度': f"{df['acceleration'].min():.2f} m/s²",
            '平均横向偏差': f"{df['lateral_deviation'].mean():.2f} m",
            '最大横向偏差': f"{df['lateral_deviation'].max():.2f} m"
        }
        
        logger.info("仿真统计分析:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 保存统计数据
        stats_file = os.path.join(output_dir, 'statistics.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)


def main():
    """测试加速度驱动仿真器"""
    simulator = AccelerationDrivenSimulator()
    
    # 设置引导路径
    trajectory_file = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    simulator.set_path_from_real_trajectory(trajectory_file)
    
    # 测试场景1：使用真实轨迹的初始状态
    print("\n=== 测试场景 1：真实初始状态 ===")
    df_real = pd.read_csv(trajectory_file)
    first_row = df_real.iloc[0]
    simulator.set_initial_state(
        utm_x=first_row['latitude'],  # 实际是UTM坐标
        utm_y=first_row['longitude'],
        speed_2d=first_row['velocity_2d_ms'],
        heading_deg=first_row['heading_deg']
    )
    
    # 运行仿真 - 让仿真器自动决定步数
    results = simulator.run_simulation()
    simulator.save_results('results/acceleration_simulation_1')
    
    # 测试场景2：自定义初始状态
    print("\n=== 测试场景 2：从静止开始 ===") 
    simulator.set_initial_state(
        utm_x=8846764.0,
        utm_y=-981867.0,
        speed_2d=0.0,  # 从静止开始
        heading_deg=45.0  # 东北方向
    )
    
    results = simulator.run_simulation()
    simulator.save_results('results/acceleration_simulation_2')
    
    # 测试场景3：高速初始状态
    print("\n=== 测试场景 3：高速初始状态 ===")
    simulator.set_initial_state(
        utm_x=8846764.0,
        utm_y=-981867.0, 
        speed_2d=10.0,  # 高速开始
        heading_deg=0.0  # 正东方向
    )
    
    results = simulator.run_simulation()
    simulator.save_results('results/acceleration_simulation_3')
    
    print("\n加速度驱动仿真测试完成！")


if __name__ == "__main__":
    main() 