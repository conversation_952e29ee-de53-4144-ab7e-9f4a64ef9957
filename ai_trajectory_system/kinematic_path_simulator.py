#!/usr/bin/env python3
"""
带状态记忆的"运动学路径仿真器"
- 核心思想: 速度不再由环境决定，而是由环境影响。
- V_new = V_current + a * Δt
- a = K * (V_target - V_current)
"""

import numpy as np
import pandas as pd
import os
import matplotlib.pyplot as plt
from datetime import datetime
import logging
import rasterio
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 20
plt.rcParams['axes.labelsize'] = 20
plt.rcParams['axes.titlesize'] = 22

class KinematicPathSimulator:
    """带状态记忆的运动学路径仿真器"""
    
    def __init__(self):
        """初始化仿真器"""
        self.path_points = pd.DataFrame()
        
        # 运动学参数
        self.speed_control_gain = 0.2  # 速度控制增益 K (从0.8降低)
        self.max_acceleration = 3.0    # m/s^2
        self.max_deceleration = -4.0   # m/s^2
        
        # 环境数据路径 (与之前一致)
        self.env_data_dir = 'trajectory_generator/data/environment'
        self.slope_file = os.path.join(self.env_data_dir, 'slope_aligned.tif')
        self.aspect_file = os.path.join(self.env_data_dir, 'aspect_aligned.tif') 
        self.landcover_file = os.path.join(self.env_data_dir, 'landcover_aligned.tif')
        
        self.landcover_mapping = {
            20: '林地', 40: '灌木地', 60: '水体'
        }
        
        self.speed_models = {
            '林地': {'base_speed': 5.8, 'slope_effect': {'uphill': -0.15, 'downhill': 0.08}},
            '灌木地': {'base_speed': 4.8, 'slope_effect': {'uphill': -0.25, 'downhill': 0.12}},
            '水体': {'base_speed': 6.1, 'slope_effect': {'uphill': -0.10, 'downhill': 0.15}}
        }
        
        logger.info("运动学路径仿真器初始化完成")
    
    def load_path_from_trajectory(self, trajectory_file: str):
        """从轨迹文件加载路径点"""
        logger.info(f"从轨迹文件加载路径: {trajectory_file}")
        df = pd.read_csv(trajectory_file)
        
        valid_mask = (
            (df['latitude'] > 1000000) & (df['latitude'] < 50000000) &
            (df['longitude'] > -50000000) & (df['longitude'] < 50000000)
        )
        self.path_points = df[valid_mask].copy()
        
        # 预计算环境特征
        self._preprocess_environment_features()
        
        logger.info(f"成功加载并预处理 {len(self.path_points)} 个路径点")

    def _preprocess_environment_features(self):
        """预计算并缓存环境特征，提高仿真速度"""
        df = self.path_points
        
        # 1. 提取基础环境数据
        coordinates = list(zip(df['longitude'], df['latitude']))
        slope = self._get_raster_values(self.slope_file, coordinates)
        aspect = self._get_raster_values(self.aspect_file, coordinates)
        landcover = self._get_raster_values(self.landcover_file, coordinates)
        
        # 2. 计算航向和有效坡度
        heading = np.degrees(np.arctan2(df['velocity_east_ms'], df['velocity_north_ms'])) % 360
        effective_slope = [self._calculate_effective_slope(s, a, h) for s, a, h in zip(slope, aspect, heading)]
        
        # 3. 计算每个点的环境目标速度
        target_speed = [self._calculate_target_speed(es, lc) for es, lc in zip(effective_slope, landcover)]
        
        # 4. 存入DataFrame
        df['landcover'] = landcover
        df['effective_slope'] = effective_slope
        df['target_speed'] = target_speed
        
        self.path_points = df
        logger.info("环境特征和目标速度已预计算完成")

    def simulate(self, initial_speed: float):
        """
        使用运动学模型进行仿真
        Args:
            initial_speed: 初始速度 (m/s)
        """
        logger.info(f"开始运动学仿真，初始速度: {initial_speed:.2f} m/s")
        
        if self.path_points.empty or 'target_speed' not in self.path_points.columns:
            raise ValueError("请先加载路径并预处理环境特征")

        df = self.path_points.copy()
        
        simulated_speeds = np.zeros(len(df))
        accelerations = np.zeros(len(df))
        simulated_speeds[0] = initial_speed
        
        for i in range(1, len(df)):
            # 获取上一步状态和当前环境
            v_current = simulated_speeds[i-1]
            v_target = df['target_speed'].iloc[i]
            delta_t = df['delta_time_s'].iloc[i]
            
            if pd.isna(v_target) or pd.isna(delta_t) or delta_t <= 0:
                simulated_speeds[i] = v_current
                accelerations[i] = 0
                continue
            
            # 计算加速度
            speed_error = v_target - v_current
            accel = self.speed_control_gain * speed_error
            accel = np.clip(accel, self.max_deceleration, self.max_acceleration)
            
            # 更新速度
            v_new = v_current + accel * delta_t
            v_new = max(0, v_new) # 速度不能为负
            
            simulated_speeds[i] = v_new
            accelerations[i] = accel
            
        df['simulated_speed'] = simulated_speeds
        df['simulated_acceleration'] = accelerations
        
        logger.info(f"仿真完成. 平均仿真速度: {df['simulated_speed'].mean():.2f} m/s")
        return df

    # --- 环境特征计算辅助函数 (与之前类似) ---
    def _get_raster_values(self, raster_path, coordinates):
        try:
            with rasterio.open(raster_path) as src:
                sampled_values = src.sample(coordinates)
                return [val[0] if len(val) > 0 else np.nan for val in sampled_values]
        except Exception as e:
            logger.error(f"读取栅格文件 {raster_path} 时出错: {e}")
            return [np.nan] * len(coordinates)

    def _calculate_effective_slope(self, slope, aspect, heading):
        if pd.isna(slope) or pd.isna(aspect) or pd.isna(heading): return np.nan
        angle_diff = abs(aspect - heading)
        angle_diff = angle_diff if angle_diff <= 180 else 360 - angle_diff
        direction = -1 if angle_diff > 90 else 1
        return direction * slope * abs(np.cos(np.radians(angle_diff)))

    def _calculate_target_speed(self, effective_slope, landcover):
        """只计算环境建议的目标速度，不含平滑"""
        landcover_type = self.landcover_mapping.get(landcover)
        if landcover_type is None or pd.isna(effective_slope):
            return np.nan
        
        model = self.speed_models[landcover_type]
        slope_effect_type = 'uphill' if effective_slope > 0 else 'downhill'
        slope_effect = model['slope_effect'][slope_effect_type]
        
        return model['base_speed'] + slope_effect * abs(effective_slope)

def run_and_compare_scenarios():
    """运行多个场景并生成对比图"""
    logger.info("===== 开始运动学仿真对比测试 =====")
    
    simulator = KinematicPathSimulator()
    simulator.load_path_from_trajectory('trajectory_generator/data/trajectories/trajectory_1.csv')
    
    # 定义测试场景
    scenarios = {
        'low_speed_start': 2.0,
        'medium_speed_start': 8.0,
        'high_speed_start': 12.0
    }
    
    results = {}
    output_dir = "kinematic_simulation_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 运行仿真
    for name, initial_speed in scenarios.items():
        result_df = simulator.simulate(initial_speed=initial_speed)
        results[name] = result_df
        # 保存每个场景的详细结果
        result_df.to_csv(os.path.join(output_dir, f"{name}_trajectory.csv"), index=False)
    
    # 生成对比图
    plt.figure(figsize=(18, 10))
    
    # 绘制真实速度
    real_speed = simulator.path_points['velocity_2d_ms']
    plt.plot(real_speed.index, real_speed, 'k--', label='真实速度', linewidth=2.5, alpha=0.7)
    
    # 绘制各场景仿真速度
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    for (name, df), color in zip(results.items(), colors):
        label = f"仿真 (初始速度 {scenarios[name]} m/s)"
        plt.plot(df.index, df['simulated_speed'], color=color, label=label, linewidth=2)
        
    plt.title('不同初始速度下的运动学仿真对比')
    plt.xlabel('路径点索引')
    plt.ylabel('速度 (m/s)')
    plt.legend(fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.tight_layout()
    
    comparison_plot_path = os.path.join(output_dir, "kinematic_comparison.png")
    plt.savefig(comparison_plot_path, dpi=300)
    plt.close()
    
    logger.info(f"所有场景仿真完成，对比图已保存至: {comparison_plot_path}")

if __name__ == "__main__":
    run_and_compare_scenarios() 