#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理模块

阶段一：数据加载与初始处理
- 读取原始CSV轨迹数据和环境栅格数据
- 轨迹时间标准化（插值到固定时间步长）
- 坐标系转换（WGS84 -> UTM）
- 环境数据预处理
"""

import os
import glob
import numpy as np
import pandas as pd
import rasterio
import pyproj
from scipy.interpolate import interp1d
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from config import config


class DataProcessor:
    """数据加载与处理核心类"""
    
    def __init__(self, cfg=None):
        """初始化数据处理器
        
        Args:
            cfg: 配置对象，如果为None则使用默认配置
        """
        self.config = cfg if cfg is not None else config
        
        # 初始化坐标转换器
        self.wgs84 = pyproj.CRS("EPSG:4326")
        self.utm = pyproj.CRS(f"EPSG:326{self.config.target_utm_zone}")
        self.transformer = pyproj.Transformer.from_crs(
            self.wgs84, self.utm, always_xy=True
        )
        
        # 预加载环境数据
        self.environment_data = {}
        self._load_environment_data()
        
        print("✅ 数据处理器初始化完成")
    
    def _load_environment_data(self):
        """预加载环境栅格数据到内存"""
        print("📁 预加载环境数据...")
        
        env_files = self.config.get_environment_files()
        
        for key, filepath in env_files.items():
            if os.path.exists(filepath):
                try:
                    with rasterio.open(filepath) as src:
                        # 读取栅格数据和地理信息
                        data = src.read(1)  # 读取第一个波段
                        transform = src.transform
                        bounds = src.bounds
                        crs = src.crs
                        
                        self.environment_data[key] = {
                            'data': data,
                            'transform': transform,
                            'bounds': bounds,
                            'crs': crs,
                            'shape': data.shape
                        }
                        
                        print(f"  ✅ {key}: {filepath} ({data.shape[0]}x{data.shape[1]})")
                        
                except Exception as e:
                    print(f"  ❌ 加载 {key} 失败: {e}")
                    self.environment_data[key] = None
            else:
                print(f"  ⚠️ 文件不存在: {filepath}")
                self.environment_data[key] = None
        
        print(f"📁 环境数据加载完成，共 {len([k for k, v in self.environment_data.items() if v is not None])} 个有效文件")
    
    def load_trajectory_files(self) -> List[str]:
        """获取所有轨迹文件路径
        
        Returns:
            List[str]: 轨迹文件路径列表
        """
        pattern = os.path.join(self.config.trajectory_data_dir, "*.csv")
        files = glob.glob(pattern)
        
        print(f"📂 找到 {len(files)} 个轨迹文件")
        return sorted(files)
    
    def load_single_trajectory(self, filepath: str) -> Optional[pd.DataFrame]:
        """加载单个轨迹文件
        
        Args:
            filepath: 轨迹文件路径
            
        Returns:
            pd.DataFrame: 处理后的轨迹数据，如果失败返回None
        """
        try:
            print(f"📄 加载轨迹: {os.path.basename(filepath)}")
            
            # 读取CSV文件
            df = pd.read_csv(filepath)
            
            # 检查必要的列
            required_cols = ['timestamp_ms', 'longitude', 'latitude']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                raise ValueError(f"缺少必要的列: {missing_cols}")
            
            # 添加速度列（如果不存在）
            if 'velocity_2d_ms' not in df.columns:
                if 'velocity_north_ms' in df.columns and 'velocity_east_ms' in df.columns:
                    df['velocity_2d_ms'] = np.sqrt(
                        df['velocity_north_ms']**2 + df['velocity_east_ms']**2
                    )
                else:
                    print("  ⚠️ 无法计算2D速度，将使用默认值")
                    df['velocity_2d_ms'] = 5.0  # 默认速度
            
            # 添加加速度列（如果不存在）
            if 'acceleration_x_ms2' not in df.columns:
                df['acceleration_x_ms2'] = 0.0
            if 'acceleration_y_ms2' not in df.columns:
                df['acceleration_y_ms2'] = 0.0
            
            print(f"  ✅ 原始数据: {len(df)} 行，时长 {(df['timestamp_ms'].max() - df['timestamp_ms'].min())/1000/60:.1f} 分钟")
            
            return df
            
        except Exception as e:
            print(f"  ❌ 加载失败: {e}")
            return None
    
    def standardize_trajectory_time(self, df: pd.DataFrame) -> pd.DataFrame:
        """轨迹时间标准化：插值到固定时间步长
        
        Args:
            df: 原始轨迹数据
            
        Returns:
            pd.DataFrame: 时间标准化后的轨迹数据
        """
        print(f"⏰ 时间标准化: {self.config.target_time_step}秒间隔")
        
        # 计算相对时间（秒）
        start_time = df['timestamp_ms'].iloc[0]
        df['time_s'] = (df['timestamp_ms'] - start_time) / 1000.0
        
        # 创建均匀时间网格
        total_time = df['time_s'].max()
        uniform_time = np.arange(0, total_time + self.config.target_time_step, 
                               self.config.target_time_step)
        
        # 准备插值的列
        interp_columns = [
            'longitude', 'latitude', 'velocity_2d_ms',
            'velocity_north_ms', 'velocity_east_ms',
            'acceleration_x_ms2', 'acceleration_y_ms2'
        ]
        
        # 只对存在的列进行插值
        existing_columns = [col for col in interp_columns if col in df.columns]
        
        # 创建插值后的DataFrame
        interpolated_data = {'time_s': uniform_time}
        
        for col in existing_columns:
            # 过滤无效值
            valid_mask = ~(pd.isna(df[col]) | np.isinf(df[col]))
            if valid_mask.sum() < 2:
                # 如果有效数据点太少，使用常数值
                interpolated_data[col] = np.full(len(uniform_time), df[col].median())
            else:
                # 线性插值
                interp_func = interp1d(
                    df.loc[valid_mask, 'time_s'], 
                    df.loc[valid_mask, col],
                    kind=self.config.interpolation_method,
                    bounds_error=False,
                    fill_value='extrapolate'
                )
                interpolated_data[col] = interp_func(uniform_time)
        
        # 重新计算时间戳
        interpolated_data['timestamp_ms'] = start_time + uniform_time * 1000
        
        result_df = pd.DataFrame(interpolated_data)
        
        print(f"  ✅ 标准化完成: {len(df)} -> {len(result_df)} 行")
        return result_df
    
    def convert_coordinates(self, df: pd.DataFrame) -> pd.DataFrame:
        """坐标系转换：检查并处理坐标系
        
        Args:
            df: 包含坐标的轨迹数据
            
        Returns:
            pd.DataFrame: 添加UTM坐标后的数据
        """
        print("🗺️ 坐标系检查和转换")
        
        df = df.copy()
        
        # 检查坐标范围判断是否已经是UTM坐标
        lat_range = df['latitude'].max() - df['latitude'].min()
        lon_range = df['longitude'].max() - df['longitude'].min()
        
        if lat_range > 1000 or lon_range > 1000:
            # 数据已经是UTM坐标格式
            print("  📍 检测到UTM坐标格式，直接使用")
            df['utm_x'] = df['longitude']
            df['utm_y'] = df['latitude']
        else:
            # 数据是WGS84经纬度，需要转换
            print("  🗺️ 检测到WGS84格式，转换为UTM")
            utm_x, utm_y = self.transformer.transform(
                df['longitude'].values, 
                df['latitude'].values
            )
            df['utm_x'] = utm_x
            df['utm_y'] = utm_y
        
        # 计算UTM速度分量（如果没有的话）
        if 'velocity_north_ms' not in df.columns or 'velocity_east_ms' not in df.columns:
            print("  🔄 计算UTM速度分量")
            # 计算位移
            dt = np.diff(df['time_s'].values)
            dx = np.diff(df['utm_x'].values)
            dy = np.diff(df['utm_y'].values)
            
            # 计算速度（添加第一个点的速度）
            velocity_x = np.concatenate([[0], dx / dt])
            velocity_y = np.concatenate([[0], dy / dt])
            
            df['velocity_north_ms'] = velocity_y  # UTM中Y轴指向北
            df['velocity_east_ms'] = velocity_x   # UTM中X轴指向东
        
        # 计算航向角
        df['heading_deg'] = np.degrees(np.arctan2(
            df['velocity_east_ms'], df['velocity_north_ms']
        )) % 360
        
        print(f"  ✅ 坐标转换完成")
        return df
    
    def query_environment_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """查询环境特征
        
        Args:
            df: 包含UTM坐标的轨迹数据
            
        Returns:
            pd.DataFrame: 添加环境特征后的数据
        """
        print("🌍 查询环境特征")
        
        df = df.copy()
        
        for env_key, env_data in self.environment_data.items():
            if env_data is None:
                print(f"  ⚠️ {env_key} 数据不可用，使用默认值")
                df[env_key] = self._get_default_env_value(env_key)
                continue
            
            try:
                # 将UTM坐标转换为栅格索引
                rows, cols = rasterio.transform.rowcol(
                    env_data['transform'],
                    df['utm_x'].values,
                    df['utm_y'].values
                )
                
                # 查询栅格值
                values = []
                for row, col in zip(rows, cols):
                    if (0 <= row < env_data['shape'][0] and 
                        0 <= col < env_data['shape'][1]):
                        values.append(env_data['data'][row, col])
                    else:
                        values.append(np.nan)
                
                df[env_key] = values
                
                # 统计有效值
                valid_count = sum(1 for v in values if not np.isnan(v))
                print(f"  ✅ {env_key}: {valid_count}/{len(values)} 有效值")
                
            except Exception as e:
                print(f"  ❌ {env_key} 查询失败: {e}")
                df[env_key] = self._get_default_env_value(env_key)
        
        # 后处理环境特征
        df = self._post_process_environment_features(df)
        
        return df
    
    def _get_default_env_value(self, env_key: str) -> float:
        """获取环境特征的默认值"""
        defaults = {
            'dem': 0.0,
            'slope': 0.0,
            'aspect': 0.0,
            'landcover': 30  # 草地
        }
        return defaults.get(env_key, 0.0)
    
    def _post_process_environment_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """后处理环境特征"""
        df = df.copy()
        
        # 处理土地覆盖类型
        if 'landcover' in df.columns:
            # 将无效值替换为草地（30）
            df['landcover'] = df['landcover'].fillna(30)
            
            # 创建土地覆盖名称列
            df['landcover_name'] = df['landcover'].map(
                self.config.landcover_mapping
            ).fillna('未分类')
        
        # 计算有效坡度（考虑运动方向）
        if all(col in df.columns for col in ['slope', 'aspect', 'heading_deg']):
            df['effective_slope'] = self._calculate_effective_slope(
                df['slope'], df['aspect'], df['heading_deg']
            )
        
        return df
    
    def _calculate_effective_slope(self, slope: pd.Series, aspect: pd.Series, heading: pd.Series) -> pd.Series:
        """计算有效坡度（考虑运动方向）"""
        # 计算坡向与运动方向的夹角
        angle_diff = np.abs(aspect - heading)
        angle_diff = np.minimum(angle_diff, 360 - angle_diff)  # 取较小的角度
        
        # 判断上坡还是下坡
        direction = np.where(angle_diff > 90, -1, 1)  # 上坡为负，下坡为正
        
        # 计算有效坡度
        effective_slope = direction * slope * np.abs(np.cos(np.radians(angle_diff)))
        
        return effective_slope
    
    def filter_trajectory(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤轨迹数据（去除异常值、长度检查等）"""
        print("🔍 轨迹数据过滤")
        
        original_length = len(df)
        
        # 长度检查
        if len(df) < self.config.min_trajectory_length:
            print(f"  ❌ 轨迹过短: {len(df)} < {self.config.min_trajectory_length}")
            return pd.DataFrame()
        
        if len(df) > self.config.max_trajectory_length:
            print(f"  ✂️ 轨迹过长，截断: {len(df)} -> {self.config.max_trajectory_length}")
            df = df.iloc[:self.config.max_trajectory_length].copy()
        
        # 速度异常值过滤
        speed_mask = (df['velocity_2d_ms'] >= 0) & (df['velocity_2d_ms'] <= self.config.max_speed)
        df = df[speed_mask].reset_index(drop=True)
        
        print(f"  ✅ 过滤完成: {original_length} -> {len(df)} 行")
        return df
    
    def process_single_trajectory(self, filepath: str) -> Optional[pd.DataFrame]:
        """处理单个轨迹文件的完整流程
        
        Args:
            filepath: 轨迹文件路径
            
        Returns:
            pd.DataFrame: 处理完成的轨迹数据，失败返回None
        """
        # 1. 加载原始数据
        df = self.load_single_trajectory(filepath)
        if df is None:
            return None
        
        # 2. 时间标准化
        df = self.standardize_trajectory_time(df)
        
        # 3. 坐标转换
        df = self.convert_coordinates(df)
        
        # 4. 环境特征查询
        df = self.query_environment_features(df)
        
        # 5. 数据过滤
        df = self.filter_trajectory(df)
        
        if len(df) == 0:
            print("  ❌ 轨迹处理后为空")
            return None
        
        # 6. 添加元信息
        df['trajectory_id'] = os.path.basename(filepath).replace('.csv', '')
        
        print(f"  ✅ 轨迹处理完成: {len(df)} 行")
        return df
    
    def process_all_trajectories(self, save_processed: bool = True) -> List[pd.DataFrame]:
        """处理所有轨迹文件
        
        Args:
            save_processed: 是否保存处理后的数据
            
        Returns:
            List[pd.DataFrame]: 处理完成的轨迹数据列表
        """
        print("🚀 开始批量处理轨迹数据")
        
        # 获取所有轨迹文件
        trajectory_files = self.load_trajectory_files()
        if not trajectory_files:
            print("❌ 未找到轨迹文件")
            return []
        
        processed_trajectories = []
        
        for i, filepath in enumerate(trajectory_files, 1):
            print(f"\n[{i}/{len(trajectory_files)}] 处理轨迹文件")
            
            df = self.process_single_trajectory(filepath)
            if df is not None:
                processed_trajectories.append(df)
                
                # 保存处理后的数据
                if save_processed:
                    output_file = os.path.join(
                        self.config.processed_data_dir,
                        f"processed_{os.path.basename(filepath)}"
                    )
                    df.to_csv(output_file, index=False)
                    print(f"  💾 已保存: {output_file}")
        
        print(f"\n✅ 批量处理完成: {len(processed_trajectories)}/{len(trajectory_files)} 成功")
        
        return processed_trajectories
    
    def get_trajectory_statistics(self, trajectories: List[pd.DataFrame]) -> Dict:
        """获取轨迹数据统计信息"""
        if not trajectories:
            return {}
        
        # 合并所有轨迹
        all_data = pd.concat(trajectories, ignore_index=True)
        
        stats = {
            'total_trajectories': len(trajectories),
            'total_points': len(all_data),
            'avg_trajectory_length': np.mean([len(df) for df in trajectories]),
            'speed_stats': {
                'mean': all_data['velocity_2d_ms'].mean(),
                'std': all_data['velocity_2d_ms'].std(),
                'min': all_data['velocity_2d_ms'].min(),
                'max': all_data['velocity_2d_ms'].max()
            },
            'environment_coverage': {
                env_key: (~all_data[env_key].isna()).sum() / len(all_data)
                for env_key in ['dem', 'slope', 'aspect', 'landcover']
                if env_key in all_data.columns
            }
        }
        
        return stats


if __name__ == "__main__":
    # 测试数据处理器
    processor = DataProcessor()
    
    # 处理所有轨迹
    trajectories = processor.process_all_trajectories()
    
    # 显示统计信息
    stats = processor.get_trajectory_statistics(trajectories)
    print(f"\n📊 数据统计:")
    print(f"轨迹数量: {stats.get('total_trajectories', 0)}")
    print(f"总数据点: {stats.get('total_points', 0)}")
    print(f"平均轨迹长度: {stats.get('avg_trajectory_length', 0):.1f}")
    
    if 'speed_stats' in stats:
        speed_stats = stats['speed_stats']
        print(f"速度统计: {speed_stats['mean']:.2f}±{speed_stats['std']:.2f} m/s") 