#!/usr/bin/env python3
"""
基于路径点的智能轨迹仿真器
- 使用现成的路径点序列
- 基于环境特征预测每个点的速度/状态
- 不做路径跟踪，直接沿路径点前进
- 参考window_correlation_analysis.py的方法
"""

import numpy as np
import pandas as pd
import joblib
import os
import json
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional
import rasterio
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 20
plt.rcParams['axes.labelsize'] = 20
plt.rcParams['axes.titlesize'] = 22

class PathBasedSimulator:
    """基于路径点的智能轨迹仿真器"""
    
    def __init__(self):
        """初始化仿真器"""
        self.path_points = []
        
        # 环境数据路径
        self.env_data_dir = 'trajectory_generator/data/environment'
        self.slope_file = os.path.join(self.env_data_dir, 'slope_aligned.tif')
        self.aspect_file = os.path.join(self.env_data_dir, 'aspect_aligned.tif') 
        self.landcover_file = os.path.join(self.env_data_dir, 'landcover_aligned.tif')
        
        # 土地覆盖类型映射（来自原脚本）
        self.landcover_mapping = {
            20: '林地',
            40: '灌木地', 
            60: '水体',
        }
        
        # 速度模型参数（来自原脚本，已优化）
        self.speed_models = {
            '林地': {
                'base_speed': 5.8,
                'slope_effect': {'uphill': -0.15, 'downhill': 0.08},
                'std_dev': 0.4,
                'max_speed': 8.0,
                'min_speed': 4.0,
                'transition_weight': 0.8
            },
            '灌木地': {
                'base_speed': 4.8,
                'slope_effect': {'uphill': -0.25, 'downhill': 0.12},
                'std_dev': 0.6,
                'max_speed': 6.5,
                'min_speed': 2.0,
                'transition_weight': 0.6
            },
            '水体': {
                'base_speed': 6.1,
                'slope_effect': {'uphill': -0.10, 'downhill': 0.15},
                'std_dev': 0.3,
                'max_speed': 7.5,
                'min_speed': 5.0,
                'transition_weight': 0.9
            }
        }
        
        logger.info("基于路径点的智能轨迹仿真器初始化完成")
    
    def load_path_from_trajectory(self, trajectory_file: str):
        """从轨迹文件加载路径点"""
        logger.info(f"从轨迹文件加载路径: {trajectory_file}")
        
        df = pd.read_csv(trajectory_file)
        
        # 注意：不要重命名坐标列！原始列名longitude和latitude实际包含UTM坐标
        # if 'latitude' in df.columns and 'longitude' in df.columns:
        #     df.rename(columns={'latitude': 'utm_x', 'longitude': 'utm_y'}, inplace=True)
        
        # 数据清洗：过滤无效坐标（使用原始列名）
        valid_mask = (
            (df['latitude'] > 1000000) & (df['latitude'] < 50000000) &
            (df['longitude'] > -50000000) & (df['longitude'] < 50000000)
        )
        df_clean = df[valid_mask].copy()
        
        if len(df_clean) == 0:
            raise ValueError("清洗后无有效路径点")
        
        # 保存路径点和真实数据用于对比
        self.path_points = df_clean.copy()
        
        logger.info(f"成功加载 {len(self.path_points)} 个路径点")
        return self.path_points
    
    def _get_environment_features(self, df):
        """获取环境特征数据（修复版本）"""
        # 准备坐标列表 - 使用原始列名（实际包含UTM坐标）
        coordinates = []
        for _, row in df.iterrows():
            # 注意：这里使用原始列名，但实际包含的是UTM坐标
            x = row['longitude']  # 实际是UTM-Y (northing)
            y = row['latitude']   # 实际是UTM-X (easting)
            coordinates.append((x, y))
        
        # 使用rasterio.sample方法获取环境数据（与原脚本一致）
        slope_values = self._get_raster_values(self.slope_file, coordinates)
        aspect_values = self._get_raster_values(self.aspect_file, coordinates)
        landcover_values = self._get_raster_values(self.landcover_file, coordinates)
        
        return slope_values, aspect_values, landcover_values
    
    def _get_raster_values(self, raster_path, coordinates):
        """从栅格文件中获取指定坐标的值（来自原脚本）"""
        values = []
        try:
            with rasterio.open(raster_path) as src:
                # coordinates 应该是 [(x1, y1), (x2, y2), ...]
                # rasterio.sample 返回一个生成器，需要转换为列表
                sampled_values = list(src.sample(coordinates))
                # 每个样本是一个包含单个值的数组，例如 [value]
                values = [val[0] if len(val) > 0 else np.nan for val in sampled_values]
        except Exception as e:
            print(f"读取栅格文件 {raster_path} 时出错: {e}")
            # 如果出错，返回NaN列表
            values = [np.nan] * len(coordinates)
        return values
    
    def _calculate_angle_difference(self, angle1, angle2):
        """计算两个角度之间的夹角（0-180度）"""
        angle1 = angle1 % 360
        angle2 = angle2 % 360
        diff = abs(angle1 - angle2)
        return diff if diff <= 180 else 360 - diff
    
    def _calculate_effective_slope(self, slope, aspect, heading):
        """计算有效坡度（考虑移动方向）"""
        if pd.isna(slope) or pd.isna(aspect) or pd.isna(heading):
            return np.nan
            
        angle_diff = self._calculate_angle_difference(aspect, heading)
        direction = -1 if angle_diff > 90 else 1
        return direction * slope * abs(np.cos(np.radians(angle_diff)))
    
    def _calculate_speed_from_environment(self, effective_slope, landcover, prev_speed=None, prev_landcover=None):
        """根据环境特征计算速度（来自原脚本的方法）"""
        landcover_type = self.landcover_mapping.get(landcover)
        if landcover_type is None:
            return np.nan
            
        model = self.speed_models[landcover_type]
        
        # 计算坡度影响
        slope_effect = (model['slope_effect']['uphill'] if effective_slope > 0 
                       else model['slope_effect']['downhill'])
        
        # 计算基础速度（考虑坡度影响）
        speed = model['base_speed'] + slope_effect * abs(effective_slope)
        
        # 添加随机扰动（减小扰动幅度）
        speed += np.random.normal(0, model['std_dev'] * 0.2)
        
        # 处理地类过渡
        if prev_speed is not None and prev_landcover is not None:
            prev_type = self.landcover_mapping.get(prev_landcover)
            if prev_type and prev_type != landcover_type:
                prev_model = self.speed_models[prev_type]
                curr_weight = model['transition_weight']
                prev_weight = prev_model['transition_weight']
                speed = (curr_weight * speed + prev_weight * prev_speed) / (curr_weight + prev_weight)
        
        # 速度平滑
        if prev_speed is not None:
            max_change = 0.5
            speed_change = speed - prev_speed
            if abs(speed_change) > max_change:
                speed = prev_speed + np.sign(speed_change) * max_change
            speed = 0.8 * speed + 0.2 * prev_speed
        
        # 限制速度范围
        speed = np.clip(speed, model['min_speed'], model['max_speed'])
        
        return speed
    
    def simulate_trajectory(self, use_custom_initial_state=False, initial_speed=None, noise_level=0.2):
        """沿路径点仿真轨迹"""
        if len(self.path_points) == 0:
            raise ValueError("请先加载路径点")
        
        logger.info("开始基于路径点的轨迹仿真")
        
        df = self.path_points.copy()
        
        # 获取环境特征
        slope_values, aspect_values, landcover_values = self._get_environment_features(df)
        
        # 计算航向角（使用真实轨迹的速度分量）
        df['heading'] = np.degrees(np.arctan2(
            df['velocity_east_ms'],
            df['velocity_north_ms']
        )) % 360
        
        # 计算有效坡度
        effective_slopes = []
        for i in range(len(df)):
            effective_slopes.append(self._calculate_effective_slope(
                slope_values[i], 
                aspect_values[i], 
                df['heading'].iloc[i]
            ))
        
        # 基于环境特征仿真速度
        simulated_speeds = []
        prev_speed = initial_speed if (use_custom_initial_state and initial_speed is not None) else None
        prev_landcover = None
        
        for i in range(len(df)):
            speed = self._calculate_speed_from_environment(
                effective_slopes[i],
                landcover_values[i],
                prev_speed,
                prev_landcover
            )
            
            # 添加随机扰动
            if not np.isnan(speed):
                speed += np.random.normal(0, noise_level)
                speed = max(0.1, speed)  # 确保速度为正
            
            simulated_speeds.append(speed)
            prev_speed = speed
            prev_landcover = landcover_values[i]
        
        # 对仿真速度进行平滑处理
        valid_speeds = [s for s in simulated_speeds if not np.isnan(s)]
        if len(valid_speeds) > 0:
            simulated_speeds_smooth = gaussian_filter1d(
                np.array([s if not np.isnan(s) else np.nanmean(valid_speeds) for s in simulated_speeds]), 
                sigma=1.0
            )
        else:
            simulated_speeds_smooth = simulated_speeds
        
        # 添加仿真结果到DataFrame
        df['simulated_speed'] = simulated_speeds_smooth
        df['slope'] = slope_values
        df['aspect'] = aspect_values  
        df['landcover'] = landcover_values
        df['effective_slope'] = effective_slopes
        
        # 计算其他物理量（时间、加速度等）
        df['real_speed'] = df['velocity_2d_ms']
        df['speed_difference'] = df['simulated_speed'] - df['real_speed']
        
        # 计算仿真的加速度（基于速度变化）
        df['simulated_acceleration'] = df['simulated_speed'].diff() / df['delta_time_s'].fillna(0.5)
        df['simulated_acceleration'] = df['simulated_acceleration'].fillna(0.0)
        
        # 统计信息
        valid_count = sum(1 for x in simulated_speeds if not np.isnan(x))
        logger.info(f"仿真完成，有效仿真点: {valid_count}/{len(df)}")
        logger.info(f"平均仿真速度: {np.nanmean(simulated_speeds):.2f} m/s")
        logger.info(f"平均真实速度: {df['real_speed'].mean():.2f} m/s")
        logger.info(f"速度相关系数: {df['simulated_speed'].corr(df['real_speed']):.3f}")
        
        self.simulated_trajectory = df
        return df
    
    def save_results(self, output_dir: str):
        """保存仿真结果"""
        if not hasattr(self, 'simulated_trajectory'):
            raise ValueError("请先运行仿真")
            
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存轨迹数据
        trajectory_file = os.path.join(output_dir, 'simulated_trajectory.csv')
        self.simulated_trajectory.to_csv(trajectory_file, index=False)
        logger.info(f"仿真轨迹已保存: {trajectory_file}")
        
        # 生成分析报告
        self._generate_analysis_report(output_dir)
    
    def _generate_analysis_report(self, output_dir: str):
        """生成分析报告和可视化"""
        df = self.simulated_trajectory
        
        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 速度对比
        axes[0, 0].plot(df.index, df['real_speed'], 'b-', label='真实速度', alpha=0.7)
        axes[0, 0].plot(df.index, df['simulated_speed'], 'r--', label='仿真速度', alpha=0.7)
        axes[0, 0].set_title('速度对比')
        axes[0, 0].set_xlabel('路径点索引')
        axes[0, 0].set_ylabel('速度 (m/s)')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 2. 轨迹对比
        axes[0, 1].plot(df['latitude'], df['longitude'], 'b-', label='真实轨迹', alpha=0.7)
        axes[0, 1].set_title('轨迹路径')
        axes[0, 1].set_xlabel('Latitude (UTM-X, m)')
        axes[0, 1].set_ylabel('Longitude (UTM-Y, m)')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        axes[0, 1].axis('equal')
        
        # 3. 速度差异分布
        speed_diff = df['speed_difference'].dropna()
        axes[1, 0].hist(speed_diff, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title(f'速度差异分布 (均值: {speed_diff.mean():.2f} m/s)')
        axes[1, 0].set_xlabel('速度差异 (m/s)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].grid(True)
        
        # 4. 环境因素分析
        valid_mask = ~np.isnan(df['effective_slope'])
        if valid_mask.sum() > 0:
            axes[1, 1].scatter(df.loc[valid_mask, 'effective_slope'], 
                             df.loc[valid_mask, 'simulated_speed'], 
                             alpha=0.6, s=10)
            axes[1, 1].set_title('有效坡度 vs 仿真速度')
            axes[1, 1].set_xlabel('有效坡度 (度)')
            axes[1, 1].set_ylabel('仿真速度 (m/s)')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'analysis_report.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 生成文本报告
        report = f"""# 基于路径点的轨迹仿真分析报告

## 基本统计
- 路径点总数: {len(df)}
- 平均真实速度: {df['real_speed'].mean():.2f} ± {df['real_speed'].std():.2f} m/s
- 平均仿真速度: {df['simulated_speed'].mean():.2f} ± {df['simulated_speed'].std():.2f} m/s
- 速度相关系数: {df['simulated_speed'].corr(df['real_speed']):.4f}

## 误差分析
- 平均速度差异: {df['speed_difference'].mean():.2f} m/s
- 速度差异标准差: {df['speed_difference'].std():.2f} m/s
- RMSE: {np.sqrt((df['speed_difference']**2).mean()):.2f} m/s

## 环境特征分析
- 有效环境数据点: {(~np.isnan(df['effective_slope'])).sum()}/{len(df)}
- 平均有效坡度: {df['effective_slope'].mean():.2f}°
- 主要地类分布: {dict(df['landcover'].value_counts().head(3))}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open(os.path.join(output_dir, 'analysis_report.md'), 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"分析报告已保存到: {output_dir}")

def main():
    """主函数：演示不同的仿真场景"""
    
    # 创建仿真器
    simulator = PathBasedSimulator()
    
    # 加载路径
    trajectory_file = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    simulator.load_path_from_trajectory(trajectory_file)
    
    # 场景1：默认仿真（完全基于环境特征）
    logger.info("\n=== 场景1：基于环境特征的默认仿真 ===")
    result1 = simulator.simulate_trajectory(noise_level=0.2)
    simulator.save_results('results/path_simulation_default')
    
    # 场景2：自定义初始速度
    logger.info("\n=== 场景2：自定义初始速度 ===")  
    result2 = simulator.simulate_trajectory(
        use_custom_initial_state=True, 
        initial_speed=8.0,  # 8 m/s初始速度
        noise_level=0.3
    )
    simulator.save_results('results/path_simulation_custom_speed')
    
    # 场景3：低噪声仿真
    logger.info("\n=== 场景3：低噪声精确仿真 ===")
    result3 = simulator.simulate_trajectory(noise_level=0.1)
    simulator.save_results('results/path_simulation_low_noise')
    
    logger.info("\n基于路径点的轨迹仿真测试完成！")

if __name__ == "__main__":
    main() 