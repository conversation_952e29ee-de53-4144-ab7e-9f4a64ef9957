"""
偏差监控和可视化模块
用于实时监控轨迹生成过程中的路径偏差情况
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class DeviationMonitor:
    """路径偏差监控器"""
    
    def __init__(self, config):
        self.config = config
        self.deviation_history = []
        self.deviation_levels = []
        self.timestamps = []
        
        # 偏差阈值
        self.thresholds = config.deviation_control
        
    def record_deviation(self, timestamp: float, cross_track_error: float, 
                        deviation_level: str, position: Tuple[float, float]):
        """记录偏差数据"""
        self.deviation_history.append(abs(cross_track_error))
        self.deviation_levels.append(deviation_level)
        self.timestamps.append(timestamp)
        
        # 实时警告
        if deviation_level in ['correction', 'emergency', 'termination']:
            logger.warning(f"时刻 {timestamp:.1f}s: 偏差 {abs(cross_track_error):.1f}m ({deviation_level})")
    
    def get_deviation_statistics(self) -> Dict:
        """获取偏差统计信息"""
        if not self.deviation_history:
            return {}
        
        deviations = np.array(self.deviation_history)
        
        stats = {
            'mean_deviation': np.mean(deviations),
            'max_deviation': np.max(deviations),
            'std_deviation': np.std(deviations),
            'median_deviation': np.median(deviations),
            '95_percentile': np.percentile(deviations, 95),
            'total_samples': len(deviations)
        }
        
        # 统计各级别偏差比例
        level_counts = {}
        for level in ['normal', 'warning', 'correction', 'emergency', 'termination']:
            count = self.deviation_levels.count(level)
            level_counts[f'{level}_count'] = count
            level_counts[f'{level}_ratio'] = count / len(self.deviation_levels) if self.deviation_levels else 0
        
        stats.update(level_counts)
        
        return stats
    
    def plot_deviation_analysis(self, save_path: str = None) -> None:
        """绘制偏差分析图表"""
        if not self.deviation_history:
            logger.warning("没有偏差数据可供绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('路径偏差分析报告', fontsize=16, fontweight='bold')
        
        # 1. 偏差时间序列
        ax1 = axes[0, 0]
        ax1.plot(self.timestamps, self.deviation_history, 'b-', linewidth=1, alpha=0.7, label='实际偏差')
        
        # 添加阈值线
        ax1.axhline(y=self.thresholds['warning_threshold'], color='orange', 
                   linestyle='--', label=f'警告阈值 ({self.thresholds["warning_threshold"]}m)')
        ax1.axhline(y=self.thresholds['correction_threshold'], color='red', 
                   linestyle='--', label=f'校正阈值 ({self.thresholds["correction_threshold"]}m)')
        ax1.axhline(y=self.thresholds['emergency_threshold'], color='darkred', 
                   linestyle='--', label=f'紧急阈值 ({self.thresholds["emergency_threshold"]}m)')
        
        ax1.set_xlabel('时间 (s)')
        ax1.set_ylabel('横向偏差 (m)')
        ax1.set_title('偏差时间序列')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 偏差分布直方图
        ax2 = axes[0, 1]
        ax2.hist(self.deviation_history, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(x=np.mean(self.deviation_history), color='red', linestyle='-', 
                   label=f'平均值: {np.mean(self.deviation_history):.1f}m')
        ax2.axvline(x=np.median(self.deviation_history), color='green', linestyle='-', 
                   label=f'中位数: {np.median(self.deviation_history):.1f}m')
        ax2.set_xlabel('横向偏差 (m)')
        ax2.set_ylabel('频次')
        ax2.set_title('偏差分布直方图')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 偏差级别饼图
        ax3 = axes[1, 0]
        level_counts = {}
        for level in ['normal', 'warning', 'correction', 'emergency', 'termination']:
            level_counts[level] = self.deviation_levels.count(level)
        
        # 只显示非零的级别
        non_zero_levels = {k: v for k, v in level_counts.items() if v > 0}
        
        if non_zero_levels:
            colors = {
                'normal': 'lightgreen',
                'warning': 'orange', 
                'correction': 'red',
                'emergency': 'darkred',
                'termination': 'black'
            }
            
            pie_colors = [colors[level] for level in non_zero_levels.keys()]
            ax3.pie(non_zero_levels.values(), labels=non_zero_levels.keys(), 
                   autopct='%1.1f%%', colors=pie_colors)
            ax3.set_title('偏差级别分布')
        
        # 4. 偏差统计表
        ax4 = axes[1, 1]
        ax4.axis('tight')
        ax4.axis('off')
        
        stats = self.get_deviation_statistics()
        
        table_data = [
            ['指标', '数值'],
            ['平均偏差', f"{stats.get('mean_deviation', 0):.2f} m"],
            ['最大偏差', f"{stats.get('max_deviation', 0):.2f} m"],
            ['标准差', f"{stats.get('std_deviation', 0):.2f} m"],
            ['中位数', f"{stats.get('median_deviation', 0):.2f} m"],
            ['95百分位', f"{stats.get('95_percentile', 0):.2f} m"],
            ['总样本数', f"{stats.get('total_samples', 0)}"],
            ['正常比例', f"{stats.get('normal_ratio', 0)*100:.1f}%"],
            ['警告比例', f"{stats.get('warning_ratio', 0)*100:.1f}%"],
            ['校正比例', f"{stats.get('correction_ratio', 0)*100:.1f}%"],
            ['紧急比例', f"{stats.get('emergency_ratio', 0)*100:.1f}%"]
        ]
        
        table = ax4.table(cellText=table_data, loc='center', cellLoc='left')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        
        # 设置表头样式
        for (i, j), cell in table.get_celld().items():
            if i == 0:
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
        
        ax4.set_title('偏差统计摘要')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"偏差分析图表已保存: {save_path}")
        
        plt.show()
    
    def is_deviation_acceptable(self) -> bool:
        """判断整体偏差是否可接受"""
        if not self.deviation_history:
            return True
        
        stats = self.get_deviation_statistics()
        
        # 评估标准
        criteria = {
            'max_deviation_ok': stats.get('max_deviation', 0) < self.thresholds['emergency_threshold'],
            'mean_deviation_ok': stats.get('mean_deviation', 0) < self.thresholds['warning_threshold'],
            'emergency_ratio_ok': stats.get('emergency_ratio', 0) < 0.05,  # 紧急情况 < 5%
            'termination_ratio_ok': stats.get('termination_ratio', 0) < 0.01  # 终止情况 < 1%
        }
        
        # 所有标准都满足才认为可接受
        all_acceptable = all(criteria.values())
        
        if not all_acceptable:
            logger.warning("偏差评估结果:")
            for criterion, result in criteria.items():
                status = "✓" if result else "✗"
                logger.warning(f"  {status} {criterion}: {result}")
        
        return all_acceptable
    
    def generate_deviation_report(self) -> str:
        """生成偏差报告"""
        stats = self.get_deviation_statistics()
        acceptable = self.is_deviation_acceptable()
        
        report = f"""
路径偏差分析报告
{'=' * 50}

基本统计:
- 平均偏差: {stats.get('mean_deviation', 0):.2f} m
- 最大偏差: {stats.get('max_deviation', 0):.2f} m
- 标准差: {stats.get('std_deviation', 0):.2f} m
- 95百分位: {stats.get('95_percentile', 0):.2f} m

偏差级别分布:
- 正常: {stats.get('normal_count', 0)} 次 ({stats.get('normal_ratio', 0)*100:.1f}%)
- 警告: {stats.get('warning_count', 0)} 次 ({stats.get('warning_ratio', 0)*100:.1f}%)
- 校正: {stats.get('correction_count', 0)} 次 ({stats.get('correction_ratio', 0)*100:.1f}%)
- 紧急: {stats.get('emergency_count', 0)} 次 ({stats.get('emergency_ratio', 0)*100:.1f}%)
- 终止: {stats.get('termination_count', 0)} 次 ({stats.get('termination_ratio', 0)*100:.1f}%)

总体评估: {'可接受' if acceptable else '需要优化'}

建议:
{self._generate_recommendations(stats)}
        """
        
        return report
    
    def _generate_recommendations(self, stats: Dict) -> str:
        """生成改进建议"""
        recommendations = []
        
        if stats.get('mean_deviation', 0) > self.thresholds['warning_threshold']:
            recommendations.append("- 考虑调整AI模型训练数据，增加路径跟踪样本")
            
        if stats.get('max_deviation', 0) > self.thresholds['emergency_threshold']:
            recommendations.append("- 增强偏差校正控制器的响应强度")
            
        if stats.get('emergency_ratio', 0) > 0.1:
            recommendations.append("- 优化路径规划，避免过于复杂的路径形状")
            
        if stats.get('std_deviation', 0) > 10.0:
            recommendations.append("- 提高控制系统的稳定性，减少偏差波动")
            
        if not recommendations:
            recommendations.append("- 当前偏差控制表现良好，可继续使用")
            
        return '\n'.join(recommendations)


def test_deviation_monitor():
    """测试偏差监控器"""
    from config import TrajectoryConfig
    
    config = TrajectoryConfig()
    monitor = DeviationMonitor(config)
    
    # 模拟一些偏差数据
    import random
    
    for i in range(100):
        timestamp = i * 0.5
        # 模拟偏差变化
        if i < 30:
            error = random.uniform(0, 10)  # 正常阶段
            level = 'normal'
        elif i < 60:
            error = random.uniform(5, 20)  # 轻微偏差
            level = 'warning' if error > 15 else 'normal'
        elif i < 80:
            error = random.uniform(15, 30)  # 中等偏差
            level = 'correction' if error > 25 else 'warning'
        else:
            error = random.uniform(25, 45)  # 严重偏差
            if error > 40:
                level = 'emergency'
            else:
                level = 'correction'
        
        monitor.record_deviation(timestamp, error, level, (i*10, i*5))
    
    # 生成分析
    monitor.plot_deviation_analysis()
    print(monitor.generate_deviation_report())


if __name__ == "__main__":
    test_deviation_monitor() 