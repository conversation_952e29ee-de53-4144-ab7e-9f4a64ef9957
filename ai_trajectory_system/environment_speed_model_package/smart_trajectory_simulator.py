#!/usr/bin/env python3
"""
基于加速度驱动的智能轨迹仿真器
- 环境感知 → 速度/加速度决策
- 控制器 → 车辆运动学更新
- 支持任意初始状态配置
"""

import numpy as np
import pandas as pd
import joblib
import os
import json
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional
import warnings

# 导入当前目录下的模块
from environment_speed_model import EnvironmentSpeedModel
from config import Config

warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartTrajectorySimulator:
    """
    基于AI决策与经典控制结合的智能轨迹仿真器
    """
    
    def __init__(self, config: Config, environment_speed_model: EnvironmentSpeedModel):
        """
        初始化仿真器
        :param config: 配置对象
        :param environment_speed_model: 预先加载的环境速度模型
        """
        self.config = config
        self.env_speed_model = environment_speed_model
        
        # 仿真状态
        self.path_points = []
        self.current_state = {}
        self.trajectory_history = []
        self.current_path_index = 0
        
        # 车辆物理参数
        self.dt = config.simulation_config.get("dt", 0.5)
        self.max_acceleration = config.simulation_config.get("max_acceleration", 3.0)
        self.max_deceleration = config.simulation_config.get("max_deceleration", -4.0)
        self.max_speed = config.simulation_config.get("max_speed", 15.0)
        self.max_steer_angle = np.radians(30)  # 最大转向角
        self.wheelbase = 2.9  # 车辆轴距 (米)

        # PID 纵向控制器参数
        self.pid_kp = 1.2
        self.pid_ki = 0.1
        self.pid_kd = 0.3
        self.pid_integral = 0
        self.pid_previous_error = 0
        
        logger.info("智能轨迹仿真器初始化完成")
    
    def set_initial_state(self, utm_x: float, utm_y: float, speed_2d: float, heading_deg: float, timestamp: float):
        """设置仿真器的任意初始状态"""
        self.current_state = {
            'timestamp': timestamp,
            'x': utm_x,
            'y': utm_y, 
            'speed': speed_2d,
            'heading': np.radians(heading_deg),
        }
        self.trajectory_history = [self.current_state.copy()]
        self.current_path_index = 0
        self.pid_integral = 0
        self.pid_previous_error = 0
        logger.info(f"设置初始状态: 位置({utm_x:.2f}, {utm_y:.2f}), "
                    f"速度={speed_2d:.2f} m/s, 航向={heading_deg:.2f}°")
    
    def set_path_from_dataframe(self, path_df: pd.DataFrame):
        """从DataFrame加载引导路径"""
        if 'utm_x' not in path_df.columns or 'utm_y' not in path_df.columns:
            raise ValueError("路径DataFrame缺少'utm_x'或'utm_y'列")
        
        self.path_points = path_df[['utm_x', 'utm_y']].values
        logger.info(f"成功加载 {len(self.path_points)} 个引导路径点")
    
    def _get_target_point_and_context(self) -> Tuple[Optional[np.ndarray], Dict]:
        """计算前瞻目标点及路径上下文特征"""
        if self.current_path_index >= len(self.path_points) - 1:
            return None, {}

        # 找到路径上距离车辆最近的点
        current_pos = np.array([self.current_state['x'], self.current_state['y']])
        distances = np.linalg.norm(self.path_points[self.current_path_index:] - current_pos, axis=1)
        nearest_idx_relative = np.argmin(distances)
        self.current_path_index += nearest_idx_relative

        # 前瞻距离 (Lookahead Distance)，与车速相关
        lookahead_dist = self.config.simulation_config.get("lookahead_base", 3.0) + \
                         self.current_state['speed'] * self.config.simulation_config.get("lookahead_ratio", 0.5)

        # 找到前瞻目标点
        target_idx = self.current_path_index
        dist_sum = 0
        while dist_sum < lookahead_dist and target_idx < len(self.path_points) - 1:
            dist_sum += np.linalg.norm(self.path_points[target_idx+1] - self.path_points[target_idx])
            target_idx += 1
        
        target_point = self.path_points[target_idx]

        # 提取用于AI决策的上下文特征
        # 在此简化，实际应提取更丰富的特征，如真实曲率、坡度等
        context = {
            'x_coord': self.current_state['x'],
            'y_coord': self.current_state['y'],
            'distance_from_start': np.linalg.norm(current_pos - self.path_points[0]),
            'curvature': 0.01, # 简化值
            'path_angle': 0, # 简化值
            'turning_rate': 0, # 简化值
            'complexity': 0.1, # 简化值
            'elevation_change': 0.0, # 简化值
            'time_progress': self.current_state['timestamp'] - self.trajectory_history[0]['timestamp']
        }
        return target_point, context

    def _longitudinal_control(self, target_speed: float) -> float:
        """纵向PID控制器，计算加速度"""
        error = target_speed - self.current_state['speed']
        self.pid_integral += error * self.dt
        derivative = (error - self.pid_previous_error) / self.dt
        
        acceleration = (self.pid_kp * error) + \
                       (self.pid_ki * self.pid_integral) + \
                       (self.pid_kd * derivative)
        
        self.pid_previous_error = error
        
        # 限制加速度范围
        return np.clip(acceleration, self.max_deceleration, self.max_acceleration)

    def _lateral_control(self, target_point: np.ndarray) -> float:
        """横向Pure Pursuit控制器，计算转向角"""
        target_vec = target_point - np.array([self.current_state['x'], self.current_state['y']])
        target_angle = np.arctan2(target_vec[1], target_vec[0])
        
        alpha = target_angle - self.current_state['heading']
        # 归一化角度差
        alpha = np.arctan2(np.sin(alpha), np.cos(alpha))

        lookahead_dist = np.linalg.norm(target_vec)
        
        steer_angle = np.arctan2(2 * self.wheelbase * np.sin(alpha), lookahead_dist)
        
        # 限制转向角范围
        return np.clip(steer_angle, -self.max_steer_angle, self.max_steer_angle)

    def simulate_step(self) -> bool:
        """模拟单个时间步"""
        # 1. 获取目标点和环境上下文
        target_point, context = self._get_target_point_and_context()
        if target_point is None:
            logger.info("已到达路径终点")
            return False

        # 2. AI决策：预测目标速度
        ai_state = {
            'utm_x': self.current_state['x'],
            'utm_y': self.current_state['y']
        }
        target_speed = self.env_speed_model.predict_speed(ai_state, context)

        # 3. 经典控制：计算控制指令
        acceleration = self._longitudinal_control(target_speed)
        steer_angle = self._lateral_control(target_point)

        # 4. 物理更新：应用车辆运动学模型
        prev_state = self.current_state
        speed = prev_state['speed']
        heading = prev_state['heading']

        new_speed = speed + acceleration * self.dt
        new_speed = np.clip(new_speed, 0, self.max_speed) # 速度不能为负
        
        # 防止速度过快时转向过大
        if new_speed > 0.1:
            try:
                # Add a small epsilon to avoid division by zero if tan(steer_angle) is zero
                tan_steer = np.tan(steer_angle)
                if np.abs(tan_steer) < 1e-8:
                    tan_steer = 1e-8
                turn_radius = self.wheelbase / tan_steer
                heading_rate = new_speed / turn_radius
            except RuntimeWarning:
                 heading_rate = 0
        else:
            heading_rate = 0

        new_heading = heading + heading_rate * self.dt
        # 归一化航向角
        new_heading = np.arctan2(np.sin(new_heading), np.cos(new_heading))
        
        # 使用平均速度更新位置，提高精度
        avg_speed = (speed + new_speed) / 2
        new_x = prev_state['x'] + avg_speed * np.cos(new_heading) * self.dt
        new_y = prev_state['y'] + avg_speed * np.sin(new_heading) * self.dt
        
        self.current_state = {
            'timestamp': prev_state['timestamp'] + self.dt,
            'x': new_x,
            'y': new_y,
            'speed': new_speed,
            'heading': new_heading,
            'acceleration': acceleration,
            'steer_angle': steer_angle
        }
        self.trajectory_history.append(self.current_state.copy())
            
        return True
    
    def run_simulation(self, duration_s: float) -> List[Dict]:
        """
        运行完整仿真
        :param duration_s: 仿真持续时间（秒）
        """
        num_steps = int(duration_s / self.dt)
        logger.info(f"开始运行仿真，总步数: {num_steps}")
        
        for step in range(num_steps):
            if not self.simulate_step():
                break
            if (step + 1) % 200 == 0:
                logger.info(f"仿真进行中... 步数: {step+1}/{num_steps}")
        
        logger.info(f"仿真完成，共生成 {len(self.trajectory_history)} 个轨迹点")
        return self.trajectory_history


def main():
    """测试加速度驱动仿真器"""
    #
    # This main function is for standalone testing and is not used
    # by the main analysis script. The constructor requires arguments
    # that are not provided here, so it would fail if run directly.
    # We leave it for potential future testing with modifications.
    #
    # config = Config()
    # env_speed_model = EnvironmentSpeedModel(config)
    # env_speed_model.load_model()
    # simulator = SmartTrajectorySimulator(config, env_speed_model)
    
    # # 设置引导路径
    # trajectory_file = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    # simulator.set_path_from_dataframe(pd.read_csv(trajectory_file))
    
    # # 测试场景1：使用真实轨迹的初始状态
    # print("\n=== 测试场景 1：真实初始状态 ===")
    # df_real = pd.read_csv(trajectory_file)
    # first_row = df_real.iloc[0]
    # simulator.set_initial_state(
    #     utm_x=first_row['utm_x'],
    #     utm_y=first_row['utm_y'],
    #     speed_2d=first_row['velocity_2d_ms'],
    #     heading_deg=first_row['heading_deg'],
    #     timestamp=first_row['timestamp_s']
    # )
    
    # # 运行仿真
    # results = simulator.run_simulation(duration_s=800)
    
    # print("\n加速度驱动仿真测试完成！")
    pass


if __name__ == "__main__":
    main() 