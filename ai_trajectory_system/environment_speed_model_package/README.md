# 环境-速度模型包

这个包包含了用于预测车辆在不同环境条件下理想速度的模型及相关代码。

## 文件说明

- `environment_speed_model.py` - 环境-速度模型的核心实现，包含特征提取、模型训练和预测功能
- `test_environment_model.py` - 用于测试环境-速度模型的脚本，包括模型训练、预测测试和可视化分析
- `smart_trajectory_simulator.py` - 基于环境-速度模型的智能轨迹仿真器，用于生成动态轨迹
- `config.py` - 系统配置文件
- `models/` - 包含训练好的模型文件
  - `environment_speed_model.pkl` - 训练好的随机森林模型，用于预测环境条件下的理想速度

## 模型原理

环境-速度模型是一个随机森林回归模型，通过学习真实轨迹数据中环境因素与速度的关系，能够预测在给定环境条件下的理想行驶速度。主要特征包括：

- 路径几何特征：曲率、路径角度、转弯率等
- 位置特征：坐标、与起点距离等
- 时间特征：时间进度等
- 环境复杂度：局部复杂度、坡度等

## 使用方法

### 训练模型

```python
from environment_speed_model import train_environment_speed_model

# 训练模型并保存
model, results = train_environment_speed_model()
```

### 使用模型预测速度

```python
from environment_speed_model import EnvironmentSpeedModel
from config import Config

# 初始化模型
config = Config()
model = EnvironmentSpeedModel(config)

# 加载已训练的模型
model.load_model("models/environment_speed_model.pkl")

# 预测速度
current_state = {
    'utm_x': 4.4491815e+06,
    'utm_y': 4.0639588e+05
}
path_context = {
    'curvature': 0.001,
    'path_angle': 0.0,
    'turning_rate': 0.0,
    'complexity': 0.1
}
predicted_speed = model.predict_speed(current_state, path_context)
print(f"预测速度: {predicted_speed} m/s")
```

### 运行轨迹仿真

```python
from smart_trajectory_simulator import AccelerationDrivenSimulator

# 初始化仿真器
simulator = AccelerationDrivenSimulator()

# 设置初始状态
simulator.set_initial_state(
    utm_x=4.4491815e+06,
    utm_y=4.0639588e+05,
    speed_2d=5.0,
    heading_deg=45.0
)

# 设置路径
simulator.set_path_from_real_trajectory("path/to/trajectory.csv")

# 运行仿真
trajectory = simulator.run_simulation(max_steps=500)

# 保存结果
simulator.save_results("results/simulation_output")
``` 