#!/usr/bin/env python3
"""
环境-速度模型使用示例
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from environment_speed_model import EnvironmentSpeedModel
from config import Config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demonstrate_model_prediction():
    """演示模型预测功能"""
    logger.info("=== 环境-速度模型使用示例 ===")
    
    # 初始化模型
    config = Config()
    model = EnvironmentSpeedModel(config)
    
    # 加载已训练的模型
    if os.path.exists("models/environment_speed_model.pkl"):
        model.load_model("models/environment_speed_model.pkl")
        logger.info("成功加载环境-速度模型")
    else:
        logger.error("模型文件不存在，请先运行训练脚本")
        return
    
    # 创建测试场景
    logger.info("创建不同环境条件的测试场景...")
    
    # 基础状态
    base_state = {
        'utm_x': 4.4491815e+06,
        'utm_y': 4.0639588e+05,
        'speed_2d': 5.0,
        'heading': 0.0
    }
    
    # 测试不同曲率对速度的影响
    curvatures = np.linspace(0, 0.01, 20)  # 从直线到弯道
    predicted_speeds = []
    
    for curvature in curvatures:
        # 创建路径上下文
        path_context = {
            'curvature': curvature,
            'path_angle': 0.0,
            'turning_rate': curvature * 5,  # 转弯率与曲率相关
            'complexity': curvature * 30,   # 复杂度与曲率相关
            'elevation_change': 0.0,
            'time_progress': 50.0,
            'start_x': base_state['utm_x'],
            'start_y': base_state['utm_y']
        }
        
        # 预测速度
        speed = model.predict_speed(base_state, path_context)
        predicted_speeds.append(speed)
        
        logger.info(f"曲率: {curvature:.4f}, 预测速度: {speed:.2f} m/s")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.plot(curvatures * 1000, predicted_speeds, 'b-o', linewidth=2)
    plt.xlabel('路径曲率 (1/km)')
    plt.ylabel('预测速度 (m/s)')
    plt.title('环境-速度模型: 曲率对速度的影响')
    plt.grid(True)
    
    # 保存结果
    os.makedirs("results", exist_ok=True)
    plt.savefig('results/curvature_speed_example.png', dpi=300)
    logger.info("结果已保存: results/curvature_speed_example.png")
    
    # 测试不同复杂度对速度的影响
    complexities = np.linspace(0, 1.0, 20)  # 从简单到复杂
    predicted_speeds_complexity = []
    
    for complexity in complexities:
        # 创建路径上下文
        path_context = {
            'curvature': 0.002,  # 固定曲率
            'path_angle': 0.0,
            'turning_rate': 0.01,
            'complexity': complexity,  # 变化的复杂度
            'elevation_change': 0.0,
            'time_progress': 50.0,
            'start_x': base_state['utm_x'],
            'start_y': base_state['utm_y']
        }
        
        # 预测速度
        speed = model.predict_speed(base_state, path_context)
        predicted_speeds_complexity.append(speed)
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.plot(complexities, predicted_speeds_complexity, 'r-o', linewidth=2)
    plt.xlabel('路径复杂度')
    plt.ylabel('预测速度 (m/s)')
    plt.title('环境-速度模型: 复杂度对速度的影响')
    plt.grid(True)
    
    # 保存结果
    plt.savefig('results/complexity_speed_example.png', dpi=300)
    logger.info("结果已保存: results/complexity_speed_example.png")
    
    logger.info("示例运行完成！")

if __name__ == "__main__":
    demonstrate_model_prediction() 