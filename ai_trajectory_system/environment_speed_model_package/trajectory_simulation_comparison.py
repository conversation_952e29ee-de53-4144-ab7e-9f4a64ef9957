#!/usr/bin/env python3
"""
轨迹仿真与真实轨迹对比分析
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
from typing import Dict, List, Tuple

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from smart_trajectory_simulator import SmartTrajectorySimulator
from environment_speed_model import EnvironmentSpeedModel
from config import Config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrajectoryComparisonAnalyzer:
    """轨迹对比分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.config = Config()
        
        # 加载环境-速度模型
        self.env_speed_model = EnvironmentSpeedModel(self.config)
        model_path = self.config.model_path
        if os.path.exists(model_path):
            self.env_speed_model.load_model(model_path)
            logger.info(f"环境-速度模型已从 {model_path} 加载")
        else:
            raise FileNotFoundError(f"模型文件未找到: {model_path}，请先运行训练。")

        # 初始化仿真器
        self.simulator = SmartTrajectorySimulator(self.config, self.env_speed_model)
        
        # 创建结果目录
        os.makedirs("results/trajectory_comparison", exist_ok=True)
    
    def load_real_trajectory(self, trajectory_file: str) -> pd.DataFrame:
        """加载真实轨迹数据"""
        logger.info(f"加载真实轨迹: {trajectory_file}")
        
        try:
            df = pd.read_csv(trajectory_file)
            required_columns = ['timestamp_ms', 'latitude', 'longitude', 'velocity_2d_ms', 'heading_deg']
            if any(col not in df.columns for col in required_columns):
                raise ValueError(f"轨迹文件缺少必要的列: {required_columns}")
            
            df.rename(columns={'latitude': 'utm_y', 'longitude': 'utm_x'}, inplace=True)
            df['timestamp_s'] = df['timestamp_ms'] / 1000.0
            
            dx = df['utm_x'].diff()
            dy = df['utm_y'].diff()
            df['segment_distance'] = np.sqrt(dx**2 + dy**2)
            df['cumulative_distance'] = df['segment_distance'].cumsum().fillna(0)
            
            logger.info(f"成功加载轨迹，共 {len(df)} 个点，总距离 {df['cumulative_distance'].iloc[-1]:.2f} 米")
            return df
            
        except Exception as e:
            logger.error(f"加载轨迹文件失败: {e}")
            return None
    
    def simulate_trajectory(self, real_trajectory: pd.DataFrame) -> List[Dict]:
        """基于真实轨迹的初始状态和路径进行仿真"""
        logger.info("开始轨迹仿真...")
        
        initial_state = real_trajectory.iloc[0]
        
        self.simulator.set_initial_state(
            utm_x=initial_state['utm_x'],
            utm_y=initial_state['utm_y'],
            speed_2d=initial_state['velocity_2d_ms'],
            heading_deg=initial_state['heading_deg'],
            timestamp=initial_state['timestamp_s']
        )
        
        self.simulator.set_path_from_dataframe(real_trajectory)
        
        simulation_duration = real_trajectory['timestamp_s'].iloc[-1] - real_trajectory['timestamp_s'].iloc[0]
        simulated_trajectory = self.simulator.run_simulation(duration_s=simulation_duration)
        
        logger.info(f"仿真完成，生成 {len(simulated_trajectory)} 个轨迹点")
        return simulated_trajectory
    
    def compare_trajectories(self, real_trajectory: pd.DataFrame, simulated_trajectory: List[Dict], 
                             output_prefix: str = "trajectory_comparison"):
        """比较真实轨迹和仿真轨迹"""
        logger.info("开始轨迹对比分析...")
        
        sim_df = pd.DataFrame(simulated_trajectory)
        if sim_df.empty:
            logger.warning(f"仿真轨迹为空，跳过对比: {output_prefix}")
            return
            
        sim_df.rename(columns={'x': 'utm_x', 'y': 'utm_y', 'speed': 'velocity_2d_ms'}, inplace=True)
        
        real_df = real_trajectory.copy()
        
        sim_df['time_diff'] = sim_df['timestamp'] - sim_df['timestamp'].iloc[0]
        real_df['time_diff'] = real_df['timestamp_s'] - real_df['timestamp_s'].iloc[0]
        
        self._plot_trajectory_comparison(real_df, sim_df, output_prefix)
        self._plot_speed_comparison(real_df, sim_df, output_prefix)
        self._calculate_comparison_metrics(real_df, sim_df, output_prefix)
    
    def _plot_trajectory_comparison(self, real_df: pd.DataFrame, sim_df: pd.DataFrame, output_prefix: str):
        """生成轨迹对比图"""
        plt.figure(figsize=(12, 10))
        plt.plot(real_df['utm_x'], real_df['utm_y'], 'b-', linewidth=2, label='真实轨迹')
        plt.plot(sim_df['utm_x'], sim_df['utm_y'], 'r--', linewidth=2, label='仿真轨迹')
        plt.scatter(real_df['utm_x'].iloc[0], real_df['utm_y'].iloc[0], c='g', s=100, marker='o', label='起点', zorder=5)
        plt.xlabel('UTM X (m)'); plt.ylabel('UTM Y (m)')
        plt.title('真实轨迹 vs 仿真轨迹'); plt.legend(); plt.grid(True); plt.axis('equal')
        
        output_path = f'results/trajectory_comparison/{output_prefix}_path.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        logger.info(f"轨迹对比图已保存: {output_path}"); plt.close()
    
    def _plot_speed_comparison(self, real_df: pd.DataFrame, sim_df: pd.DataFrame, output_prefix: str):
        """生成速度对比图"""
        plt.figure(figsize=(12, 6))
        plt.plot(real_df['time_diff'], real_df['velocity_2d_ms'], 'b-', linewidth=2, label='真实速度')
        plt.plot(sim_df['time_diff'], sim_df['velocity_2d_ms'], 'r--', linewidth=2, label='仿真速度')
        plt.xlabel('时间 (秒)'); plt.ylabel('速度 (m/s)')
        plt.title('速度对比'); plt.legend(); plt.grid(True)
        
        output_path = f'results/trajectory_comparison/{output_prefix}_speed.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        logger.info(f"速度对比图已保存: {output_path}"); plt.close()
    
    def _calculate_comparison_metrics(self, real_df: pd.DataFrame, sim_df: pd.DataFrame, output_prefix: str):
        """计算并保存对比指标"""
        # 使用插值使两个df在相同时间点上对齐
        merged_df = pd.merge_asof(sim_df.sort_values('time_diff'), 
                                  real_df.sort_values('time_diff'), 
                                  on='time_diff', 
                                  direction='nearest',
                                  suffixes=('_sim', '_real'))
        
        path_deviation = np.linalg.norm(merged_df[['utm_x_sim', 'utm_y_sim']].values - \
                                        merged_df[['utm_x_real', 'utm_y_real']].values, axis=1)
        speed_diff = np.abs(merged_df['velocity_2d_ms_sim'] - merged_df['velocity_2d_ms_real'])
        
        metrics = {
            "平均轨迹偏差(米)": np.mean(path_deviation),
            "最大轨迹偏差(米)": np.max(path_deviation),
            "平均速度差异(m/s)": np.mean(speed_diff),
            "最大速度差异(m/s)": np.max(speed_diff),
        }
        
        output_path = f'results/trajectory_comparison/{output_prefix}_metrics.txt'
        with open(output_path, 'w') as f:
            f.write("轨迹对比分析指标\n" + "="*30 + "\n")
            for key, value in metrics.items():
                f.write(f"{key}: {value:.4f}\n")
        logger.info(f"对比指标已保存: {output_path}")

    def run_analysis_for_trajectory(self, trajectory_file: str):
        """对单个轨迹文件运行完整分析"""
        trajectory_name = os.path.basename(trajectory_file).replace('.csv', '')
        logger.info(f"\n--- 开始分析轨迹: {trajectory_name} ---")
        
        real_trajectory = self.load_real_trajectory(trajectory_file)
        if real_trajectory is None: return

        simulated_trajectory = self.simulate_trajectory(real_trajectory)
        if not simulated_trajectory: return

        self.compare_trajectories(real_trajectory, simulated_trajectory, trajectory_name)
        logger.info(f"--- 轨迹 {trajectory_name} 分析完成 ---")
    
    def run_batch_analysis(self, trajectory_dir: str):
        """批量分析目录中的所有轨迹"""
        logger.info(f"=== 开始批量分析轨迹目录: {trajectory_dir} ===")
        
        trajectory_files = [os.path.join(trajectory_dir, f) for f in os.listdir(trajectory_dir) 
                            if f.endswith('.csv')]
        
        if not trajectory_files:
            logger.error(f"在 {trajectory_dir} 中未找到轨迹文件"); return
        
        for file in trajectory_files:
            self.run_analysis_for_trajectory(file)
        
        logger.info("=== 批量分析完成 ===")

def main():
    """主函数"""
    analyzer = TrajectoryComparisonAnalyzer()
    
    # 查找项目根目录（假设此脚本位于 '.../ai_agent_generation/ai_trajectory_system/...' 中）
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 从当前脚本位置向上查找，直到找到 'ai_agent_generation'
        project_root = current_dir
        while os.path.basename(project_root) != 'ai_agent_generation':
            project_root = os.path.dirname(project_root)
            if project_root == '/': # 到达文件系统根目录，防止死循环
                raise FileNotFoundError("无法定位项目根目录 'ai_agent_generation'")
        
        trajectory_dir = os.path.join(project_root, "trajectories")
    except Exception:
        # 如果自动查找失败，则回退到简单的相对路径
        logger.warning("自动查找项目根目录失败，将使用简单的相对路径。")
        trajectory_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../../trajectories")

    if not os.path.exists(trajectory_dir):
        logger.error(f"轨迹目录未找到: {trajectory_dir}")
        # 尝试备用路径
        alt_trajectory_dir = "trajectories"
        if os.path.exists(alt_trajectory_dir):
            logger.info(f"找到备用轨迹目录: {alt_trajectory_dir}")
            trajectory_dir = alt_trajectory_dir
        else:
            logger.error(f"备用轨迹目录也未找到: {alt_trajectory_dir}")
            return

    analyzer.run_batch_analysis(trajectory_dir)

if __name__ == "__main__":
    main()
 