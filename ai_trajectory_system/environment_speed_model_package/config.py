#!/usr/bin/env python3
"""
配置文件
"""

class Config:
    """系统配置类"""
    
    def __init__(self):
        # 模型配置
        self.model_path = "models/environment_speed_model.pkl"
        
        # 环境-速度模型配置
        self.random_forest_config = {
            "n_estimators": 100,
            "max_depth": 15,
            "min_samples_split": 10,
            "min_samples_leaf": 5,
            "random_state": 42,
            "n_jobs": -1
        }
        
        # 轨迹仿真配置
        self.simulation_config = {
            "dt": 0.5,  # 时间步长(秒)
            "max_acceleration": 3.0,  # 最大加速度 (m/s²)
            "max_deceleration": -4.0,  # 最大减速度 (m/s²)
            "max_speed": 15.0,  # 最大速度 (m/s)
            "noise_std": 0.1  # 加速度噪声标准差
        }
        
        # 数据路径配置
        self.data_paths = [
            "../trajectory_generator/data/trajectories",
            "trajectory_generator/data/trajectories",
            "data/trajectories"
        ]
        
        # 结果保存路径
        self.results_dir = "results"
        
        # 日志配置
        self.log_level = "INFO" 