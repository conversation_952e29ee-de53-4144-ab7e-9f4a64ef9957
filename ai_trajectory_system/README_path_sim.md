# 基于路径点的智能轨迹仿真器算法说明

## 1. 设计目标与适用场景
本模块旨在实现"基于现成路径点、环境驱动的速度仿真"，适用于如下场景：
- 已有高精度轨迹点（如实测或高精地图），无需路径跟踪算法
- 关注环境（坡度、地表类型等）对速度的影响
- 需要对仿真结果进行统计分析与可视化

## 2. 核心算法流程
1. **数据加载**：
   - 读取轨迹CSV，直接使用原始`latitude`（UTM-X）、`longitude`（UTM-Y）列
   - 过滤无效点
2. **环境特征提取**：
   - 通过`rasterio.sample`方法，按UTM坐标从坡度、坡向、土地覆盖栅格中提取环境特征
3. **速度仿真**：
   - 按点遍历，基于地表类型、坡度、历史速度等，使用经验模型计算仿真速度
   - 支持地类过渡、速度平滑、随机扰动
4. **结果输出**：
   - 保存仿真速度、环境特征、误差等到CSV
   - 自动生成统计分析报告和可视化图片

## 3. 与window_correlation_analysis.py的异同
- **相同点**：
  - 都是基于路径点、环境特征驱动的速度仿真
  - 速度模型、地类映射、坡度处理等核心逻辑一致
- **不同点**：
  - 本模块不做窗口相关性分析，专注于逐点仿真与可视化
  - 结构更清晰，便于集成到更大系统
  - 输出更适合后续AI/控制/运动学分析

## 4. 主要参数与可扩展性
- 地表类型映射、速度模型参数可灵活调整
- 支持自定义初始速度、噪声水平
- 可扩展更多环境特征（如气象、障碍物等）
- 支持多场景批量仿真

## 5. 典型流程图
```mermaid
graph TD
    A[加载轨迹数据] --> B[提取环境特征]
    B --> C[逐点仿真速度]
    C --> D[保存仿真结果]
    D --> E[生成分析报告/可视化]
```

## 6. 结果解读建议
- 关注速度相关系数、RMSE等指标衡量仿真合理性
- 可结合地类分布、坡度分布分析模型表现
- 典型应用：环境敏感性分析、速度规划策略评估、AI控制器训练数据生成 