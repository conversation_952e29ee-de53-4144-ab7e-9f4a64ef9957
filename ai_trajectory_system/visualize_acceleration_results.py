#!/usr/bin/env python3
"""
加速度驱动仿真结果可视化
- 轨迹对比图
- 速度对比图
- 加速度对比图
- 横向偏差分析图
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import argparse
from pathlib import Path

# 解决中文字体显示问题
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 20  # 设置默认字体大小为20号

def load_data(sim_file, real_file=None):
    """加载仿真和真实轨迹数据"""
    # 加载仿真数据
    sim_df = pd.read_csv(sim_file)
    
    # 加载真实轨迹数据
    if real_file is None:
        real_file = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    
    real_df = pd.read_csv(real_file)
    
    # 修正真实轨迹的坐标列名
    if 'latitude' in real_df.columns:
        real_df.rename(columns={'latitude': 'utm_x', 'longitude': 'utm_y'}, inplace=True)
    
    # 数据清洗
    valid_mask = (
        (real_df['utm_x'] > 1000000) & (real_df['utm_x'] < 50000000) &
        (real_df['utm_y'] > -50000000) & (real_df['utm_y'] < 50000000)
    )
    real_df = real_df[valid_mask].copy()
    
    # 时间对齐：将真实轨迹的时间戳转换为相对时间
    real_df['time_relative'] = (real_df['timestamp_ms'] / 1000.0 - real_df['timestamp_ms'].iloc[0] / 1000.0)
    sim_df['time_relative'] = sim_df['timestamp'] - sim_df['timestamp'].iloc[0]
    
    return sim_df, real_df

def create_trajectory_comparison(sim_df, real_df, output_dir):
    """创建轨迹对比图"""
    plt.figure(figsize=(12, 8))
    
    # 绘制轨迹
    plt.plot(real_df['utm_x'], real_df['utm_y'], 'r-', linewidth=2, label='真实轨迹', alpha=0.8)
    plt.plot(sim_df['utm_x'], sim_df['utm_y'], 'b-', linewidth=2, label='仿真轨迹', alpha=0.8)
    
    # 标记起点和终点
    plt.plot(real_df['utm_x'].iloc[0], real_df['utm_y'].iloc[0], 'go', markersize=10, label='起点')
    plt.plot(real_df['utm_x'].iloc[-1], real_df['utm_y'].iloc[-1], 'ro', markersize=10, label='终点')
    
    plt.xlabel('UTM X坐标 (m)', fontsize=22)
    plt.ylabel('UTM Y坐标 (m)', fontsize=22)
    plt.title('仿真与真实轨迹对比', fontsize=22)
    plt.legend(fontsize=20)
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    
    # 保存图片
    output_file = os.path.join(output_dir, 'trajectory_comparison.png')
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"轨迹对比图已保存: {output_file}")

def create_kinematics_comparison(sim_df, real_df, output_dir):
    """创建运动学对比图（速度和加速度）"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 时间轴对齐和截取
    max_time = min(sim_df['time_relative'].max(), real_df['time_relative'].max())
    sim_mask = sim_df['time_relative'] <= max_time
    real_mask = real_df['time_relative'] <= max_time
    
    sim_subset = sim_df[sim_mask]
    real_subset = real_df[real_mask]
    
    # 速度对比
    ax1.plot(sim_subset['time_relative'], sim_subset['speed_2d'], 'b-', linewidth=2, label='仿真速度')
    ax1.plot(real_subset['time_relative'], real_subset['velocity_2d_ms'], 'r--', linewidth=2, label='真实速度', alpha=0.7)
    ax1.set_ylabel('速度 (m/s)', fontsize=22)
    ax1.set_title('仿真与真实轨迹运动学对比', fontsize=22)
    ax1.legend(fontsize=20)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(labelsize=20)
    
    # 加速度对比
    if 'acceleration' in sim_subset.columns:
        ax2.plot(sim_subset['time_relative'], sim_subset['acceleration'], 'b-', linewidth=2, label='仿真加速度')
    
    # 计算真实轨迹的加速度
    real_acceleration = np.gradient(real_subset['velocity_2d_ms']) / np.gradient(real_subset['time_relative'])
    ax2.plot(real_subset['time_relative'], real_acceleration, 'r--', linewidth=2, label='真实加速度', alpha=0.7)
    
    ax2.set_xlabel('时间 (秒)', fontsize=22)
    ax2.set_ylabel('加速度 (m/s²)', fontsize=22)
    ax2.legend(fontsize=20)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(labelsize=20)
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'kinematics_comparison.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"运动学对比图已保存: {output_file}")

def create_deviation_analysis(sim_df, output_dir):
    """创建横向偏差分析图"""
    if 'lateral_deviation' not in sim_df.columns:
        print("警告：仿真数据中无横向偏差信息")
        return
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 横向偏差随时间变化
    ax1.plot(sim_df['time_relative'], sim_df['lateral_deviation'], 'g-', linewidth=2)
    ax1.set_ylabel('横向偏差 (m)', fontsize=22)
    ax1.set_title('横向偏差分析', fontsize=22)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(labelsize=20)
    
    # 横向偏差分布直方图
    ax2.hist(sim_df['lateral_deviation'], bins=30, color='green', alpha=0.7, edgecolor='black')
    ax2.set_xlabel('横向偏差 (m)', fontsize=22)
    ax2.set_ylabel('频次', fontsize=22)
    ax2.set_title('横向偏差分布直方图', fontsize=22)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(labelsize=20)
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(output_dir, 'deviation_analysis.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"横向偏差分析图已保存: {output_file}")

def create_speed_acceleration_evolution(sim_df, output_dir):
    """创建速度-加速度演化图"""
    if 'acceleration' not in sim_df.columns:
        print("警告：仿真数据中无加速度信息")
        return
    
    plt.figure(figsize=(10, 8))
    
    # 使用时间作为颜色映射
    scatter = plt.scatter(sim_df['speed_2d'], sim_df['acceleration'], 
                         c=sim_df['time_relative'], cmap='viridis', alpha=0.6, s=30)
    
    plt.xlabel('速度 (m/s)', fontsize=22)
    plt.ylabel('加速度 (m/s²)', fontsize=22)
    plt.title('速度-加速度相空间演化', fontsize=22)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('时间 (秒)', fontsize=20)
    cbar.ax.tick_params(labelsize=18)
    
    plt.grid(True, alpha=0.3)
    plt.tick_params(labelsize=20)
    
    # 保存图片
    output_file = os.path.join(output_dir, 'speed_acceleration_evolution.png')
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"速度-加速度演化图已保存: {output_file}")

def generate_statistics_report(sim_df, real_df, output_dir):
    """生成统计分析报告"""
    # 计算统计指标
    stats = {
        "仿真统计": {
            "总时间": f"{sim_df['time_relative'].max():.1f} 秒",
            "平均速度": f"{sim_df['speed_2d'].mean():.2f} m/s",
            "最大速度": f"{sim_df['speed_2d'].max():.2f} m/s",
            "速度标准差": f"{sim_df['speed_2d'].std():.2f} m/s"
        },
        "真实轨迹统计": {
            "总时间": f"{real_df['time_relative'].max():.1f} 秒",
            "平均速度": f"{real_df['velocity_2d_ms'].mean():.2f} m/s",
            "最大速度": f"{real_df['velocity_2d_ms'].max():.2f} m/s",
            "速度标准差": f"{real_df['velocity_2d_ms'].std():.2f} m/s"
        }
    }
    
    if 'acceleration' in sim_df.columns:
        stats["仿真统计"].update({
            "平均加速度": f"{sim_df['acceleration'].mean():.3f} m/s²",
            "最大加速度": f"{sim_df['acceleration'].max():.2f} m/s²",
            "最小加速度": f"{sim_df['acceleration'].min():.2f} m/s²"
        })
    
    if 'lateral_deviation' in sim_df.columns:
        stats["仿真统计"].update({
            "平均横向偏差": f"{sim_df['lateral_deviation'].mean():.2f} m",
            "最大横向偏差": f"{sim_df['lateral_deviation'].max():.2f} m"
        })
    
    # 保存统计报告
    import json
    output_file = os.path.join(output_dir, 'statistics_report.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"统计分析报告已保存: {output_file}")
    print("\n统计摘要:")
    for category, metrics in stats.items():
        print(f"\n{category}:")
        for key, value in metrics.items():
            print(f"  {key}: {value}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='加速度驱动仿真结果可视化')
    parser.add_argument('--sim_file', required=True, help='仿真结果CSV文件路径')
    parser.add_argument('--real_file', default=None, help='真实轨迹CSV文件路径')
    parser.add_argument('--output_dir', default='ai_trajectory_system', help='输出目录')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"开始可视化分析...")
    print(f"仿真数据: {args.sim_file}")
    print(f"真实数据: {args.real_file}")
    print(f"输出目录: {args.output_dir}")
    
    # 加载数据
    try:
        sim_df, real_df = load_data(args.sim_file, args.real_file)
        print(f"数据加载成功: 仿真数据{len(sim_df)}行, 真实数据{len(real_df)}行")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 生成各种可视化图表
    create_trajectory_comparison(sim_df, real_df, args.output_dir)
    create_kinematics_comparison(sim_df, real_df, args.output_dir)
    create_deviation_analysis(sim_df, args.output_dir)
    create_speed_acceleration_evolution(sim_df, args.output_dir)
    
    # 生成统计报告
    generate_statistics_report(sim_df, real_df, args.output_dir)
    
    print(f"\n可视化分析完成！所有结果已保存到: {args.output_dir}")

if __name__ == "__main__":
    main() 