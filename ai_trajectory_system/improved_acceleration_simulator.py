#!/usr/bin/env python3
"""
改进的加速度驱动轨迹仿真器
基于最新研究解决仿真-真实差距问题
"""

import numpy as np
import pandas as pd
import logging
from pathlib import Path
from sklearn.ensemble import RandomForestRegressor
from sklearn.cluster import KMeans
from typing import Dict, List, Tuple, Optional
import pickle
import json
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DriverProfile:
    """驾驶员特征描述"""
    aggressiveness: float  # 激进程度 [0,1]
    reaction_time: float   # 反应时间 [s]
    preferred_speed: float # 偏好速度 [m/s]
    risk_tolerance: float  # 风险容忍度 [0,1]
    comfort_preference: float # 舒适度偏好 [0,1]

class ImprovedAccelerationSimulator:
    """改进的加速度驱动轨迹仿真器"""
    
    def __init__(self):
        """初始化仿真器"""
        self.config = {
            'dt': 0.5,
            'max_acceleration': 3.0,
            'min_acceleration': -4.0,
            'max_speed': 25.0,
            'noise_std': 0.1
        }
        self.driver_profiles = self._generate_driver_profiles()
        self.acceleration_model = None
        self.reference_path = None
        
    def _generate_driver_profiles(self) -> List[DriverProfile]:
        """生成多样化的驾驶员特征"""
        profiles = []
        
        # 保守型驾驶员
        profiles.append(DriverProfile(
            aggressiveness=0.2,
            reaction_time=0.8,
            preferred_speed=12.0,
            risk_tolerance=0.3,
            comfort_preference=0.8
        ))
        
        # 普通驾驶员  
        profiles.append(DriverProfile(
            aggressiveness=0.5,
            reaction_time=0.6,
            preferred_speed=15.0,
            risk_tolerance=0.5,
            comfort_preference=0.5
        ))
        
        # 激进型驾驶员
        profiles.append(DriverProfile(
            aggressiveness=0.8,
            reaction_time=0.4,
            preferred_speed=18.0,
            risk_tolerance=0.8,
            comfort_preference=0.2
        ))
        
        return profiles
    
    def train_simple_model(self, trajectory_files: List[str]):
        """训练简化的基于规则的模型"""
        logger.info("加载训练数据...")
        
        # 加载第一个轨迹作为参考
        if trajectory_files and Path(trajectory_files[0]).exists():
            df = pd.read_csv(trajectory_files[0])
            
            # 提取参考路径（注意：latitude/longitude实际是UTM坐标）
            self.reference_path = np.column_stack([
                df['latitude'].values,  # 实际是UTM X坐标
                df['longitude'].values  # 实际是UTM Y坐标
            ])
            
            logger.info(f"参考路径加载完成，包含 {len(self.reference_path)} 个点")
        else:
            # 默认路径
            self.reference_path = np.column_stack([
                np.linspace(8846764, 8846800, 100),
                np.linspace(-981867, -981900, 100)
            ])
            logger.warning("使用默认参考路径")
    
    def simulate(self, initial_state: Dict, driver_profile_id: int = 1, 
                max_steps: int = 800, scenario_name: str = "default") -> Dict:
        """执行改进的仿真"""
        logger.info(f"开始{scenario_name}仿真...")
        
        # 选择驾驶员特征
        if driver_profile_id >= len(self.driver_profiles):
            driver_profile_id = 1
        driver_profile = self.driver_profiles[driver_profile_id]
        
        # 初始化状态
        states = {
            'x': [initial_state['x']],
            'y': [initial_state['y']], 
            'speed': [initial_state['speed']],
            'acceleration': [0.0],
            'heading': [initial_state['heading']],
            'timestamp': [0.0]
        }
        
        # 仿真循环
        for step in range(max_steps):
            current_time = step * self.config['dt']
            
            # 当前状态
            current_x = states['x'][-1]
            current_y = states['y'][-1]
            current_speed = states['speed'][-1]
            current_heading = states['heading'][-1]
            
            # 计算到参考路径的距离和目标点
            target_point, distance_to_path = self._find_target_point(current_x, current_y)
            
            # 计算期望速度（基于距离和驾驶员特征）
            desired_speed = self._compute_desired_speed(
                distance_to_path, current_speed, driver_profile
            )
            
            # 计算加速度（带有更多变化）
            acceleration = self._compute_acceleration(
                current_speed, desired_speed, driver_profile, step
            )
            
            # 应用约束
            acceleration = self._apply_constraints(acceleration, current_speed)
            
            # 更新速度
            new_speed = max(0, current_speed + acceleration * self.config['dt'])
            
            # 计算新的航向角（更真实的路径跟踪）
            new_heading = self._compute_new_heading(
                current_x, current_y, current_heading, target_point, new_speed
            )
            
            # 更新位置
            new_x = current_x + new_speed * np.cos(new_heading) * self.config['dt']
            new_y = current_y + new_speed * np.sin(new_heading) * self.config['dt']
            
            # 记录状态
            states['x'].append(new_x)
            states['y'].append(new_y)
            states['speed'].append(new_speed)
            states['acceleration'].append(acceleration)
            states['heading'].append(new_heading)
            states['timestamp'].append(current_time + self.config['dt'])
            
            # 进度报告
            if (step + 1) % 100 == 0:
                logger.info(f"步数: {step+1}, 位置: ({new_x:.1f}, {new_y:.1f}), "
                          f"速度: {new_speed:.1f} m/s, 加速度: {acceleration:.2f} m/s², "
                          f"横向偏差: {distance_to_path:.1f} m")
        
        logger.info(f"{scenario_name}仿真完成: 总步数 {max_steps}")
        return states
    
    def _find_target_point(self, x: float, y: float) -> Tuple[np.ndarray, float]:
        """寻找参考路径上的目标点"""
        if self.reference_path is None:
            return np.array([x + 10, y]), 0.0
        
        current_pos = np.array([x, y])
        
        # 找到最近的路径点
        distances = np.linalg.norm(self.reference_path - current_pos, axis=1)
        closest_idx = np.argmin(distances)
        closest_distance = distances[closest_idx]
        
        # 前视距离（根据速度调整）
        lookahead_distance = 15.0
        
        # 寻找前视目标点
        target_idx = closest_idx
        for i in range(closest_idx + 1, len(self.reference_path)):
            if np.linalg.norm(self.reference_path[i] - current_pos) >= lookahead_distance:
                target_idx = i
                break
        else:
            target_idx = min(len(self.reference_path) - 1, closest_idx + 10)
        
        target_point = self.reference_path[target_idx]
        return target_point, closest_distance
    
    def _compute_desired_speed(self, distance_to_path: float, current_speed: float, 
                             driver_profile: DriverProfile) -> float:
        """计算期望速度"""
        # 基础期望速度
        base_speed = driver_profile.preferred_speed
        
        # 根据路径偏差调整
        if distance_to_path > 5.0:
            # 偏离路径较远时减速
            path_factor = max(0.5, 1.0 - (distance_to_path - 5.0) / 20.0)
        else:
            path_factor = 1.0
        
        # 根据当前速度和目标速度的差异调整
        speed_error = abs(current_speed - base_speed)
        if speed_error > 2.0:
            # 速度偏差较大时更保守
            adjustment_factor = 0.8
        else:
            adjustment_factor = 1.0
        
        desired_speed = base_speed * path_factor * adjustment_factor
        
        return desired_speed
    
    def _compute_acceleration(self, current_speed: float, desired_speed: float,
                            driver_profile: DriverProfile, step: int) -> float:
        """计算加速度（增加变化性）"""
        # 基础PID控制
        speed_error = desired_speed - current_speed
        base_acceleration = speed_error * 0.8  # 比例增益
        
        # 驾驶员特征调整
        aggressiveness_factor = 0.5 + driver_profile.aggressiveness
        base_acceleration *= aggressiveness_factor
        
        # 添加周期性变化（模拟真实驾驶的不确定性）
        time_factor = step * self.config['dt']
        periodic_variation = 0.3 * np.sin(0.1 * time_factor) * driver_profile.risk_tolerance
        
        # 添加随机噪声
        noise = np.random.normal(0, 0.2 * driver_profile.risk_tolerance)
        
        # 舒适度约束
        comfort_limit = 2.0 * (1.0 - driver_profile.comfort_preference)
        comfort_factor = min(1.0, comfort_limit / max(abs(base_acceleration), 0.1))
        
        final_acceleration = base_acceleration * comfort_factor + periodic_variation + noise
        
        return final_acceleration
    
    def _apply_constraints(self, acceleration: float, current_speed: float) -> float:
        """应用物理约束"""
        # 加速度限制
        acceleration = np.clip(acceleration, 
                             self.config['min_acceleration'], 
                             self.config['max_acceleration'])
        
        # 确保不会导致负速度
        min_acc_for_positive_speed = -current_speed / self.config['dt']
        acceleration = max(acceleration, min_acc_for_positive_speed)
        
        # 速度限制（通过限制加速度）
        projected_speed = current_speed + acceleration * self.config['dt']
        if projected_speed > self.config['max_speed']:
            acceleration = (self.config['max_speed'] - current_speed) / self.config['dt']
        
        return acceleration
    
    def _compute_new_heading(self, x: float, y: float, current_heading: float,
                           target_point: np.ndarray, speed: float) -> float:
        """计算新的航向角"""
        current_pos = np.array([x, y])
        direction_to_target = target_point - current_pos
        
        if np.linalg.norm(direction_to_target) < 0.1:
            return current_heading
        
        desired_heading = np.arctan2(direction_to_target[1], direction_to_target[0])
        
        # 计算航向误差
        heading_error = desired_heading - current_heading
        
        # 角度归一化
        while heading_error > np.pi:
            heading_error -= 2 * np.pi
        while heading_error < -np.pi:
            heading_error += 2 * np.pi
        
        # 航向角变化率限制（模拟车辆动力学）
        max_heading_rate = 0.8  # rad/s
        max_heading_change = max_heading_rate * self.config['dt']
        
        heading_change = np.clip(heading_error * 2.0, -max_heading_change, max_heading_change)
        
        new_heading = current_heading + heading_change
        
        # 归一化到[-π, π]
        while new_heading > np.pi:
            new_heading -= 2 * np.pi
        while new_heading < -np.pi:
            new_heading += 2 * np.pi
        
        return new_heading
    
    def save_results(self, states: Dict, filename: str):
        """保存仿真结果"""
        output_dir = Path("results") / "improved_acceleration_simulation"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        df = pd.DataFrame(states)
        output_file = output_dir / filename
        df.to_csv(output_file, index=False)
        logger.info(f"仿真结果已保存至: {output_file}")
        
        # 计算统计信息
        speeds = np.array(states['speed'])
        accelerations = np.array(states['acceleration'])
        
        # 计算横向偏差
        lateral_deviations = []
        for i in range(len(states['x'])):
            x, y = states['x'][i], states['y'][i]
            _, deviation = self._find_target_point(x, y)
            lateral_deviations.append(deviation)
        
        stats = {
            'total_time': states['timestamp'][-1],
            'average_speed': float(np.mean(speeds)),
            'max_speed': float(np.max(speeds)),
            'min_speed': float(np.min(speeds)),
            'speed_std': float(np.std(speeds)),
            'average_acceleration': float(np.mean(accelerations)),
            'max_acceleration': float(np.max(accelerations)),
            'min_acceleration': float(np.min(accelerations)),
            'acceleration_std': float(np.std(accelerations)),
            'average_lateral_deviation': float(np.mean(lateral_deviations)),
            'max_lateral_deviation': float(np.max(lateral_deviations)),
            'lateral_deviation_std': float(np.std(lateral_deviations))
        }
        
        stats_file = output_dir / f"{filename.replace('.csv', '_stats.json')}"
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2)
        
        return stats

def main():
    """主函数"""
    # 创建仿真器
    simulator = ImprovedAccelerationSimulator()
    
    # 训练数据路径
    trajectory_files = [
        "trajectory_generator/data/trajectories/trajectory_1.csv",
        "trajectory_generator/data/trajectories/trajectory_2.csv",
        "trajectory_generator/data/trajectories/trajectory_3.csv"
    ]
    
    try:
        # 加载参考数据
        simulator.train_simple_model(trajectory_files)
        
        # 测试场景
        scenarios = [
            {
                'name': '真实初始状态_改进版',
                'initial_state': {'x': 8846764.2, 'y': -981867.1, 'speed': 0.449, 'heading': np.radians(45.8)},
                'driver_profile_id': 1
            },
            {
                'name': '保守驾驶_静止开始',
                'initial_state': {'x': 8846764.0, 'y': -981867.0, 'speed': 0.0, 'heading': np.radians(45.0)},
                'driver_profile_id': 0
            },
            {
                'name': '激进驾驶_高速开始',
                'initial_state': {'x': 8846764.0, 'y': -981867.0, 'speed': 10.0, 'heading': np.radians(0.0)},
                'driver_profile_id': 2
            }
        ]
        
        # 运行仿真
        all_results = {}
        all_stats = {}
        
        for i, scenario in enumerate(scenarios):
            results = simulator.simulate(
                initial_state=scenario['initial_state'],
                driver_profile_id=scenario['driver_profile_id'],
                max_steps=800,
                scenario_name=scenario['name']
            )
            
            # 保存结果
            filename = f"improved_scenario_{i+1}_{scenario['name']}.csv"
            stats = simulator.save_results(results, filename)
            
            all_results[scenario['name']] = results
            all_stats[scenario['name']] = stats
        
        logger.info("=" * 60)
        logger.info("所有改进仿真完成！统计摘要：")
        logger.info("=" * 60)
        
        for name, stats in all_stats.items():
            logger.info(f"\n{name}:")
            logger.info(f"  平均速度: {stats['average_speed']:.2f} m/s")
            logger.info(f"  速度范围: {stats['min_speed']:.2f} - {stats['max_speed']:.2f} m/s")
            logger.info(f"  平均加速度: {stats['average_acceleration']:.2f} m/s²")
            logger.info(f"  加速度范围: {stats['min_acceleration']:.2f} - {stats['max_acceleration']:.2f} m/s²")
            logger.info(f"  平均横向偏差: {stats['average_lateral_deviation']:.2f} m")
            logger.info(f"  最大横向偏差: {stats['max_lateral_deviation']:.2f} m")
        
    except Exception as e:
        logger.error(f"仿真过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main() 