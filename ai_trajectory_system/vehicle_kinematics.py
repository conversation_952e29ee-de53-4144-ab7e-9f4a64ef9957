"""
车辆运动学模型
实现基本的自行车模型和物理约束
"""

import numpy as np
import logging
from typing import Dict, Tuple

logger = logging.getLogger(__name__)

class VehicleKinematics:
    """车辆运动学模型 - 自行车模型"""
    
    def __init__(self, config):
        self.config = config
        
        # 车辆参数
        self.wheelbase = 2.5  # 轴距(米)
        self.max_steering_angle = np.pi / 4  # 最大转向角(45度)
        self.max_speed = 15.0  # 最大速度(m/s)
        self.max_acceleration = 3.0  # 最大加速度(m/s²)
        self.max_deceleration = 5.0  # 最大减速度(m/s²)
        
    def update_state(self, current_state: Dict, control_input: Dict, dt: float) -> Dict:
        """
        使用自行车模型更新车辆状态
        
        Args:
            current_state: 当前状态 {utm_x, utm_y, heading, speed_2d, timestamp}
            control_input: 控制输入 {throttle_brake, steering}
            dt: 时间步长
            
        Returns:
            新的状态字典
        """
        # 提取当前状态
        x = current_state['utm_x']
        y = current_state['utm_y']
        heading = current_state['heading']
        speed = current_state.get('speed_2d', 0.0)
        timestamp = current_state.get('timestamp', 0.0)
        
        # 提取控制输入
        throttle_brake = control_input['throttle_brake']  # [-1, 1]
        steering_normalized = control_input['steering']   # [-1, 1]
        
        # 转换控制输入到物理量
        if throttle_brake >= 0:
            acceleration = throttle_brake * self.max_acceleration
        else:
            acceleration = throttle_brake * self.max_deceleration
            
        steering_angle = steering_normalized * self.max_steering_angle
        
        # 自行车模型运动学更新
        # 速度更新
        new_speed = speed + acceleration * dt
        new_speed = max(0.0, min(new_speed, self.max_speed))  # 速度约束
        
        # 位置和航向更新
        if abs(steering_angle) < 1e-6:
            # 直线运动
            new_x = x + new_speed * np.cos(heading) * dt
            new_y = y + new_speed * np.sin(heading) * dt
            new_heading = heading
            angular_velocity = 0.0
        else:
            # 转弯运动
            angular_velocity = new_speed * np.tan(steering_angle) / self.wheelbase
            new_heading = heading + angular_velocity * dt
            
            # 标准化航向角到[-π, π]
            while new_heading > np.pi:
                new_heading -= 2 * np.pi
            while new_heading < -np.pi:
                new_heading += 2 * np.pi
            
            # 位置更新
            new_x = x + new_speed * np.cos(heading) * dt
            new_y = y + new_speed * np.sin(heading) * dt
        
        # 计算加速度
        actual_acceleration = (new_speed - speed) / dt if dt > 0 else 0.0
        
        # 构建新状态
        new_state = {
            'utm_x': new_x,
            'utm_y': new_y,
            'heading': new_heading,
            'speed_2d': new_speed,
            'timestamp': timestamp + dt,
            'acceleration_longitudinal': actual_acceleration,
            'acceleration_lateral': 0.0,  # 简化版本
            'angular_velocity': angular_velocity,
            'curvature': angular_velocity / (new_speed + 1e-6)
        }
        
        return new_state
    
    def validate_control_input(self, control_input: Dict) -> bool:
        """验证控制输入是否在合理范围内"""
        throttle_brake = control_input.get('throttle_brake', 0.0)
        steering = control_input.get('steering', 0.0)
        
        # 检查范围
        if not (-1.0 <= throttle_brake <= 1.0):
            return False
        if not (-1.0 <= steering <= 1.0):
            return False
            
        return True
    
    def clip_control_input(self, control_input: Dict) -> Dict:
        """裁剪控制输入到合理范围"""
        return {
            'throttle_brake': np.clip(control_input.get('throttle_brake', 0.0), -1.0, 1.0),
            'steering': np.clip(control_input.get('steering', 0.0), -1.0, 1.0)
        }
    
    def calculate_turning_radius(self, speed: float, steering_angle: float) -> float:
        """计算转弯半径"""
        if abs(steering_angle) < 1e-6:
            return float('inf')
        return self.wheelbase / np.tan(abs(steering_angle))
    
    def check_physical_constraints(self, state: Dict) -> Dict:
        """检查物理约束"""
        speed = state.get('speed_2d', 0.0)
        acceleration = state.get('acceleration_longitudinal', 0.0)
        
        constraints = {
            'speed_ok': 0.0 <= speed <= self.max_speed,
            'acceleration_ok': -self.max_deceleration <= acceleration <= self.max_acceleration,
            'all_satisfied': True
        }
        
        constraints['all_satisfied'] = all([
            constraints['speed_ok'],
            constraints['acceleration_ok']
        ])
        
        return constraints


def test_vehicle_kinematics():
    """测试车辆运动学模型"""
    from config import Config
    
    config = Config()
    kinematics = VehicleKinematics(config)
    
    # 初始状态
    state = {
        'utm_x': 0.0,
        'utm_y': 0.0,
        'heading': 0.0,
        'speed_2d': 5.0,
        'timestamp': 0.0
    }
    
    # 测试不同控制输入
    test_cases = [
        {'throttle_brake': 0.5, 'steering': 0.0},    # 加速直行
        {'throttle_brake': 0.0, 'steering': 0.3},    # 匀速右转
        {'throttle_brake': -0.3, 'steering': -0.2},  # 减速左转
    ]
    
    for i, control in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {control}")
        
        # 验证控制输入
        is_valid = kinematics.validate_control_input(control)
        print(f"控制输入有效性: {is_valid}")
        
        # 更新状态
        new_state = kinematics.update_state(state, control, 0.5)
        
        # 检查约束
        constraints = kinematics.check_physical_constraints(new_state)
        
        print(f"更新后状态:")
        print(f"  位置: ({new_state['utm_x']:.2f}, {new_state['utm_y']:.2f})")
        print(f"  航向: {np.degrees(new_state['heading']):.1f}°")
        print(f"  速度: {new_state['speed_2d']:.2f} m/s")
        print(f"  物理约束满足: {constraints['all_satisfied']}")
        
        state = new_state


if __name__ == "__main__":
    test_vehicle_kinematics() 