#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨迹跟踪仿真动画生成器
=======================
该脚本读取真实的轨迹数据和仿真生成的轨迹数据，
并创建一个三合一的对比动画，展示仿真过程。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import os, logging

# --- 基本设置 ---
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# --- 动画配置 ---
class AnimationConfig:
    # --- 文件路径 ---
    # !!! 注意: 请确保这里的路径指向您想要可视化的仿真结果 !!!
    REAL_TRAJ_PATH = 'trajectory_generator/data/trajectories/trajectory_1.csv'
    SIM_TRAJ_PATH = 'results/acceleration_simulation_1/trajectory.csv'
    OUTPUT_FILENAME = 'results/acceleration_simulation_1/simulation_animation.gif'

    # --- 动画参数 ---
    INTERVAL = 50  # 每帧之间的毫秒数
    FPS = 20       # 输出GIF的帧率

    # --- 样式设置 ---
    FONT_NAME = 'SimHei' # 使用黑体作为中文字体
    FONT_SIZE = 20       # 统一字号
    
def prepare_real_data(filepath):
    """加载并为真实轨迹数据计算必要的运动学特征"""
    logging.info(f"预处理真实轨迹数据: {filepath}")
    df = pd.read_csv(filepath)
    df.rename(columns={'latitude': 'x', 'longitude': 'y', 'timestamp_ms': 'time'}, inplace=True)
    
    # 计算运动学特征
    df['dt'] = df['time'].diff().fillna(500) / 1000.0 # 填充第一个dt为0.5s
    df.loc[df['dt'] <= 0, 'dt'] = 0.5 # 确保dt为正
    
    df['speed'] = np.hypot(df['x'].diff(), df['y'].diff()).fillna(0) / df['dt']
    df['acceleration'] = df['speed'].diff().fillna(0) / df['dt']
    df.fillna(method='bfill', inplace=True) # 填充所有剩余的NaN
    
    # 转换时间为相对时间（秒）
    df['time_sec'] = (df['time'] - df['time'].iloc[0]) / 1000.0
    
    logging.info("真实轨迹数据预处理完成。")
    return df

def prepare_sim_data(filepath):
    """预处理仿真数据"""
    logging.info(f"预处理仿真轨迹数据: {filepath}")
    df = pd.read_csv(filepath)
    
    # 检查数据列名
    if 'utm_x' in df.columns:
        df.rename(columns={'utm_x': 'x', 'utm_y': 'y', 'speed_2d': 'speed'}, inplace=True)
    
    # 如果没有时间列，创建一个
    if 'timestamp' not in df.columns:
        df['timestamp'] = np.arange(len(df)) * 0.5  # 假设0.5秒步长
    
    # 转换为相对时间
    df['time_sec'] = df['timestamp'] - df['timestamp'].iloc[0]
    
    logging.info("仿真轨迹数据预处理完成。")
    return df

# --- 主函数 ---
def generate_animation():
    """主函数：加载数据、设置画布、创建并保存动画"""
    
    cfg = AnimationConfig()

    # --- 1. 加载数据 ---
    logging.info("正在加载数据...")
    if not os.path.exists(cfg.SIM_TRAJ_PATH):
        logging.error(f"仿真结果文件未找到: {cfg.SIM_TRAJ_PATH}")
        logging.error("请先运行仿真器生成结果，或修改脚本中的路径。")
        return

    real_df = prepare_real_data(cfg.REAL_TRAJ_PATH)
    sim_df = prepare_sim_data(cfg.SIM_TRAJ_PATH)
    
    # 确定动画总长度：使用更短的轨迹长度
    max_time = min(real_df['time_sec'].max(), sim_df['time_sec'].max())
    num_frames = min(len(real_df), len(sim_df))
    
    logging.info(f"动画长度: {max_time:.1f} 秒, 总帧数: {num_frames}")

    # --- 2. 设置 Matplotlib 样式 ---
    logging.info("设置图表样式...")
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = [cfg.FONT_NAME]
    plt.rcParams['axes.unicode_minus'] = False # 正常显示负号
    plt.rcParams['font.size'] = cfg.FONT_SIZE

    # --- 3. 创建画布 ---
    fig, axs = plt.subplots(3, 1, figsize=(18, 24), gridspec_kw={'height_ratios': [4, 1, 1]})
    fig.patch.set_facecolor('white')
    
    # --- 4. 静态背景绘制 (init 函数) ---
    # 轨迹图
    ax1 = axs[0]
    ax1.plot(real_df['x'], real_df['y'], 'b-', label='真实轨迹', linewidth=2.5, alpha=0.6)
    sim_path, = ax1.plot([], [], 'r--', label='仿真轨迹', linewidth=2.5)
    real_vehicle, = ax1.plot([], [], 'bo', markersize=12, label='真实车辆位置')
    sim_vehicle, = ax1.plot([], [], 'ro', markersize=12, label='仿真车辆位置')
    lookahead_point, = ax1.plot([], [], 'gx', markersize=12, mew=3, label='前瞻点')
    ax1.set_title("轨迹对比", pad=20)
    ax1.set_xlabel("UTM X 坐标 (m)"); ax1.set_ylabel("UTM Y 坐标 (m)")
    ax1.legend(loc='upper left'); ax1.grid(True); ax1.set_aspect('equal', adjustable='box')

    # 速度图
    ax2 = axs[1]
    ax2.plot(real_df['time_sec'], real_df['speed'], 'b-', label='真实速度', alpha=0.6)
    sim_speed_line, = ax2.plot([], [], 'r--', label='仿真速度')
    real_speed_dot, = ax2.plot([], [], 'bo', markersize=8)
    sim_speed_dot, = ax2.plot([], [], 'ro', markersize=8)
    ax2.set_title("速度对比", pad=20)
    ax2.set_xlabel("时间 (s)"); ax2.set_ylabel("速度 (m/s)")
    ax2.legend(loc='upper left'); ax2.grid(True)
    ax2.set_xlim(0, max_time); ax2.set_ylim(0, max(real_df['speed'].max(), sim_df['speed'].max()) * 1.1)

    # 加速度图
    ax3 = axs[2]
    ax3.plot(real_df['time_sec'], real_df['acceleration'].clip(-3.5, 3.5), 'b-', label='真实加速度', alpha=0.6)
    sim_accel_line, = ax3.plot([], [], 'r--', label='仿真加速度')
    real_accel_dot, = ax3.plot([], [], 'bo', markersize=8)
    sim_accel_dot, = ax3.plot([], [], 'ro', markersize=8)
    ax3.set_title("加速度对比", pad=20)
    ax3.set_xlabel("时间 (s)"); ax3.set_ylabel("加速度 (m/s²)")
    ax3.legend(loc='upper left'); ax3.grid(True)
    ax3.set_xlim(0, max_time); ax3.set_ylim(-4, 4)
    
    fig.tight_layout(pad=4.0)
    
    # 动态标题
    time_template = '仿真时间: %.1f s'
    time_text = ax1.text(0.05, 0.95, '', transform=ax1.transAxes, ha='left', va='top', 
                        bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5))

    # --- 5. 动画更新函数 ---
    def update(frame_idx):
        if frame_idx >= num_frames: 
            return []

        # 当前时间
        current_time = frame_idx * max_time / num_frames
        
        # 找到对应的真实轨迹点
        real_idx = min(frame_idx, len(real_df) - 1)
        
        # 找到对应的仿真轨迹点（基于时间匹配）
        sim_time_diffs = np.abs(sim_df['time_sec'] - current_time)
        sim_idx = np.argmin(sim_time_diffs)
        sim_idx = min(sim_idx, len(sim_df) - 1)
        
        # 更新轨迹
        sim_path.set_data(sim_df['x'][:sim_idx+1], sim_df['y'][:sim_idx+1])
        
        # 更新车辆位置
        real_vehicle.set_data([real_df['x'].iloc[real_idx]], [real_df['y'].iloc[real_idx]])
        sim_vehicle.set_data([sim_df['x'].iloc[sim_idx]], [sim_df['y'].iloc[sim_idx]])
        
        # 更新前瞻点（如果存在）
        if 'target_x' in sim_df.columns and pd.notna(sim_df['target_x'].iloc[sim_idx]):
            lookahead_point.set_data([sim_df['target_x'].iloc[sim_idx]], 
                                   [sim_df['target_y'].iloc[sim_idx]])
        
        # 更新速度
        sim_speed_line.set_data(sim_df['time_sec'][:sim_idx+1], sim_df['speed'][:sim_idx+1])
        real_speed_dot.set_data([real_df['time_sec'].iloc[real_idx]], [real_df['speed'].iloc[real_idx]])
        sim_speed_dot.set_data([sim_df['time_sec'].iloc[sim_idx]], [sim_df['speed'].iloc[sim_idx]])

        # 更新加速度
        if 'acceleration' in sim_df.columns:
            sim_accel_line.set_data(sim_df['time_sec'][:sim_idx+1], sim_df['acceleration'][:sim_idx+1])
            sim_accel_dot.set_data([sim_df['time_sec'].iloc[sim_idx]], [sim_df['acceleration'].iloc[sim_idx]])
        
        real_accel_dot.set_data([real_df['time_sec'].iloc[real_idx]], [real_df['acceleration'].iloc[real_idx]])
        
        # 更新时间
        time_text.set_text(time_template % current_time)
        
        return [sim_path, real_vehicle, sim_vehicle, lookahead_point, 
                sim_speed_line, real_speed_dot, sim_speed_dot, 
                sim_accel_line, real_accel_dot, sim_accel_dot, time_text]

    # --- 6. 创建并保存动画 ---
    logging.info(f"开始生成动画 (总帧数: {num_frames})... 这可能需要几分钟时间。")
    
    ani = FuncAnimation(fig, update, frames=num_frames, interval=cfg.INTERVAL, blit=True, repeat=False)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(cfg.OUTPUT_FILENAME), exist_ok=True)
    
    # 使用Pillow作为writer来保存GIF
    ani.save(cfg.OUTPUT_FILENAME, writer='pillow', fps=cfg.FPS)
    
    logging.info(f"动画已成功保存到: {cfg.OUTPUT_FILENAME}")
    plt.close(fig)

if __name__ == '__main__':
    generate_animation() 