#!/usr/bin/env python3
"""
环境-速度建模模块
从真实轨迹数据中学习环境因素对速度的影响，建立预测模型
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import pickle
import glob

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config import Config
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnvironmentSpeedModel:
    """环境-速度影响模型"""
    
    def __init__(self, config):
        self.config = config
        self.speed_model = None
        self.feature_columns = []
        self.model_trained = False
        
    def load_and_analyze_all_trajectories(self):
        """加载所有轨迹数据并分析环境-速度关系"""
        logger.info("开始加载和分析所有轨迹数据")
        
        data_dirs = [
            "../trajectory_generator/data/trajectories",
            "trajectory_generator/data/trajectories",
            "data/trajectories"
        ]
        
        all_trajectory_files = []
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                files = glob.glob(os.path.join(data_dir, "trajectory_*.csv"))
                all_trajectory_files.extend(files)
        
        if not all_trajectory_files:
            logger.error("未找到轨迹数据文件")
            return None
        
        logger.info(f"找到 {len(all_trajectory_files)} 个轨迹文件")
        
        all_data = []
        for file_path in all_trajectory_files[:3]:
            try:
                df = pd.read_csv(file_path)
                if len(df) > 100:
                    logger.info(f"加载轨迹文件: {os.path.basename(file_path)} ({len(df)} 点)")
                    all_data.append(df)
            except Exception as e:
                logger.warning(f"无法加载文件 {file_path}: {e}")
        
        if not all_data:
            logger.error("没有有效的轨迹数据")
            return None
        
        combined_df = pd.concat(all_data, ignore_index=True)
        logger.info(f"合并后总数据点: {len(combined_df)}")
        
        return combined_df
    
    def extract_environmental_features(self, df):
        """从轨迹数据中提取环境特征"""
        logger.info("开始提取环境特征")
        
        features_df = pd.DataFrame()
        
        if 'longitude' in df.columns and 'latitude' in df.columns:
            x_col, y_col = 'longitude', 'latitude'
        else:
            x_col, y_col = 'utm_x', 'utm_y'
        
        if 'velocity_2d_ms' in df.columns:
            features_df['speed'] = df['velocity_2d_ms']
        elif 'velocit_y_2d_ms' in df.columns:
            features_df['speed'] = df['velocit_y_2d_ms']
        else:
            dx = df[x_col].diff()
            dy = df[y_col].diff()
            dt = df['timestamp_ms'].diff() / 1000.0
            features_df['speed'] = np.sqrt(dx**2 + dy**2) / dt
        
        # 路径几何特征
        features_df['curvature'] = self._calculate_curvature(df[x_col], df[y_col])
        features_df['path_angle'] = self._calculate_path_angle(df[x_col], df[y_col])
        features_df['turning_rate'] = features_df['path_angle'].diff().abs()
        
        # 位置特征
        features_df['x_coord'] = df[x_col]
        features_df['y_coord'] = df[y_col]
        features_df['distance_from_start'] = np.sqrt(
            (df[x_col] - df[x_col].iloc[0])**2 + 
            (df[y_col] - df[y_col].iloc[0])**2
        )
        
        if 'timestamp_ms' in df.columns:
            timestamps = pd.to_datetime(df['timestamp_ms'], unit='ms')
            features_df['hour'] = timestamps.dt.hour
            features_df['minute'] = timestamps.dt.minute
            features_df['time_progress'] = (df['timestamp_ms'] - df['timestamp_ms'].iloc[0]) / 1000.0
        
        features_df['elevation_proxy'] = np.sin(df[x_col] / 1000.0) * 50 + np.cos(df[y_col] / 800.0) * 30
        features_df['slope_proxy'] = features_df['elevation_proxy'].diff().abs()
        
        features_df['local_complexity'] = (
            features_df['curvature'].rolling(window=10, min_periods=1).std().fillna(0) +
            features_df['turning_rate'].rolling(window=10, min_periods=1).mean().fillna(0)
        )
        
        features_df = features_df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        features_df = features_df.replace([np.inf, -np.inf], 0)
        
        logger.info(f"提取特征完成，特征维度: {features_df.shape}")
        return features_df
    
    def _calculate_curvature(self, x, y):
        dx = x.diff(); dy = y.diff(); ddx = dx.diff(); ddy = dy.diff()
        numerator = np.abs(dx * ddy - dy * ddx)
        denominator = (dx**2 + dy**2)**1.5
        return (numerator / (denominator + 1e-8)).fillna(0)
    
    def _calculate_path_angle(self, x, y):
        return np.arctan2(y.diff(), x.diff()).fillna(0)
    
    def train_speed_model(self, features_df):
        logger.info("开始训练环境-速度预测模型")
        
        target_col = 'speed'
        feature_cols = [col for col in features_df.columns if col != target_col]
        
        X = features_df[feature_cols]
        y = features_df[target_col]
        
        speed_95 = y.quantile(0.95); speed_5 = y.quantile(0.05)
        valid_mask = (y >= speed_5) & (y <= speed_95)
        
        X = X[valid_mask]; y = y[valid_mask]
        
        logger.info(f"训练数据: {len(X)} 样本, {len(feature_cols)} 特征")
        
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        self.speed_model = RandomForestRegressor(n_estimators=100, max_depth=15, min_samples_split=10,
                                               min_samples_leaf=5, random_state=42, n_jobs=-1)
        self.speed_model.fit(X_train, y_train)
        self.feature_columns = feature_cols
        self.model_trained = True
        
        y_pred = self.speed_model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        logger.info(f"模型性能: MSE={mse:.4f}, R²={r2:.4f}")
        
        feature_importance = pd.DataFrame({'feature': feature_cols, 'importance': self.speed_model.feature_importances_}
                                          ).sort_values('importance', ascending=False)
        logger.info("Top 10 重要特征:"); [logger.info(f"  {row['feature']}: {row['importance']:.4f}") for _, row in feature_importance.head(10).iterrows()]
        
        return {'mse': mse, 'r2': r2, 'feature_importance': feature_importance}
    
    def predict_speed(self, current_state, path_context, trajectory_history=None):
        if not self.model_trained: return 5.0
        features = self._build_prediction_features(current_state, path_context, trajectory_history)
        predicted_speed = self.speed_model.predict([features])[0]
        return max(1.0, min(15.0, predicted_speed))
    
    def _build_prediction_features(self, current_state, path_context, trajectory_history):
        features = {}
        features['x_coord'] = current_state['utm_x']
        features['y_coord'] = current_state['utm_y']
        features['curvature'] = path_context.get('curvature', 0)
        features['path_angle'] = path_context.get('path_angle', 0)
        features['turning_rate'] = path_context.get('turning_rate', 0)
        start_x = path_context.get('start_x', features['x_coord'])
        start_y = path_context.get('start_y', features['y_coord'])
        features['distance_from_start'] = np.sqrt((features['x_coord'] - start_x)**2 + (features['y_coord'] - start_y)**2)
        features['hour'] = 12; features['minute'] = 0
        features['time_progress'] = path_context.get('time_progress', 0)
        features['elevation_proxy'] = np.sin(features['x_coord'] / 1000.0) * 50 + np.cos(features['y_coord'] / 800.0) * 30
        features['slope_proxy'] = abs(path_context.get('elevation_change', 0))
        features['local_complexity'] = path_context.get('complexity', 0)
        for col in self.feature_columns:
            if col not in features: features[col] = 0
        return [features[col] for col in self.feature_columns]
    
    def save_model(self, file_path):
        if not self.model_trained: return False
        with open(file_path, 'wb') as f: pickle.dump({'speed_model': self.speed_model, 'feature_columns': self.feature_columns, 'model_trained': self.model_trained}, f)
        logger.info(f"模型已保存到: {file_path}"); return True
    
    def load_model(self, file_path):
        try:
            with open(file_path, 'rb') as f: model_data = pickle.load(f)
            self.speed_model = model_data['speed_model']; self.feature_columns = model_data['feature_columns']; self.model_trained = model_data['model_trained']
            logger.info(f"模型已从 {file_path} 加载"); return True
        except Exception as e:
            logger.error(f"加载模型失败: {e}"); return False

def train_environment_speed_model():
    logger.info("开始训练环境-速度影响模型"); config = Config(); model = EnvironmentSpeedModel(config)
    combined_df = model.load_and_analyze_all_trajectories()
    if combined_df is None: return None
    features_df = model.extract_environmental_features(combined_df)
    results = model.train_speed_model(features_df)
    os.makedirs("models", exist_ok=True); model.save_model("models/environment_speed_model.pkl")
    plot_feature_importance(results['feature_importance'])
    logger.info("环境-速度模型训练完成"); return model, results

def plot_feature_importance(feature_importance):
    plt.figure(figsize=(12, 8))
    top_features = feature_importance.head(15)
    plt.barh(range(len(top_features)), top_features['importance']); plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('特征重要性'); plt.title('环境因素对速度影响的重要性排序', fontsize=16); plt.gca().invert_yaxis(); plt.tight_layout()
    os.makedirs("results", exist_ok=True)
    plt.savefig('results/feature_importance.png', dpi=300, bbox_inches='tight')
    logger.info("特征重要性图表已保存")

if __name__ == "__main__":
    os.makedirs("models", exist_ok=True); os.makedirs("results", exist_ok=True)
    try:
        model, results = train_environment_speed_model()
        if model: logger.info("模型训练成功完成！")
    except Exception as e:
        logger.error(f"训练过程中出错: {e}"); import traceback; traceback.print_exc()