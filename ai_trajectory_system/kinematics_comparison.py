#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运动学对比分析脚本

该脚本用于对比分析仿真轨迹与真实轨迹在速度和加速度上的差异。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import argparse
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
plt.rcParams['font.size'] = 22  # 设置默认字体大小为22号

def plot_kinematics_comparison(real_trajectory_path, sim_trajectory_path, output_dir):
    """
    加载数据、进行对比分析并生成图表。

    Args:
        real_trajectory_path (str): 真实轨迹文件的路径。
        sim_trajectory_path (str): 仿真轨迹文件的路径。
        output_dir (str): 输出图表的保存目录。
    """
    # --- 1. 加载和预处理数据 ---
    try:
        df_sim = pd.read_csv(sim_trajectory_path)
        df_real = pd.read_csv(real_trajectory_path)
    except FileNotFoundError as e:
        print(f"错误: 无法找到文件 {e.filename}")
        return

    # 处理真实轨迹中的列名错误问题
    if 'latitude' in df_real.columns and 'longitude' in df_real.columns:
        df_real.rename(columns={'latitude': 'utm_x', 'longitude': 'utm_y'}, inplace=True)

    # --- 2. 创建相对时间轴 ---
    # 创建统一的相对时间轴（都从0开始），以秒为单位
    sim_time = df_sim['timestamp'] - df_sim['timestamp'].iloc[0]
    real_time = (df_real['timestamp_ms'] - df_real['timestamp_ms'].iloc[0]) / 1000.0
    
    # --- 3. 时间对齐与插值 ---
    # 使用插值将真实轨迹的运动学数据对齐到仿真时间轴上
    # np.interp(新的x坐标, 旧的x坐标, 旧的y坐标)
    real_speed_aligned = np.interp(sim_time, real_time, df_real['velocity_2d_ms'])
    real_accel_aligned = np.interp(sim_time, real_time, df_real['horizontal_acceleration_ms2'])

    # --- 4. 计算仿真加速度 ---
    # 使用numpy.gradient计算速度的变化率，即加速度
    sim_speed = df_sim['speed_2d']
    sim_accel = np.gradient(sim_speed, sim_time, edge_order=2)

    # --- 5. 绘图 ---
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 12), sharex=True)
    fig.suptitle('仿真与真实轨迹运动学对比', fontsize=22, y=0.95)

    # 子图1: 速度对比
    ax1.plot(sim_time, sim_speed, 'b-', linewidth=2, label='仿真速度')
    ax1.plot(sim_time, real_speed_aligned, 'r--', linewidth=2, label='真实速度 (对齐后)', alpha=0.8)
    ax1.set_ylabel('速度 (m/s)', fontsize=22)
    ax1.set_title('速度对比', fontsize=22)
    ax1.legend(fontsize=22)
    ax1.tick_params(axis='both', which='major', labelsize=22)
    ax1.grid(True, alpha=0.3)

    # 子图2: 加速度对比
    ax2.plot(sim_time, sim_accel, 'b-', linewidth=2, label='仿真加速度')
    ax2.plot(sim_time, real_accel_aligned, 'r--', linewidth=2, label='真实加速度 (对齐后)', alpha=0.8)
    ax2.set_xlabel('时间 (秒)', fontsize=22)
    ax2.set_ylabel('纵向加速度 (m/s²)', fontsize=22)
    ax2.set_title('加速度对比', fontsize=22)
    ax2.legend(fontsize=22)
    ax2.tick_params(axis='both', which='major', labelsize=22)
    ax2.grid(True, alpha=0.3)

    # 调整Y轴范围，使其更具可读性
    accel_min = min(np.percentile(sim_accel, 5), np.percentile(real_accel_aligned, 5))
    accel_max = max(np.percentile(sim_accel, 95), np.percentile(real_accel_aligned, 95))
    ax2.set_ylim(accel_min - 0.5, accel_max + 0.5)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    
    # 保存图表
    output_filename = Path(output_dir) / 'kinematics_comparison.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"运动学对比图已保存到: {output_filename}")
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='对比仿真轨迹与真实轨迹的运动学参数。')
    parser.add_argument(
        '--sim_file', 
        type=str, 
        required=True,
        help='仿真生成的轨迹文件路径 (e.g., results/smart_simulation_1/trajectory.csv)'
    )
    parser.add_argument(
        '--real_file', 
        type=str, 
        default='trajectory_generator/data/trajectories/trajectory_1.csv',
        help='真实的轨迹文件路径'
    )
    args = parser.parse_args()

    # 输出目录就是仿真文件所在的目录
    output_directory = Path(args.sim_file).parent

    if not Path(args.sim_file).exists():
        print(f"错误：找不到仿真文件 '{args.sim_file}'")
        return
    if not Path(args.real_file).exists():
        print(f"错误：找不到真实轨迹文件 '{args.real_file}'")
        return

    plot_kinematics_comparison(args.real_file, args.sim_file, output_directory)

if __name__ == '__main__':
    main() 