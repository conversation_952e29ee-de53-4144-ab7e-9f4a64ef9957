#!/usr/bin/env python3
"""
综合分析工具 - 诊断轨迹仿真问题
基于最新研究和您的可视化结果
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
import json
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 22  # 设置标题为22号字体

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrajectoryAnalyzer:
    """轨迹分析器"""
    
    def __init__(self):
        self.real_data = {}
        self.simulation_data = {}
        
    def load_real_trajectory(self, file_path):
        """加载真实轨迹数据"""
        if Path(file_path).exists():
            df = pd.read_csv(file_path)
            
            # 处理数据
            df['speed'] = df['calculated_speed_ms'].fillna(0)
            df['x'] = df['latitude']  # 实际为UTM坐标
            df['y'] = df['longitude']
            df['time'] = np.arange(len(df)) * 0.1
            
            self.real_data = {
                'x': df['x'].values,
                'y': df['y'].values,
                'speed': df['speed'].values,
                'time': df['time'].values,
                'acceleration_x': df['acceleration_x_ms2'].fillna(0).values,
                'acceleration_y': df['acceleration_y_ms2'].fillna(0).values
            }
            
            logger.info(f"真实轨迹加载完成: {len(df)} 个数据点")
            return True
        return False
    
    def load_simulation_results(self, result_dirs):
        """加载仿真结果"""
        for result_dir in result_dirs:
            if Path(result_dir).exists():
                for csv_file in Path(result_dir).glob("*.csv"):
                    scenario_name = csv_file.stem
                    df = pd.read_csv(csv_file)
                    
                    # 处理不同的列名格式
                    if 'x' in df.columns:
                        x_col, y_col = 'x', 'y'
                        speed_col = 'speed'
                        time_col = 'timestamp' if 'timestamp' in df.columns else 'time'
                    else:
                        x_col, y_col = 'utm_x', 'utm_y'
                        speed_col = 'speed_2d'
                        time_col = 'timestamp'
                    
                    self.simulation_data[scenario_name] = {
                        'x': df[x_col].values,
                        'y': df[y_col].values,
                        'speed': df[speed_col].values,
                        'acceleration': df['acceleration'].values,
                        'time': df[time_col].values if time_col in df.columns else np.arange(len(df)) * 0.1
                    }
        
        logger.info(f"仿真结果加载完成: {len(self.simulation_data)} 个场景")
    
    def analyze_behavior_patterns(self):
        """分析行为模式"""
        analysis_results = {}
        
        # 真实数据分析
        if self.real_data:
            real_speed_mean = np.mean(self.real_data['speed'])
            real_speed_std = np.std(self.real_data['speed'])
            real_speed_range = (np.min(self.real_data['speed']), np.max(self.real_data['speed']))
            
            analysis_results['real'] = {
                'speed_mean': real_speed_mean,
                'speed_std': real_speed_std,
                'speed_range': real_speed_range,
                'speed_entropy': self._calculate_entropy(self.real_data['speed']),
                'acceleration_variance': np.var(self.real_data['acceleration_x'])
            }
        
        # 仿真数据分析
        for scenario, data in self.simulation_data.items():
            sim_speed_mean = np.mean(data['speed'])
            sim_speed_std = np.std(data['speed'])
            sim_speed_range = (np.min(data['speed']), np.max(data['speed']))
            
            analysis_results[scenario] = {
                'speed_mean': sim_speed_mean,
                'speed_std': sim_speed_std,
                'speed_range': sim_speed_range,
                'speed_entropy': self._calculate_entropy(data['speed']),
                'acceleration_variance': np.var(data['acceleration'])
            }
        
        return analysis_results
    
    def _calculate_entropy(self, data, bins=20):
        """计算数据的熵值"""
        hist, _ = np.histogram(data, bins=bins, density=True)
        hist = hist[hist > 0]  # 移除零值
        return -np.sum(hist * np.log(hist))
    
    def generate_problem_diagnosis(self):
        """生成问题诊断报告"""
        logger.info("=== 轨迹仿真问题诊断报告 ===")
        
        patterns = self.analyze_behavior_patterns()
        
        print("\n🔍 **核心问题识别**:")
        print("1. **行为多样性不足**: 仿真速度变化过于规律")
        print("2. **物理约束缺失**: 加速度变化不符合真实驾驶")
        print("3. **环境响应性差**: 缺乏对路况的动态适应")
        print("4. **长期稳定性问题**: 累积误差导致轨迹偏移")
        
        print("\n📊 **数据统计对比**:")
        if 'real' in patterns:
            real = patterns['real']
            print(f"真实轨迹 - 速度均值: {real['speed_mean']:.2f} m/s, 标准差: {real['speed_std']:.2f}")
            print(f"真实轨迹 - 速度范围: {real['speed_range'][0]:.2f} - {real['speed_range'][1]:.2f} m/s")
            print(f"真实轨迹 - 行为熵值: {real['speed_entropy']:.3f}")
        
        print("\n仿真场景对比:")
        for scenario, data in patterns.items():
            if scenario != 'real':
                print(f"{scenario}:")
                print(f"  速度均值: {data['speed_mean']:.2f} m/s, 标准差: {data['speed_std']:.2f}")
                print(f"  速度范围: {data['speed_range'][0]:.2f} - {data['speed_range'][1]:.2f} m/s")
                print(f"  行为熵值: {data['speed_entropy']:.3f}")
        
        print("\n🎯 **基于最新研究的改进建议**:")
        print("1. **采用Patch级预测**: 类似BehaviorGPT，预测轨迹段而非单点")
        print("2. **多模态行为建模**: 为每个时刻预测多种可能的行为模式")
        print("3. **相对时空表示**: 使用相对坐标系提高泛化能力")
        print("4. **三重注意力机制**: 建模时序、地图、智能体间的交互")
        print("5. **物理约束集成**: 在损失函数中加入运动学约束")
        
        return patterns
    
    def create_comprehensive_visualization(self, save_dir="ai_trajectory_system"):
        """创建综合可视化分析"""
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        # 创建多子图分析
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 轨迹对比图
        ax1 = plt.subplot(2, 3, 1)
        if self.real_data:
            plt.plot(self.real_data['x'], self.real_data['y'], 'b-', 
                    linewidth=3, label='真实轨迹', alpha=0.8)
        
        colors = ['red', 'green', 'orange', 'purple']
        for i, (name, data) in enumerate(self.simulation_data.items()):
            plt.plot(data['x'], data['y'], color=colors[i % len(colors)], 
                    linewidth=2, label=f'仿真-{name}', alpha=0.7)
        
        plt.title('轨迹空间对比', fontsize=22, fontweight='bold')
        plt.xlabel('X坐标 (m)', fontsize=20)
        plt.ylabel('Y坐标 (m)', fontsize=20)
        plt.legend(fontsize=16)
        plt.grid(True, alpha=0.3)
        
        # 2. 速度分布对比
        ax2 = plt.subplot(2, 3, 2)
        if self.real_data:
            plt.hist(self.real_data['speed'], bins=30, alpha=0.7, 
                    label='真实轨迹', density=True, color='blue')
        
        for i, (name, data) in enumerate(self.simulation_data.items()):
            plt.hist(data['speed'], bins=30, alpha=0.5, 
                    label=f'仿真-{name}', density=True, 
                    color=colors[i % len(colors)])
        
        plt.title('速度分布对比', fontsize=22, fontweight='bold')
        plt.xlabel('速度 (m/s)', fontsize=20)
        plt.ylabel('概率密度', fontsize=20)
        plt.legend(fontsize=16)
        plt.grid(True, alpha=0.3)
        
        # 3. 速度时序图
        ax3 = plt.subplot(2, 3, 3)
        if self.real_data:
            plt.plot(self.real_data['time'], self.real_data['speed'], 'b-', 
                    linewidth=3, label='真实轨迹', alpha=0.8)
        
        for i, (name, data) in enumerate(self.simulation_data.items()):
            plt.plot(data['time'], data['speed'], color=colors[i % len(colors)], 
                    linewidth=2, label=f'仿真-{name}', alpha=0.7)
        
        plt.title('速度时序对比', fontsize=22, fontweight='bold')
        plt.xlabel('时间 (s)', fontsize=20)
        plt.ylabel('速度 (m/s)', fontsize=20)
        plt.legend(fontsize=16)
        plt.grid(True, alpha=0.3)
        
        # 4. 加速度分析
        ax4 = plt.subplot(2, 3, 4)
        if self.real_data:
            real_acc = np.sqrt(self.real_data['acceleration_x']**2 + 
                             self.real_data['acceleration_y']**2)
            plt.plot(self.real_data['time'], real_acc, 'b-', 
                    linewidth=3, label='真实轨迹', alpha=0.8)
        
        for i, (name, data) in enumerate(self.simulation_data.items()):
            plt.plot(data['time'], data['acceleration'], color=colors[i % len(colors)], 
                    linewidth=2, label=f'仿真-{name}', alpha=0.7)
        
        plt.title('加速度时序对比', fontsize=22, fontweight='bold')
        plt.xlabel('时间 (s)', fontsize=20)
        plt.ylabel('加速度 (m/s²)', fontsize=20)
        plt.legend(fontsize=16)
        plt.grid(True, alpha=0.3)
        
        # 5. 行为复杂度分析
        ax5 = plt.subplot(2, 3, 5)
        
        # 计算速度变化率
        complexity_data = {}
        if self.real_data:
            speed_diff = np.diff(self.real_data['speed'])
            complexity_data['真实轨迹'] = np.std(speed_diff)
        
        for name, data in self.simulation_data.items():
            speed_diff = np.diff(data['speed'])
            complexity_data[f'仿真-{name}'] = np.std(speed_diff)
        
        names = list(complexity_data.keys())
        values = list(complexity_data.values())
        bars = plt.bar(names, values, color=['blue'] + colors[:len(names)-1])
        
        plt.title('行为复杂度对比', fontsize=22, fontweight='bold')
        plt.ylabel('速度变化率标准差', fontsize=20)
        plt.xticks(rotation=45, fontsize=16)
        plt.grid(True, alpha=0.3)
        
        # 6. 问题总结
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')
        
        problem_text = """
问题诊断总结:

🚨 主要问题:
• 仿真行为过于规律化
• 缺乏真实的随机性
• 环境适应性不足
• 长期累积误差

💡 改进方向:
• 多模态行为建模
• Patch级别预测
• 物理约束集成
• 注意力机制优化

📊 关键指标差距:
• 速度变化复杂度低
• 加速度模式单一
• 轨迹偏移过大
        """
        
        plt.text(0.05, 0.95, problem_text, transform=ax6.transAxes, 
                fontsize=18, verticalalignment='top', fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path / 'comprehensive_trajectory_analysis.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        logger.info(f"综合分析图已保存: {save_path / 'comprehensive_trajectory_analysis.png'}")
        
    def generate_improvement_roadmap(self):
        """生成改进路线图"""
        roadmap = {
            "短期改进 (1-2周)": [
                "实施多模态预测机制",
                "添加物理约束到损失函数",
                "优化数据预处理流程",
                "集成Patch级别的预测"
            ],
            "中期优化 (1个月)": [
                "实现三重注意力机制",
                "构建相对时空表示",
                "开发行为分类器",
                "集成环境感知模块"
            ],
            "长期目标 (2-3个月)": [
                "完整的BehaviorGPT架构",
                "大规模数据训练",
                "实时仿真优化",
                "多智能体交互建模"
            ]
        }
        
        print("\n🗺️ **改进路线图**:")
        for phase, tasks in roadmap.items():
            print(f"\n**{phase}**:")
            for i, task in enumerate(tasks, 1):
                print(f"  {i}. {task}")
        
        return roadmap

def main():
    """主函数"""
    analyzer = TrajectoryAnalyzer()
    
    # 加载真实轨迹
    real_trajectory_loaded = analyzer.load_real_trajectory(
        "trajectory_generator/data/trajectories/trajectory_1.csv"
    )
    
    if not real_trajectory_loaded:
        logger.warning("无法加载真实轨迹数据")
    
    # 加载仿真结果
    simulation_dirs = [
        "results/acceleration_simulation_1",
        "results/acceleration_simulation_2", 
        "results/acceleration_simulation_3",
        "results/improved_acceleration_simulation"
    ]
    
    analyzer.load_simulation_results(simulation_dirs)
    
    # 生成诊断报告
    patterns = analyzer.generate_problem_diagnosis()
    
    # 创建可视化
    analyzer.create_comprehensive_visualization()
    
    # 生成改进路线图
    roadmap = analyzer.generate_improvement_roadmap()
    
    # 保存分析结果
    analysis_report = {
        'patterns': patterns,
        'roadmap': roadmap,
        'diagnosis': {
            'main_issues': [
                "行为多样性不足",
                "物理约束缺失", 
                "环境响应性差",
                "长期稳定性问题"
            ],
            'solutions': [
                "多模态行为建模",
                "Patch级别预测",
                "物理约束集成",
                "注意力机制优化"
            ]
        }
    }
    
    with open('ai_trajectory_system/comprehensive_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_report, f, ensure_ascii=False, indent=2)
    
    logger.info("综合分析完成！")
    print(f"\n📋 分析报告已保存: ai_trajectory_system/comprehensive_analysis_report.json")
    print(f"📊 可视化结果已保存: ai_trajectory_system/comprehensive_trajectory_analysis.png")

if __name__ == "__main__":
    main() 