nohup: 忽略输入
Starting resilient download process...
If the script appears to fail, it will automatically restart in 5 seconds.
This will continue until all files are successfully downloaded.
-----------------------------------------------------------------------
Base output directory: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/data

Overall Progress:   0%|          | 0/10 [00:00<?, ?it/s]
Overall Progress:   0%|          | 0/10 [00:00<?, ?it/s, Region: scottish_highlands]
Downloading DEM for scottish_highlands:   0%|          | 0/10 [00:00<?, ?it/s, Region: scottish_highlands]
Downloading DEM for scottish_highlands:  10%|█         | 1/10 [04:30<40:31, 270.12s/it, Region: scottish_highlands]
Downloading LANDCOVER for scottish_highlands:  10%|█         | 1/10 [04:30<40:31, 270.12s/it, Region: scottish_highlands]
Downloading LANDCOVER for scottish_highlands:  20%|██        | 2/10 [09:00<36:01, 270.25s/it, Region: scottish_highlands]
Downloading LANDCOVER for scottish_highlands:  20%|██        | 2/10 [09:00<36:01, 270.25s/it, Region: israel_palestine]  
Downloading DEM for israel_palestine:  20%|██        | 2/10 [09:00<36:01, 270.25s/it, Region: israel_palestine]        
Downloading LANDCOVER for israel_palestine:  30%|███       | 3/10 [09:00<31:31, 270.25s/it, Region: israel_palestine]
Downloading LANDCOVER for israel_palestine:  40%|████      | 4/10 [09:00<27:01, 270.25s/it, Region: kashmir]         
Downloading DEM for kashmir:  40%|████      | 4/10 [09:00<27:01, 270.25s/it, Region: kashmir]               
Downloading LANDCOVER for kashmir:  50%|█████     | 5/10 [09:00<22:31, 270.25s/it, Region: kashmir]
Downloading LANDCOVER for kashmir:  60%|██████    | 6/10 [09:00<18:00, 270.25s/it, Region: gulf_war_kuwait]
Downloading DEM for gulf_war_kuwait:  60%|██████    | 6/10 [09:00<18:00, 270.25s/it, Region: gulf_war_kuwait]
Downloading LANDCOVER for gulf_war_kuwait:  70%|███████   | 7/10 [09:00<13:30, 270.25s/it, Region: gulf_war_kuwait]
Downloading LANDCOVER for gulf_war_kuwait:  80%|████████  | 8/10 [09:00<09:00, 270.25s/it, Region: donbas_ukraine_russia]
Downloading DEM for donbas_ukraine_russia:  80%|████████  | 8/10 [09:00<09:00, 270.25s/it, Region: donbas_ukraine_russia]
Downloading LANDCOVER for donbas_ukraine_russia:  90%|█████████ | 9/10 [09:00<04:30, 270.25s/it, Region: donbas_ukraine_russia]
Downloading LANDCOVER for donbas_ukraine_russia: 100%|██████████| 10/10 [09:00<00:00, 54.05s/it, Region: donbas_ukraine_russia]

  - ERROR downloading DEM for scottish_highlands.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: CURL error: Failed to connect to copernicus-dem-30m.s3.eu-central-1.amazonaws.com port 443 after 134949 ms: Couldn't connect to server

  - ERROR downloading LANDCOVER for scottish_highlands.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: CURL error: Failed to connect to esa-worldcover.s3.eu-central-1.amazonaws.com port 443 after 135169 ms: Couldn't connect to server

  - ERROR downloading DEM for israel_palestine.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for israel_palestine.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading DEM for kashmir.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for kashmir.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading DEM for gulf_war_kuwait.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for gulf_war_kuwait.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading DEM for donbas_ukraine_russia.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for donbas_ukraine_russia.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

One or more downloads failed. The script will be retried automatically.
-----------------------------------------------------------------------
Download script failed. Retrying in 5 seconds... (Press Ctrl+C to cancel)
-----------------------------------------------------------------------
Base output directory: /home/<USER>/data/Sucess_or_Die/ai_agent_generation/data

Overall Progress:   0%|          | 0/10 [00:00<?, ?it/s]
Overall Progress:   0%|          | 0/10 [00:00<?, ?it/s, Region: scottish_highlands]
Downloading DEM for scottish_highlands:   0%|          | 0/10 [00:00<?, ?it/s, Region: scottish_highlands]
Downloading DEM for scottish_highlands:  10%|█         | 1/10 [04:28<40:17, 268.59s/it, Region: scottish_highlands]
Downloading LANDCOVER for scottish_highlands:  10%|█         | 1/10 [04:28<40:17, 268.59s/it, Region: scottish_highlands]
Downloading LANDCOVER for scottish_highlands:  20%|██        | 2/10 [08:58<35:56, 269.62s/it, Region: scottish_highlands]
Downloading LANDCOVER for scottish_highlands:  20%|██        | 2/10 [08:58<35:56, 269.62s/it, Region: israel_palestine]  
Downloading DEM for israel_palestine:  20%|██        | 2/10 [08:58<35:56, 269.62s/it, Region: israel_palestine]        
Downloading LANDCOVER for israel_palestine:  30%|███       | 3/10 [08:58<31:27, 269.62s/it, Region: israel_palestine]
Downloading LANDCOVER for israel_palestine:  40%|████      | 4/10 [08:58<26:57, 269.62s/it, Region: kashmir]         
Downloading DEM for kashmir:  40%|████      | 4/10 [08:58<26:57, 269.62s/it, Region: kashmir]               
Downloading LANDCOVER for kashmir:  50%|█████     | 5/10 [08:58<22:28, 269.62s/it, Region: kashmir]
Downloading LANDCOVER for kashmir:  60%|██████    | 6/10 [08:58<17:58, 269.62s/it, Region: gulf_war_kuwait]
Downloading DEM for gulf_war_kuwait:  60%|██████    | 6/10 [08:58<17:58, 269.62s/it, Region: gulf_war_kuwait]
Downloading LANDCOVER for gulf_war_kuwait:  70%|███████   | 7/10 [08:58<13:28, 269.62s/it, Region: gulf_war_kuwait]
Downloading LANDCOVER for gulf_war_kuwait:  80%|████████  | 8/10 [08:58<08:59, 269.62s/it, Region: donbas_ukraine_russia]
Downloading DEM for donbas_ukraine_russia:  80%|████████  | 8/10 [08:58<08:59, 269.62s/it, Region: donbas_ukraine_russia]
Downloading LANDCOVER for donbas_ukraine_russia:  90%|█████████ | 9/10 [08:58<04:29, 269.62s/it, Region: donbas_ukraine_russia]
Downloading LANDCOVER for donbas_ukraine_russia: 100%|██████████| 10/10 [08:58<00:00, 53.89s/it, Region: donbas_ukraine_russia]

  - ERROR downloading DEM for scottish_highlands.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: CURL error: Failed to connect to copernicus-dem-30m.s3.eu-central-1.amazonaws.com port 443 after 133416 ms: Couldn't connect to server

  - ERROR downloading LANDCOVER for scottish_highlands.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: CURL error: Failed to connect to esa-worldcover.s3.eu-central-1.amazonaws.com port 443 after 135168 ms: Couldn't connect to server

  - ERROR downloading DEM for israel_palestine.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for israel_palestine.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading DEM for kashmir.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for kashmir.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading DEM for gulf_war_kuwait.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for gulf_war_kuwait.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading DEM for donbas_ukraine_russia.
  - URL: https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt
  - Error details: '/vsicurl/https://copernicus-dem-30m.s3.eu-central-1.amazonaws.com/copernicus-dem-30m.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

  - ERROR downloading LANDCOVER for donbas_ukraine_russia.
  - URL: https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt
  - Error details: '/vsicurl/https://esa-worldcover.s3.eu-central-1.amazonaws.com/v2/2021/vrt/ESA_WorldCover_10m_2021_v2_mosaic.vrt' does not exist in the file system, and is not recognized as a supported dataset name.

One or more downloads failed. The script will be retried automatically.
